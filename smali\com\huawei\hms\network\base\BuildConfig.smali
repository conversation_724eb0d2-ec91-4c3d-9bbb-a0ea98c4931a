.class public final Lcom/huawei/hms/network/base/BuildConfig;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final APPLICATION_ID:Ljava/lang/String; = "com.huawei.hms.network.base"
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final BUILDTIME:Ljava/lang/String; = "2023-06-27"

.field public static final BUILD_TYPE:Ljava/lang/String; = "release"

.field public static final DEBUG:Z = false

.field public static final FLAVOR:Ljava/lang/String; = ""

.field public static final LIBRARY_PACKAGE_NAME:Ljava/lang/String; = "com.huawei.hms.network.base"

.field public static final VERSION_CODE:I = 0x42c2a64

.field public static final VERSION_NAME:Ljava/lang/String; = "7.0.3.300"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, <PERSON><PERSON><PERSON>/lang/Object;-><init>()V

    return-void
.end method
