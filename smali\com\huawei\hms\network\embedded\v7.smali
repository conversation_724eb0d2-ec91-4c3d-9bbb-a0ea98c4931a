.class public interface abstract Lcom/huawei/hms/network/embedded/v7;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/network/embedded/v7$a;
    }
.end annotation


# virtual methods
.method public abstract a()J
.end method

.method public abstract a(ILjava/lang/String;)Z
    .param p2    # Ljava/lang/String;
        .annotation runtime Ljavax/annotation/Nullable;
        .end annotation
    .end param
.end method

.method public abstract b(Lcom/huawei/hms/network/embedded/ab;)Z
.end method

.method public abstract b(Ljava/lang/String;)Z
.end method

.method public abstract cancel()V
.end method

.method public abstract request()Lcom/huawei/hms/network/embedded/p7;
.end method
