.class public Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;
.super Ljava/lang/Object;
.source "ML3DFaceAnalyzerSetting.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Factory"
.end annotation


# instance fields
.field private face3dEnabled:Z

.field private isTracingAllowed:Z

.field private performanceType:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    .line 2
    iput v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;->performanceType:I

    .line 4
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;->face3dEnabled:Z

    const/4 v0, 0x0

    .line 5
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;->isTracingAllowed:Z

    return-void
.end method


# virtual methods
.method public create()Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;
    .locals 4

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;->performanceType:I

    iget-boolean v2, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;->face3dEnabled:Z

    iget-boolean v3, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;->isTracingAllowed:Z

    invoke-direct {v0, v1, v2, v3}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;-><init>(IZZ)V

    return-object v0
.end method

.method public setPerformanceType(I)Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;->performanceType:I

    return-object p0
.end method

.method public setTracingAllowed(Z)Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;->isTracingAllowed:Z

    return-object p0
.end method
