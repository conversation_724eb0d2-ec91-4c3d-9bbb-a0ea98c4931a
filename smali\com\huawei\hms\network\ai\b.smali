.class public Lcom/huawei/hms/network/ai/b;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final A:I = 0x1

.field public static final B:I = 0x2

.field public static final C:I = 0x3

.field public static final D:Ljava/lang/String; = "ping"

.field public static final E:Ljava/lang/String; = "pingStatus"

.field public static final F:Ljava/lang/String; = "mnc"

.field public static final G:Ljava/lang/String; = "domain"

.field public static final H:Ljava/lang/String; = "businessPing"

.field public static final I:Ljava/lang/String; = "wifi_signal_strength"

.field public static final J:Ljava/lang/String; = "mobile_signal_strength"

.field public static final K:Ljava/lang/String; = "networkChange"

.field public static final L:Ljava/lang/String; = "pingIntervalList"

.field public static final M:Ljava/lang/String; = "networkType"

.field public static final N:Ljava/lang/String; = "isSuccess"

.field public static final O:Ljava/lang/String; = "isActive"

.field public static final P:Ljava/lang/String; = "firstNetworkType"

.field public static final Q:Ljava/lang/String; = "continuePing"

.field public static final R:Ljava/lang/String; = "continueTimes"

.field public static final S:Ljava/lang/String; = "delayPing"

.field public static final T:Ljava/lang/String; = "delayTimes"

.field public static final a:Ljava/lang/String; = "init.model"

.field public static final b:Ljava/lang/String; = "domainRelation.model"

.field public static final c:Ljava/lang/String; = "event.model"

.field public static final d:Ljava/lang/String; = "ipsort.model"

.field public static final e:Ljava/lang/String; = "connecttimeout.model"

.field public static final f:Ljava/lang/String; = "ping.model"

.field public static final g:Ljava/lang/String; = "error_url"

.field public static final h:I = 0x0

.field public static final i:I = 0x1

.field public static final j:Ljava/lang/String; = "h2"

.field public static final k:Ljava/lang/String; = "http/1.1"

.field public static final l:I = 0x8

.field public static final m:I = 0x12

.field public static final n:Ljava/lang/String; = "0"

.field public static final o:Ljava/lang/String; = "1"

.field public static final p:Ljava/lang/String; = "2"

.field public static final q:Ljava/lang/String; = "3"

.field public static final r:Ljava/lang/String; = "2sModel"

.field public static final s:Ljava/lang/String; = "4sModel"

.field public static final t:Ljava/lang/String; = "failModel"

.field public static final u:I = 0x0

.field public static final v:I = 0x1

.field public static final w:I = 0x1

.field public static final x:I = 0x2

.field public static final y:I = 0x0

.field public static final z:I = 0x1


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
