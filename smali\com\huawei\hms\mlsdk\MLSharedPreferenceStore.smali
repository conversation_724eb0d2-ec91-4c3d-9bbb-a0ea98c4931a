.class public final Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;
.super Ljava/lang/Object;
.source "MLSharedPreferenceStore.java"


# static fields
.field private static storeMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private final application:Lcom/huawei/hms/mlsdk/common/MLApplication;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->storeMap:Ljava/util/Map;

    return-void
.end method

.method private constructor <init>(Lcom/huawei/hms/mlsdk/common/MLApplication;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    return-void
.end method

.method public static getAppStore(Lcom/huawei/hms/mlsdk/common/MLApplication;)Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;
    .locals 4

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getUniqueKey()Ljava/lang/String;

    move-result-object v0

    .line 2
    sget-object v1, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->storeMap:Ljava/util/Map;

    monitor-enter v1

    .line 3
    :try_start_0
    sget-object v2, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->storeMap:Ljava/util/Map;

    invoke-interface {v2, v0}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v2

    if-nez v2, :cond_0

    .line 4
    sget-object v2, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->storeMap:Ljava/util/Map;

    new-instance v3, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;

    invoke-direct {v3, p0}, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;)V

    invoke-interface {v2, v0, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 6
    :cond_0
    sget-object p0, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->storeMap:Ljava/util/Map;

    invoke-interface {p0, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;

    monitor-exit v1

    return-object p0

    :catchall_0
    move-exception p0

    .line 7
    monitor-exit v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p0
.end method


# virtual methods
.method public final declared-synchronized isStatisticsAllowed()Z
    .locals 6

    monitor-enter p0

    .line 1
    :try_start_0
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v0

    const-string v1, "com.huawei.hms.mlsdk"

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v0

    sget-object v1, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const/4 v3, 0x1

    new-array v4, v3, [Ljava/lang/Object;

    iget-object v5, p0, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    .line 2
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getUniqueKey()Ljava/lang/String;

    move-result-object v5

    aput-object v5, v4, v2

    const-string v2, "statistics allowed %s"

    invoke-static {v1, v2, v4}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    .line 3
    invoke-interface {v0, v1, v3}, Landroid/content/SharedPreferences;->getBoolean(Ljava/lang/String;Z)Z

    move-result v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public final declared-synchronized setStatisticsAllowed(Z)V
    .locals 5

    monitor-enter p0

    .line 1
    :try_start_0
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v0

    const-string v1, "com.huawei.hms.mlsdk"

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v0

    invoke-interface {v0}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v0

    sget-object v1, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const/4 v3, 0x1

    new-array v3, v3, [Ljava/lang/Object;

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    .line 2
    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getUniqueKey()Ljava/lang/String;

    move-result-object v4

    aput-object v4, v3, v2

    const-string v2, "statistics allowed %s"

    invoke-static {v1, v2, v3}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    .line 3
    invoke-interface {v0, v1, p1}, Landroid/content/SharedPreferences$Editor;->putBoolean(Ljava/lang/String;Z)Landroid/content/SharedPreferences$Editor;

    move-result-object p1

    .line 4
    invoke-interface {p1}, Landroid/content/SharedPreferences$Editor;->apply()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method
