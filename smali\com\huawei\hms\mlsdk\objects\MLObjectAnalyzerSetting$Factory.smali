.class public Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;
.super Ljava/lang/Object;
.source "MLObjectAnalyzerSetting.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Factory"
.end annotation


# instance fields
.field private analyzerType:I

.field private isClassificationAllowed:Z

.field private isMultipleResultsAllowed:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    .line 2
    iput v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;->analyzerType:I

    const/4 v0, 0x0

    .line 4
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;->isMultipleResultsAllowed:Z

    .line 6
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;->isClassificationAllowed:Z

    return-void
.end method


# virtual methods
.method public allowClassification()Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;
    .locals 1

    const/4 v0, 0x1

    .line 1
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;->isClassificationAllowed:Z

    return-object p0
.end method

.method public allowMultiResults()Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;
    .locals 1

    const/4 v0, 0x1

    .line 1
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;->isMultipleResultsAllowed:Z

    return-object p0
.end method

.method public create()Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;
    .locals 5

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    iget v1, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;->analyzerType:I

    iget-boolean v2, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;->isMultipleResultsAllowed:Z

    iget-boolean v3, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;->isClassificationAllowed:Z

    const/4 v4, 0x0

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;-><init>(IZZLcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$1;)V

    return-object v0
.end method

.method public setAnalyzerType(I)Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;->analyzerType:I

    return-object p0
.end method
