.class public Lcom/huawei/hms/network/embedded/y$b;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/network/embedded/y;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation


# static fields
.field public static final a:Ljava/lang/String; = "trigger_type"

.field public static final b:Ljava/lang/String; = "request_domain"

.field public static final c:Ljava/lang/String; = "error_code"

.field public static final d:I = 0x98e4a0

.field public static final e:I = 0x98e4a1

.field public static final f:Ljava/lang/String; = "dns_subtype"

.field public static final g:Ljava/lang/String; = "site_detect"

.field public static final h:Ljava/lang/String; = "dns_change"

.field public static final i:Ljava/lang/String; = "protocol_impl"

.field public static final j:Ljava/lang/String; = "okhttp"

.field public static final k:Ljava/lang/String; = "tcpconn_time"

.field public static final l:Ljava/lang/String; = "ssl_time"

.field public static final m:Ljava/lang/String; = "connect_time"

.field public static final n:Ljava/lang/String; = "dns_request"

.field public static final o:Ljava/lang/String; = "site_detect_threshold"

.field public static final p:Ljava/lang/String; = "server_ip"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
