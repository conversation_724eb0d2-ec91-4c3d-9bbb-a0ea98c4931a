.class public Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;
.super Ljava/lang/Object;
.source "MLFaceAnalyzerSetting.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$KeyPointType;,
        Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$ShapeType;,
        Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$FeatureType;,
        Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$PerformanceType;,
        Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
    }
.end annotation


# static fields
.field public static final MODE_TRACING_FAST:I = 0x1

.field public static final MODE_TRACING_ROBUST:I = 0x2

.field public static final TYPE_FEATURES:I = 0x1

.field public static final TYPE_FEATURE_AGE:I = 0x100

.field public static final TYPE_FEATURE_BEARD:I = 0x20

.field public static final TYPE_FEATURE_EMOTION:I = 0x4

.field public static final TYPE_FEATURE_EYEGLASS:I = 0x8

.field public static final TYPE_FEATURE_GENDAR:I = 0x80

.field public static final TYPE_FEATURE_HAT:I = 0x10

.field public static final TYPE_FEATURE_OPENCLOSEEYE:I = 0x40

.field public static final TYPE_KEYPOINTS:I = 0x1

.field public static final TYPE_PRECISION:I = 0x1

.field public static final TYPE_SHAPES:I = 0x2

.field public static final TYPE_SPEED:I = 0x2

.field public static final TYPE_UNSUPPORT_FEATURES:I = 0x2

.field public static final TYPE_UNSUPPORT_KEYPOINTS:I = 0x0

.field public static final TYPE_UNSUPPORT_SHAPES:I = 0x3


# instance fields
.field private final featureType:I

.field private final isMaxSizeFaceOnly:Z

.field private final isPoseDisabled:Z

.field private final isTracingAllowed:Z

.field private final keyPointType:I

.field private final minFaceProportion:F

.field private final performanceType:I

.field private final shapeType:I

.field private final tracingMode:I


# direct methods
.method public constructor <init>(IIIIZZF)V
    .locals 10

    const/4 v5, 0x0

    const/4 v7, 0x2

    move-object v0, p0

    move v1, p1

    move v2, p2

    move v3, p3

    move v4, p4

    move v6, p5

    move/from16 v8, p6

    move/from16 v9, p7

    .line 1
    invoke-direct/range {v0 .. v9}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;-><init>(IIIIZZIZF)V

    return-void
.end method

.method public constructor <init>(IIIIZZIZF)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->keyPointType:I

    .line 5
    iput p2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->shapeType:I

    .line 6
    iput p3, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->featureType:I

    .line 7
    iput p4, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->performanceType:I

    .line 8
    iput-boolean p5, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isPoseDisabled:Z

    .line 9
    iput-boolean p6, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isTracingAllowed:Z

    .line 10
    iput p7, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->tracingMode:I

    .line 11
    iput-boolean p8, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isMaxSizeFaceOnly:Z

    .line 12
    iput p9, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->minFaceProportion:F

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 4
    :cond_1
    check-cast p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    .line 5
    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->minFaceProportion:F

    invoke-static {v1}, Ljava/lang/Float;->floatToIntBits(F)I

    move-result v1

    iget v3, p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->minFaceProportion:F

    invoke-static {v3}, Ljava/lang/Float;->floatToIntBits(F)I

    move-result v3

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->keyPointType:I

    iget v3, p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->keyPointType:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->shapeType:I

    iget v3, p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->shapeType:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->featureType:I

    iget v3, p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->featureType:I

    if-ne v1, v3, :cond_2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isPoseDisabled:Z

    iget-boolean v3, p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isPoseDisabled:Z

    if-ne v1, v3, :cond_2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isTracingAllowed:Z

    iget-boolean v3, p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isTracingAllowed:Z

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->tracingMode:I

    iget v3, p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->tracingMode:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->performanceType:I

    iget v3, p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->performanceType:I

    if-ne v1, v3, :cond_2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isMaxSizeFaceOnly:Z

    iget-boolean p1, p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isMaxSizeFaceOnly:Z

    if-ne v1, p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public getFeatureType()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->featureType:I

    return v0
.end method

.method public getKeyPointType()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->keyPointType:I

    return v0
.end method

.method public getMinFaceProportion()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->minFaceProportion:F

    return v0
.end method

.method public getPerformanceType()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->performanceType:I

    return v0
.end method

.method public getShapeType()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->shapeType:I

    return v0
.end method

.method public getTracingMode()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->tracingMode:I

    return v0
.end method

.method public hashCode()I
    .locals 3

    const/16 v0, 0x9

    new-array v0, v0, [Ljava/lang/Object;

    .line 1
    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->minFaceProportion:F

    .line 2
    invoke-static {v1}, Ljava/lang/Float;->floatToIntBits(F)I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->keyPointType:I

    .line 3
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->shapeType:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->featureType:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x3

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isPoseDisabled:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x4

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isTracingAllowed:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x5

    aput-object v1, v0, v2

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->tracingMode:I

    .line 4
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x6

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isMaxSizeFaceOnly:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x7

    aput-object v1, v0, v2

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->performanceType:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/16 v2, 0x8

    aput-object v1, v0, v2

    .line 5
    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    return v0
.end method

.method public isMaxSizeFaceOnly()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isMaxSizeFaceOnly:Z

    return v0
.end method

.method public isPoseDisabled()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isPoseDisabled:Z

    return v0
.end method

.method public isTracingAllowed()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isTracingAllowed:Z

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 1
    invoke-static {p0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->toStringHelper(Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->keyPointType:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "keyPointType"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->shapeType:I

    .line 2
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "shapeMode"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->featureType:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "featureType"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->performanceType:I

    .line 3
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "performanceType"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isPoseDisabled:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v2, "isPoseDisabled"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isTracingAllowed:Z

    .line 4
    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v2, "isTracingAllowed"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->tracingMode:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "tracingMode"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->minFaceProportion:F

    .line 5
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "minFaceProportion"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isMaxSizeFaceOnly:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v2, "isMaxSizeFaceOnly"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    .line 6
    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
