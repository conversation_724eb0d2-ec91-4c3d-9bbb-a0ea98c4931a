.class public Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;
.super Ljava/lang/Object;
.source "ML3DFaceAnalyzerSetting.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$PerformanceType;,
        Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;
    }
.end annotation


# static fields
.field public static final TYPE_PRECISION:I = 0x2

.field public static final TYPE_SPEED:I = 0x1


# instance fields
.field private final face3dEnabled:Z

.field private final isTracingAllowed:Z

.field private final performanceType:I


# direct methods
.method public constructor <init>(IZ)V
    .locals 1

    const/4 v0, 0x1

    .line 1
    invoke-direct {p0, p1, v0, p2}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;-><init>(IZZ)V

    return-void
.end method

.method public constructor <init>(IZZ)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->performanceType:I

    .line 4
    iput-boolean p2, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->face3dEnabled:Z

    .line 5
    iput-boolean p3, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->isTracingAllowed:Z

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 4
    :cond_1
    check-cast p1, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;

    .line 5
    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->face3dEnabled:Z

    iget-boolean v3, p1, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->face3dEnabled:Z

    if-ne v1, v3, :cond_2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->isTracingAllowed:Z

    iget-boolean v3, p1, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->isTracingAllowed:Z

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->performanceType:I

    iget p1, p1, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->performanceType:I

    if-ne v1, p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public getPerformanceType()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->performanceType:I

    return v0
.end method

.method public hashCode()I
    .locals 3

    const/4 v0, 0x3

    new-array v0, v0, [Ljava/lang/Object;

    .line 1
    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->face3dEnabled:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->isTracingAllowed:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->performanceType:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    invoke-virtual {v0}, Ljava/lang/Object;->hashCode()I

    move-result v0

    return v0
.end method

.method public isTracingAllowed()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->isTracingAllowed:Z

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 1
    invoke-static {p0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->toStringHelper(Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->performanceType:I

    .line 2
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "performanceType"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->face3dEnabled:Z

    .line 3
    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v2, "face3dEnabled"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->isTracingAllowed:Z

    .line 4
    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v2, "isTracingAllowed"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
