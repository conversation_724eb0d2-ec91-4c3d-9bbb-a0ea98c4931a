<?xml version="1.0" encoding="utf-8" standalone="no"?><manifest xmlns:android="http://schemas.android.com/apk/res/android" android:compileSdkVersion="33" android:compileSdkVersionCodename="13" package="com.mxz.hyx" platformBuildVersionCode="33" platformBuildVersionName="13">
    <queries>
        <intent>
            <action android:name="android.intent.action.DIAL"/>
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW"/>
        </intent>
        <intent>
            <action android:name="android.intent.action.MAIN"/>
        </intent>
        <intent>
            <action android:name="com.huawei.hms.core.aidlservice"/>
        </intent>
        <intent>
            <action android:name="com.huawei.hms.core"/>
        </intent>
        <package android:name="com.huawei.hff"/>
        <package android:name="com.huawei.hms"/>
        <package android:name="com.huawei.hwid"/>
        <package android:name="com.huawei.hwid.tv"/>
        <package android:name="com.huawei.works"/>
    </queries>
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD"/>
    <uses-permission android:name="android.permission.GET_TASKS"/>
    <permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" android:protectionLevel="signature"/>
    <permission android:name="android.permission.GET_TOP_ACTIVITY_INFO" android:protectionLevel="signature"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE"/>
    <uses-permission android:name="android.permission.REORDER_TASKS"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_ADDED"/>
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_CHANGED"/>
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_INSTALL"/>
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_REPLACED"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.RESTART_PACKAGES"/>
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW"/>
    <uses-permission android:name="android.permission.FLASHLIGHT"/>
    <uses-permission android:name="android.permission.SET_WALLPAPER"/>
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"/>
    <uses-permission android:name="android.permission.INJECT_EVENTS"/>
    <uses-permission android:name="android.permission.ACCESS_CLIPBOARD"/>
    <uses-permission-sdk-23 android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission-sdk-23 android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <permission android:name="com.mxz.hyx.permission.MIPUSH_RECEIVE" android:protectionLevel="signatureOrSystem"/>
    <uses-feature android:name="android.hardware.usb.accessory"/>
    <uses-permission android:name="android.permission.USB_PERMISSION"/>
    <uses-feature android:name="android.hardware.usb.host"/>
    <uses-permission android:maxSdkVersion="30" android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:maxSdkVersion="30" android:name="android.permission.BLUETOOTH_ADMIN"/>
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE"/>
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="true"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM"/>
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS"/>
    <uses-permission-sdk-23 android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission-sdk-23 android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <application android:allowBackup="false" android:appComponentFactory="androidx.core.app.CoreComponentFactory" android:extractNativeLibs="true" android:icon="@mipmap/icon" android:label="@string/app_name" android:largeHeap="true" android:name="com.mxz.hyx.activitys.MyApplication" android:networkSecurityConfig="@xml/network_security_config" android:screenOrientation="portrait" android:supportsRtl="true" android:theme="@style/AppTheme" android:usesCleartextTraffic="true" android:windowSoftInputMode="adjustResize|stateHidden">
        <uses-library android:name="org.apache.http.legacy" android:required="false"/>
        <meta-data android:name="UMENG_APPKEY" android:value="64e197e98efadc41dcc6a9b2"/>
        <meta-data android:name="UMENG_CHANNEL" android:value="suoyou"/>
        <activity android:exported="true" android:label="@string/app_name" android:launchMode="singleTop" android:name="com.mxz.hyx.activitys.SplashActivity" android:screenOrientation="portrait" android:theme="@style/BaseAppTheme" android:windowSoftInputMode="adjustResize|stateHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity android:configChanges="keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize" android:name="com.mxz.hyx.activitys.MainNewActivity" android:theme="@style/mainTheme">
            <meta-data android:name="android.support.PARENT_ACTIVITY" android:value="com.mxz.hyx.activitys.MainNewActivity"/>
        </activity>
        <activity android:configChanges="keyboardHidden|orientation|screenSize" android:exported="true" android:label="a" android:name="com.mxz.hyx.activitys.GetZipActivity" android:theme="@style/mainTheme">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="file"/>
                <data android:scheme="content"/>
                <data android:scheme="http"/>
                <data android:scheme="https"/>
                <data android:mimeType="*/*"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:mimeType="*/*"/>
            </intent-filter>
        </activity>
        <receiver android:exported="false" android:launchMode="singleTask" android:name="com.mxz.hyx.activitys.NotificationClickReceiver">
            <intent-filter android:priority="20">
                <action android:name="com.mxz.hyx"/>
            </intent-filter>
        </receiver>
        <activity android:name="com.mxz.hyx.activitys.QuestionActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.activitys.LoggerActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.activitys.ErrorLoggerActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.activitys.SettingActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.activitys.ParamSettingActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.floatwin.FloatActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.activitys.WorksActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.activitys.WorkInfoActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.activitys.WorkKeyInfoActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.activitys.MyCodeActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.activitys.AboutMeActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.activitys.ImgGroupActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.activitys.ImgGroupInfoActivity" android:theme="@style/mainTheme"/>
        <activity android:name="com.mxz.hyx.activitys.SysVariableActivity" android:theme="@style/mainTheme"/>
        <activity android:excludeFromRecents="true" android:exported="true" android:name="com.mxz.hyx.activitys.ClipboardActivity" android:showOnLockScreen="true" android:taskAffinity="" android:theme="@style/TransparentNoAnimTheme" android:turnScreenOn="true"/>
        <service android:exported="false" android:foregroundServiceType="mediaProjection" android:name="com.mxz.hyx.services.MyScreenshotService"/>
        <service android:exported="false" android:name="com.mxz.hyx.services.RunJobService"/>
        <service android:exported="false" android:label="@string/app_name" android:name="com.mxz.hyx.services.MyImeService" android:permission="android.permission.BIND_INPUT_METHOD">
            <intent-filter>
                <action android:name="android.view.InputMethod"/>
            </intent-filter>
            <meta-data android:name="android.view.im" android:resource="@xml/method"/>
        </service>
        <service android:enabled="true" android:exported="true" android:label="@string/app_name" android:name="com.mxz.hyx.services.LiveWallpaperService" android:permission="android.permission.BIND_WALLPAPER">
            <intent-filter>
                <action android:name="android.service.wallpaper.WallpaperService"/>
            </intent-filter>
            <meta-data android:name="android.service.wallpaper" android:resource="@xml/live_wall_paper"/>
        </service>
        <receiver android:exported="false" android:name="com.mxz.hyx.receiver.PhoneCallReceiver">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.PHONE_STATE"/>
            </intent-filter>
        </receiver>
        <receiver android:exported="false" android:name="com.mxz.hyx.services.YourBroadcastReceiver"/>
        <receiver android:exported="false" android:name="com.mxz.hyx.receiver.ExampleAppWidgetProvider">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE"/>
            </intent-filter>
            <meta-data android:name="android.appwidget.provider" android:resource="@xml/example_appwidget_info"/>
        </receiver>
        <service android:label="dexopt" android:name="com.tencent.smtt.export.external.DexClassLoaderProviderService" android:process=":dexopt"/>
        <activity android:configChanges="keyboardHidden|locale|orientation|screenSize" android:name="com.tencent.bugly.beta.ui.BetaActivity" android:theme="@android:style/Theme.Translucent"/>
        <provider android:authorities="com.mxz.hyx.selfupdate.fileprovider" android:exported="false" android:grantUriPermissions="true" android:name="androidx.core.content.FileProvider">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/provider_paths"/>
        </provider>
        <provider android:authorities="com.mxz.hyx.fileProvider" android:exported="false" android:grantUriPermissions="true" android:name="com.mxz.hyx.util.ImageFileProvider">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/provider_paths"/>
        </provider>
        <service android:enabled="true" android:exported="false" android:label="@string/app_name" android:name="com.mxz.hyx.activitys.QiangHongBaoService" android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" android:priority="**********">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService"/>
            </intent-filter>
            <meta-data android:name="android.accessibilityservice" android:resource="@xml/qianghongbao_service_config"/>
        </service>
        <activity android:launchMode="singleTask" android:name="com.mylhyl.acp.AcpActivity" android:theme="@style/Theme.Transparent"/>
        <provider android:authorities="com.mxz.hyx.lifecycle-process" android:exported="false" android:multiprocess="true" android:name="androidx.lifecycle.ProcessLifecycleOwnerInitializer"/>
        <meta-data android:name="com.huawei.hms:ml-computer-voice-asr" android:value="ml-computer-voice-asr:3.14.1.300"/>
        <meta-data android:name="com.huawei.hms.min_api_level:ml-computer-vision:huawei_module_mlkit_ocr" android:value="7"/>
        <meta-data android:name="com.huawei.hms.client.service.name:ml-computer-vision-ocr" android:value="ml-computer-vision-ocr:3.11.0.301"/>
        <meta-data android:name="com.huawei.hms.client.service.name:ml-computer-vision-cloud" android:value="ml-computer-vision:3.11.0.301"/>
        <meta-data android:name="com.huawei.hms.client.service.name:ml-computer-vision-ocr-latin-model" android:value="ml-computer-vision-ocr-latin-model:3.11.0.301"/>
        <meta-data android:name="com.huawei.hms.client.service.name:ml-computer-vision-ocr-jk-model" android:value="ml-computer-vision-ocr-jk-model:3.11.0.301"/>
        <meta-data android:name="com.huawei.hms.client.service.name:ml-computer-vision-ocr-cn-model" android:value="ml-computer-vision-ocr-cn-model:3.11.0.301"/>
        <meta-data android:name="com.huawei.hms.client.service.name:ml-computer-vision-ocr-base" android:value="ml-computer-vision-ocr-base:3.11.0.301"/>
        <activity android:name="com.tencent.bugly.beta.ui.BetaActiveAlertActivity"/>
        <provider android:authorities="com.mxz.hyx.fileProvider" android:exported="false" android:grantUriPermissions="true" android:name="com.tencent.bugly.beta.utils.BuglyFileProvider">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/provider_paths"/>
        </provider>
        <service android:exported="false" android:name="com.tencent.bugly.beta.tinker.TinkerResultService" android:permission="android.permission.BIND_JOB_SERVICE"/>
        <meta-data android:name="com.huawei.hms:ml-computer-voice-asr-sdk" android:value="ml-computer-voice-asr-sdk:3.14.1.300"/>
        <meta-data android:name="com.huawei.hms.client.service.name:ml-computer-vision-base" android:value="ml-computer-vision-base:3.11.0.301"/>
        <provider android:authorities="com.mxz.hyx.MLInitializerProvider" android:exported="false" android:name="com.huawei.hms.mlsdk.common.provider.MLInitializerProvider"/>
        <meta-data android:name="com.huawei.hms.client.service.name:ml-computer-net" android:value="ml-computer-net:3.13.1.302"/>
        <meta-data android:name="networkservice_netdiag" android:value="com.huawei.hms.network.netdiag.NetDiagnosisService"/>
        <meta-data android:name="com.huawei.hms.min_api_level:null:huawei_module_quic_pro" android:value="-1"/>
        <meta-data android:name="com.huawei.hms.min_api_level:null:huawei_module_quic" android:value="-1"/>
        <meta-data android:name="networkservice_config" android:value="com.huawei.hms.network.conf.api.ConfigService"/>
        <meta-data android:name="networkservice_dns" android:value="com.huawei.hms.network.dns.DNSService"/>
        <service android:name="com.liulishuo.filedownloader.services.FileDownloadService$SharedMainProcessService"/>
        <service android:name="com.liulishuo.filedownloader.services.FileDownloadService$SeparateProcessService" android:process=":filedownloader"/>
        <meta-data android:name="com.huawei.hms.client.service.name:ml-computer-ha-inner" android:value="ml-computer-ha-inner:3.13.1.302"/>
        <meta-data android:name="com.huawei.hms.client.service.name:base" android:value="base:6.11.0.302"/>
        <meta-data android:name="com.huawei.hms.min_api_level:base:hmscore" android:value="1"/>
        <meta-data android:name="com.huawei.hms.client.service.name:ml-computer-dynamic" android:value="ml-computer-dynamic:3.11.0.301"/>
        <meta-data android:name="availableLoaded" android:value="yes"/>
        <provider android:authorities="com.mxz.hyx.AGCInitializeProvider" android:exported="false" android:name="com.huawei.agconnect.core.provider.AGConnectInitializeProvider"/>
        <service android:exported="false" android:name="com.huawei.agconnect.core.ServiceDiscovery"/>
        <meta-data android:name="com.huawei.hms.client.service.name:network-api" android:value="network-api:7.0.3.300"/>
        <meta-data android:name="networkservice_ai" android:value="com.huawei.hms.network.ai.AIPolicyService"/>
        <meta-data android:name="com.huawei.hms.client.service.name:ml-computer-vision-inner" android:value="ml-computer-vision-inner:3.11.0.301"/>
        <meta-data android:name="com.huawei.hms.client.service.name:dynamic-api" android:value="dynamic-api:1.0.24.300"/>
        <meta-data android:name="com.huawei.hms.min_api_level:dynamic-api:huawei_module_dynamicloader" android:value="10"/>
        <activity android:configChanges="fontScale|layoutDirection|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:excludeFromRecents="true" android:exported="false" android:hardwareAccelerated="true" android:name="com.huawei.hms.activity.BridgeActivity" android:screenOrientation="behind" android:theme="@style/Base_Translucent">
            <meta-data android:name="hwc-theme" android:value="androidhwext:style/Theme.Emui.Translucent"/>
        </activity>
        <activity android:configChanges="keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize" android:exported="false" android:name="com.huawei.hms.activity.EnableServiceActivity"/>
        <meta-data android:name="com.huawei.hms.client.appid" android:value="appid=104336079"/>
    </application>
</manifest>