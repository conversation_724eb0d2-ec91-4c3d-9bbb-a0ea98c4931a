.class public Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;
.super Ljava/lang/Object;
.source "RestClientRequestHeaders.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/internal/client/rest/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation


# instance fields
.field private a:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private b:Lcom/huawei/hms/mlsdk/internal/client/rest/c;


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a:Ljava/util/Map;

    const/4 v0, 0x0

    .line 3
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->b:Lcom/huawei/hms/mlsdk/internal/client/rest/c;

    .line 6
    new-instance v0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;

    invoke-static {}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getInstance()Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/huawei/hms/mlsdk/internal/client/rest/c;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;)V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->b:Lcom/huawei/hms/mlsdk/internal/client/rest/c;

    .line 7
    invoke-virtual {v0, p0}, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->a(Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;)Ljava/util/Map;

    move-result-object v0

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a:Ljava/util/Map;

    return-void
.end method

.method static synthetic a(Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;)Ljava/util/Map;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a:Ljava/util/Map;

    return-object p0
.end method


# virtual methods
.method public a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;
    .locals 1

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a:Ljava/util/Map;

    invoke-interface {v0, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object p0
.end method

.method public a()Lcom/huawei/hms/mlsdk/internal/client/rest/e;
    .locals 2

    .line 3
    new-instance v0, Lcom/huawei/hms/mlsdk/internal/client/rest/e;

    const/4 v1, 0x0

    invoke-direct {v0, p0, v1}, Lcom/huawei/hms/mlsdk/internal/client/rest/e;-><init>(Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;Lcom/huawei/hms/mlsdk/internal/client/rest/e$a;)V

    return-object v0
.end method
