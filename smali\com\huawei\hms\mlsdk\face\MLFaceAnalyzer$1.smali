.class Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$1;
.super Ljava/lang/Object;
.source "MLFaceAnalyzer.java"

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->asyncAnalyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Ljava/util/List<",
        "Lcom/huawei/hms/mlsdk/face/MLFace;",
        ">;>;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;

.field final synthetic val$mlframe:Lcom/huawei/hms/mlsdk/common/MLFrame;


# direct methods
.method constructor <init>(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;

    iput-object p2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$1;->val$mlframe:Lcom/huawei/hms/mlsdk/common/MLFrame;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$1;->call()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public call()Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/face/MLFace;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->isAvailable()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 3
    invoke-static {}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->getInstance()Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$1;->val$mlframe:Lcom/huawei/hms/mlsdk/common/MLFrame;

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;

    invoke-static {v2}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->access$000(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    move-result-object v2

    iget-object v3, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;

    invoke-static {v3}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->access$100(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v3

    invoke-virtual {v0, v1, v2, v3}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->detectFromRemote(Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;Lcom/huawei/hms/mlsdk/common/MLApplication;)Ljava/util/List;

    move-result-object v0

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 5
    :goto_0
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_1

    .line 6
    invoke-static {}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->access$200()Ljava/lang/String;

    move-result-object v1

    const-string v2, "asyncAnalyseFrame faceParcelList is empty!"

    invoke-static {v1, v2}, Lcom/huawei/hms/ml/common/base/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    :cond_1
    return-object v0
.end method
