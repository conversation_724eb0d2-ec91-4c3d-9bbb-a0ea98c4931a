.class public Lcom/huawei/hms/mlsdk/objects/MLObject;
.super Ljava/lang/Object;
.source "MLObject.java"


# static fields
.field public static final TYPE_FACE:I = 0x6

.field public static final TYPE_FOOD:I = 0x2

.field public static final TYPE_FURNITURE:I = 0x3

.field public static final TYPE_GOODS:I = 0x1

.field public static final TYPE_OTHER:I = 0x0

.field public static final TYPE_PLACE:I = 0x5

.field public static final TYPE_PLANT:I = 0x4


# instance fields
.field private border:Landroid/graphics/Rect;

.field private possibility:Ljava/lang/Float;

.field private tracingIdentity:Ljava/lang/Integer;

.field private type:I


# direct methods
.method public constructor <init>(Landroid/graphics/Rect;Ljava/lang/Integer;Ljava/lang/Float;I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Landroid/graphics/Rect;

    invoke-direct {v0, p1}, Landroid/graphics/Rect;-><init>(Landroid/graphics/Rect;)V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObject;->border:Landroid/graphics/Rect;

    .line 3
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/objects/MLObject;->tracingIdentity:Ljava/lang/Integer;

    .line 4
    iput-object p3, p0, Lcom/huawei/hms/mlsdk/objects/MLObject;->possibility:Ljava/lang/Float;

    .line 5
    iput p4, p0, Lcom/huawei/hms/mlsdk/objects/MLObject;->type:I

    return-void
.end method


# virtual methods
.method public getBorder()Landroid/graphics/Rect;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObject;->border:Landroid/graphics/Rect;

    return-object v0
.end method

.method public getTracingIdentity()Ljava/lang/Integer;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObject;->tracingIdentity:Ljava/lang/Integer;

    return-object v0
.end method

.method public getTypeIdentity()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObject;->type:I

    return v0
.end method

.method public getTypePossibility()Ljava/lang/Float;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObject;->possibility:Ljava/lang/Float;

    return-object v0
.end method

.method public setBorder(Landroid/graphics/Rect;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/objects/MLObject;->border:Landroid/graphics/Rect;

    return-void
.end method

.method public setTracingIdentity(Ljava/lang/Integer;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/objects/MLObject;->tracingIdentity:Ljava/lang/Integer;

    return-void
.end method

.method public setTypeIdentity(I)V
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/objects/MLObject;->type:I

    return-void
.end method

.method public setTypePossibility(Ljava/lang/Float;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/objects/MLObject;->possibility:Ljava/lang/Float;

    return-void
.end method
