.class public Lcom/huawei/hms/network/ai/e;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final d:Lcom/huawei/hms/network/ai/e;

.field public static final e:Ljava/lang/String; = "AIModel_Train"

.field public static final f:Ljava/lang/String; = "AIModel_Execute"

.field public static final g:I = 0x1

.field public static final h:I = 0x1

.field public static final i:I = 0x2

.field public static final j:I = 0x5

.field public static final k:I = 0x5

.field public static final l:I = 0x40


# instance fields
.field public a:Ljava/util/concurrent/RejectedExecutionHandler;

.field public b:Ljava/util/concurrent/ThreadPoolExecutor;

.field public c:Ljava/util/concurrent/ThreadPoolExecutor;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/huawei/hms/network/ai/e;

    invoke-direct {v0}, Lcom/huawei/hms/network/ai/e;-><init>()V

    sput-object v0, Lcom/huawei/hms/network/ai/e;->d:Lcom/huawei/hms/network/ai/e;

    return-void
.end method

.method public constructor <init>()V
    .locals 18

    move-object/from16 v0, p0

    invoke-direct/range {p0 .. p0}, Ljava/lang/Object;-><init>()V

    new-instance v1, Ljava/util/concurrent/ThreadPoolExecutor$DiscardOldestPolicy;

    invoke-direct {v1}, Ljava/util/concurrent/ThreadPoolExecutor$DiscardOldestPolicy;-><init>()V

    iput-object v1, v0, Lcom/huawei/hms/network/ai/e;->a:Ljava/util/concurrent/RejectedExecutionHandler;

    new-instance v1, Lcom/huawei/hms/framework/common/ThreadPoolExcutorEnhance;

    sget-object v6, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    new-instance v7, Ljava/util/concurrent/LinkedBlockingQueue;

    const/16 v2, 0x40

    invoke-direct {v7, v2}, Ljava/util/concurrent/LinkedBlockingQueue;-><init>(I)V

    const-string v2, "AIModel_Train"

    invoke-static {v2}, Lcom/huawei/hms/framework/common/ExecutorsUtils;->createThreadFactory(Ljava/lang/String;)Ljava/util/concurrent/ThreadFactory;

    move-result-object v8

    iget-object v9, v0, Lcom/huawei/hms/network/ai/e;->a:Ljava/util/concurrent/RejectedExecutionHandler;

    const/4 v3, 0x1

    const/4 v4, 0x1

    const/4 v5, 0x5

    move-object v2, v1

    invoke-direct/range {v2 .. v9}, Lcom/huawei/hms/framework/common/ThreadPoolExcutorEnhance;-><init>(IIILjava/util/concurrent/TimeUnit;Ljava/util/concurrent/LinkedBlockingQueue;Ljava/util/concurrent/ThreadFactory;Ljava/util/concurrent/RejectedExecutionHandler;)V

    iput-object v1, v0, Lcom/huawei/hms/network/ai/e;->b:Ljava/util/concurrent/ThreadPoolExecutor;

    new-instance v1, Lcom/huawei/hms/framework/common/ThreadPoolExcutorEnhance;

    sget-object v15, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    new-instance v16, Ljava/util/concurrent/LinkedBlockingQueue;

    invoke-direct/range {v16 .. v16}, Ljava/util/concurrent/LinkedBlockingQueue;-><init>()V

    const-string v2, "AIModel_Execute"

    invoke-static {v2}, Lcom/huawei/hms/framework/common/ExecutorsUtils;->createThreadFactory(Ljava/lang/String;)Ljava/util/concurrent/ThreadFactory;

    move-result-object v17

    const/4 v11, 0x2

    const/4 v12, 0x5

    const-wide/16 v13, 0x5

    move-object v10, v1

    invoke-direct/range {v10 .. v17}, Lcom/huawei/hms/framework/common/ThreadPoolExcutorEnhance;-><init>(IIJLjava/util/concurrent/TimeUnit;Ljava/util/concurrent/BlockingQueue;Ljava/util/concurrent/ThreadFactory;)V

    iput-object v1, v0, Lcom/huawei/hms/network/ai/e;->c:Ljava/util/concurrent/ThreadPoolExecutor;

    iget-object v1, v0, Lcom/huawei/hms/network/ai/e;->b:Ljava/util/concurrent/ThreadPoolExecutor;

    const/4 v2, 0x1

    invoke-virtual {v1, v2}, Ljava/util/concurrent/ThreadPoolExecutor;->allowCoreThreadTimeOut(Z)V

    iget-object v1, v0, Lcom/huawei/hms/network/ai/e;->c:Ljava/util/concurrent/ThreadPoolExecutor;

    invoke-virtual {v1, v2}, Ljava/util/concurrent/ThreadPoolExecutor;->allowCoreThreadTimeOut(Z)V

    return-void
.end method

.method public static a()Lcom/huawei/hms/network/ai/e;
    .locals 1

    sget-object v0, Lcom/huawei/hms/network/ai/e;->d:Lcom/huawei/hms/network/ai/e;

    return-object v0
.end method


# virtual methods
.method public a(Ljava/lang/Runnable;)V
    .locals 2

    :try_start_0
    iget-object v0, p0, Lcom/huawei/hms/network/ai/e;->b:Ljava/util/concurrent/ThreadPoolExecutor;

    new-instance v1, Lcom/huawei/hms/network/ai/k;

    invoke-direct {v1, p1}, Lcom/huawei/hms/network/ai/k;-><init>(Ljava/lang/Runnable;)V

    invoke-virtual {v0, v1}, Ljava/util/concurrent/ThreadPoolExecutor;->execute(Ljava/lang/Runnable;)V
    :try_end_0
    .catch Ljava/util/concurrent/RejectedExecutionException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    const-string p1, "AIexecute"

    const-string v0, "executeTrain meet RejectedExecutionException"

    invoke-static {p1, v0}, Lcom/huawei/hms/framework/common/Logger;->d(Ljava/lang/String;Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public b(Ljava/lang/Runnable;)V
    .locals 2

    :try_start_0
    iget-object v0, p0, Lcom/huawei/hms/network/ai/e;->c:Ljava/util/concurrent/ThreadPoolExecutor;

    new-instance v1, Lcom/huawei/hms/network/ai/k;

    invoke-direct {v1, p1}, Lcom/huawei/hms/network/ai/k;-><init>(Ljava/lang/Runnable;)V

    invoke-virtual {v0, v1}, Ljava/util/concurrent/ThreadPoolExecutor;->execute(Ljava/lang/Runnable;)V
    :try_end_0
    .catch Ljava/util/concurrent/RejectedExecutionException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    const-string p1, "AIexecute"

    const-string v0, "executeUrgent meet RejectedExecutionException"

    invoke-static {p1, v0}, Lcom/huawei/hms/framework/common/Logger;->d(Ljava/lang/String;Ljava/lang/Object;)V

    :goto_0
    return-void
.end method
