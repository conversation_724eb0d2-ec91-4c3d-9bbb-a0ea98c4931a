.class public abstract Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;
.super Ljava/lang/Object;
.source "AbstractAdapter.java"

# interfaces
.implements Lcom/huawei/hms/mlsdk/internal/client/adapter/IAdapter;


# instance fields
.field private hmsAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

.field private isAvailable:Z

.field private isModulePopup:Z

.field private moduleAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/ModuleAdapter;


# direct methods
.method protected constructor <init>()V
    .locals 1

    .line 22
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 18
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->isAvailable:Z

    .line 20
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->isModulePopup:Z

    .line 23
    new-instance v0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->hmsAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

    .line 24
    new-instance v0, Lcom/huawei/hms/mlsdk/internal/client/adapter/ModuleAdapter;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/ModuleAdapter;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->moduleAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/ModuleAdapter;

    return-void
.end method


# virtual methods
.method public getHmsAdapter()Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;
    .locals 1

    .line 83
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->hmsAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

    return-object v0
.end method

.method public abstract getMinHmsApkVersion()I
.end method

.method public getModuleAdapter()Lcom/huawei/hms/mlsdk/internal/client/adapter/ModuleAdapter;
    .locals 1

    .line 87
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->moduleAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/ModuleAdapter;

    return-object v0
.end method

.method public isAvailable(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)Z
    .locals 3

    .line 29
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->isAvailable:Z

    const/4 v1, 0x0

    if-nez v0, :cond_0

    invoke-static {}, Lcom/huawei/hms/mlsdk/dynamic/util/TimerAssist;->getInstance()Lcom/huawei/hms/mlsdk/dynamic/util/TimerAssist;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/dynamic/util/TimerAssist;->isLock()Z

    move-result v0

    if-eqz v0, :cond_0

    return v1

    .line 34
    :cond_0
    invoke-interface {p2}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->isLocalExisted()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 35
    invoke-interface {p2}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->isOperational()Z

    move-result p1

    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->isAvailable:Z

    goto :goto_0

    .line 39
    :cond_1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->hmsAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->getMinHmsApkVersion()I

    move-result v2

    invoke-virtual {v0, p1, v2}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isHmsAvailable(Landroid/content/Context;I)Z

    move-result p1

    if-nez p1, :cond_2

    .line 40
    iput-boolean v1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->isAvailable:Z

    goto :goto_0

    .line 44
    :cond_2
    invoke-interface {p2}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->isOperational()Z

    move-result p1

    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->isAvailable:Z

    .line 47
    :goto_0
    iget-boolean p1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->isAvailable:Z

    if-nez p1, :cond_3

    iget-object p1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->hmsAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isHmsInstalled()Z

    move-result p1

    if-eqz p1, :cond_3

    .line 48
    iget-boolean p1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->isModulePopup:Z

    if-nez p1, :cond_3

    iget-object p1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->moduleAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/ModuleAdapter;

    invoke-interface {p2}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getLoadExceptionBundle()Landroid/os/Bundle;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/huawei/hms/mlsdk/internal/client/adapter/ModuleAdapter;->notifyDownloadModuleIfNeeded(Landroid/os/Bundle;)Z

    move-result p1

    if-eqz p1, :cond_3

    const/4 p1, 0x1

    .line 49
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->isModulePopup:Z

    .line 53
    :cond_3
    iget-boolean p1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->isAvailable:Z

    return p1
.end method

.method public declared-synchronized notifyDownloadIfNeeded(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)V
    .locals 2

    monitor-enter p0

    const/4 v0, 0x0

    .line 59
    :try_start_0
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->isModulePopup:Z

    .line 62
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->hmsAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->resetStatus()V

    .line 64
    invoke-interface {p2}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->isLocalExisted()Z

    move-result v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    .line 65
    monitor-exit p0

    return-void

    .line 68
    :cond_0
    :try_start_1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->hmsAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->getMinHmsApkVersion()I

    move-result v1

    invoke-virtual {v0, p1, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isHmsAvailable(Landroid/content/Context;I)Z

    move-result v0

    if-nez v0, :cond_1

    .line 69
    iget-object p2, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->hmsAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->getMinHmsApkVersion()I

    move-result v0

    invoke-virtual {p2, p1, v0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->notifyDownloadHmsIfNeeded(Landroid/content/Context;I)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 70
    monitor-exit p0

    return-void

    .line 73
    :cond_1
    :try_start_2
    iget-object p1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->moduleAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/ModuleAdapter;

    invoke-interface {p2}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getLoadExceptionBundle()Landroid/os/Bundle;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/huawei/hms/mlsdk/internal/client/adapter/ModuleAdapter;->notifyDownloadModuleIfNeeded(Landroid/os/Bundle;)Z
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 74
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public release(Landroid/content/Context;)V
    .locals 1

    .line 78
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->hmsAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->release(Landroid/content/Context;)V

    .line 79
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;->moduleAdapter:Lcom/huawei/hms/mlsdk/internal/client/adapter/ModuleAdapter;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/ModuleAdapter;->release(Landroid/content/Context;)V

    return-void
.end method
