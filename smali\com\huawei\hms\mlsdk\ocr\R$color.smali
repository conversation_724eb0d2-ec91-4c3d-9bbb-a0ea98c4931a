.class public final Lcom/huawei/hms/mlsdk/ocr/R$color;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/ocr/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "color"
.end annotation


# static fields
.field public static final emui_color_gray_1:I = 0x7f0600ce

.field public static final emui_color_gray_10:I = 0x7f0600cf

.field public static final emui_color_gray_7:I = 0x7f0600d0


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
