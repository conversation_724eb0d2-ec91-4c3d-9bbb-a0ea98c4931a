.class public abstract Lcom/huawei/hms/network/embedded/w7;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onClosed(Lcom/huawei/hms/network/embedded/v7;ILjava/lang/String;)V
    .locals 0

    return-void
.end method

.method public onClosing(Lcom/huawei/hms/network/embedded/v7;ILjava/lang/String;)V
    .locals 0

    return-void
.end method

.method public onFailure(Lcom/huawei/hms/network/embedded/v7;Ljava/lang/Throwable;Lcom/huawei/hms/network/embedded/r7;)V
    .locals 0
    .param p3    # Lcom/huawei/hms/network/embedded/r7;
        .annotation runtime Ljavax/annotation/Nullable;
        .end annotation
    .end param

    return-void
.end method

.method public onMessage(Lcom/huawei/hms/network/embedded/v7;Lcom/huawei/hms/network/embedded/ab;)V
    .locals 0

    return-void
.end method

.method public onMessage(Lcom/huawei/hms/network/embedded/v7;Ljava/lang/String;)V
    .locals 0

    return-void
.end method

.method public onOpen(Lcom/huawei/hms/network/embedded/v7;Lcom/huawei/hms/network/embedded/r7;)V
    .locals 0

    return-void
.end method

.method public onReadPong(JLjava/util/LinkedList;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(J",
            "Ljava/util/LinkedList<",
            "Ljava/lang/Long;",
            ">;)V"
        }
    .end annotation

    return-void
.end method
