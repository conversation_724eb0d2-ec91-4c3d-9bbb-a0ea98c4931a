.class public final Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;
.super Lcom/huawei/hms/mlsdk/common/MLAnalyzer;
.source "MLFaceAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/huawei/hms/mlsdk/common/MLAnalyzer<",
        "Lcom/huawei/hms/mlsdk/face/MLFace;",
        ">;"
    }
.end annotation


# static fields
.field private static final TAG:Ljava/lang/String; = "MLFaceAnalyzer"

.field private static appSettingAnalyzerMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lcom/huawei/hms/mlsdk/common/AppSettingHolder<",
            "Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;",
            ">;",
            "Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private app:Lcom/huawei/hms/mlsdk/common/MLApplication;

.field private bidirectionArray:Lcom/huawei/hms/mlsdk/mlvision/f;

.field private setting:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

.field private syncObject:Ljava/lang/Object;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 3
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->appSettingAnalyzerMap:Ljava/util/Map;

    return-void
.end method

.method private constructor <init>(Landroid/content/Context;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)V
    .locals 1

    .line 11
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/common/MLAnalyzer;-><init>()V

    const/4 v0, 0x0

    .line 12
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->bidirectionArray:Lcom/huawei/hms/mlsdk/mlvision/f;

    .line 13
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->syncObject:Ljava/lang/Object;

    .line 23
    new-instance v0, Lcom/huawei/hms/mlsdk/mlvision/f;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/mlvision/f;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->bidirectionArray:Lcom/huawei/hms/mlsdk/mlvision/f;

    .line 24
    new-instance v0, Ljava/lang/Object;

    invoke-direct {v0}, Ljava/lang/Object;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->syncObject:Ljava/lang/Object;

    .line 25
    invoke-static {p1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->initialize(Landroid/content/Context;)Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object p1

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    .line 26
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->setting:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    return-void
.end method

.method synthetic constructor <init>(Landroid/content/Context;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$1;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;-><init>(Landroid/content/Context;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)V

    return-void
.end method

.method private constructor <init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)V
    .locals 1

    .line 2
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/common/MLAnalyzer;-><init>()V

    const/4 v0, 0x0

    .line 3
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->bidirectionArray:Lcom/huawei/hms/mlsdk/mlvision/f;

    .line 4
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->syncObject:Ljava/lang/Object;

    .line 7
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    .line 8
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->setting:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    .line 9
    new-instance p1, Lcom/huawei/hms/mlsdk/mlvision/f;

    invoke-direct {p1}, Lcom/huawei/hms/mlsdk/mlvision/f;-><init>()V

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->bidirectionArray:Lcom/huawei/hms/mlsdk/mlvision/f;

    .line 10
    new-instance p1, Ljava/lang/Object;

    invoke-direct {p1}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->syncObject:Ljava/lang/Object;

    return-void
.end method

.method static synthetic access$000(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->setting:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    return-object p0
.end method

.method static synthetic access$100(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    return-object p0
.end method

.method static synthetic access$200()Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->TAG:Ljava/lang/String;

    return-object v0
.end method

.method public static declared-synchronized create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;
    .locals 4

    const-class v0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;

    monitor-enter v0

    .line 1
    :try_start_0
    sget-object v1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->TAG:Ljava/lang/String;

    const-string v2, "create 2D MLFaceAnalyzer"

    invoke-static {v1, v2}, Lcom/huawei/hms/ml/common/base/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getUniqueKey()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, p1}, Lcom/huawei/hms/mlsdk/common/AppSettingHolder;->create(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/common/AppSettingHolder;

    move-result-object v1

    .line 3
    sget-object v2, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->appSettingAnalyzerMap:Ljava/util/Map;

    invoke-interface {v2, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;

    if-nez v2, :cond_0

    .line 5
    new-instance v2, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;

    invoke-direct {v2, p0, p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)V

    .line 6
    sget-object v3, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->appSettingAnalyzerMap:Ljava/util/Map;

    invoke-interface {v3, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 9
    :cond_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->getInstance()Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;

    move-result-object v1

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v3

    invoke-virtual {v1, v3}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->prepare(Landroid/content/Context;)V

    .line 10
    invoke-static {}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->getInstance()Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;

    move-result-object v1

    invoke-virtual {v1, p1}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->convertSetting(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    move-result-object p1

    invoke-static {p1}, Lcom/huawei/hms/mlsdk/face/internal/client/FaceHelper;->create(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;

    move-result-object p1

    .line 11
    invoke-static {}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->getInstance()Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;

    move-result-object v1

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object p0

    invoke-virtual {v1, p0, p1}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->initialize(Landroid/content/Context;Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;)I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit v0

    return-object v2

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method


# virtual methods
.method public analyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Landroid/util/SparseArray;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Landroid/util/SparseArray<",
            "Lcom/huawei/hms/mlsdk/face/MLFace;",
            ">;"
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->TAG:Ljava/lang/String;

    const-string v1, "enter syncAnalyseFrame"

    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/base/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v1

    .line 3
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->initialize()V

    const/4 v3, 0x0

    const/4 v4, 0x1

    .line 4
    invoke-virtual {p1, v3, v4}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getFrame(ZZ)Lcom/huawei/hms/mlsdk/common/MLFrame;

    move-result-object p1

    .line 5
    invoke-static {}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->getInstance()Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;

    move-result-object v4

    iget-object v5, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->setting:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    iget-object v6, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v4, p1, v5, v6}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->detectFromRemote(Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;Lcom/huawei/hms/mlsdk/common/MLApplication;)Ljava/util/List;

    move-result-object p1

    .line 6
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result v4

    if-eqz v4, :cond_0

    const-string v4, "syncAnalyseFrame faceParcelList is empty!"

    .line 7
    invoke-static {v0, v4}, Lcom/huawei/hms/ml/common/base/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    .line 9
    :cond_0
    new-instance v0, Landroid/util/SparseArray;

    invoke-direct {v0}, Landroid/util/SparseArray;-><init>()V

    .line 10
    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v4

    if-ge v3, v4, :cond_1

    .line 11
    invoke-interface {p1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    invoke-virtual {v0, v3, v4}, Landroid/util/SparseArray;->put(ILjava/lang/Object;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    .line 13
    :cond_1
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v3

    .line 14
    sget-object p1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->TAG:Ljava/lang/String;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "syncAnalyseFrame duration= "

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sub-long/2addr v3, v1

    invoke-virtual {v5, v3, v4}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {p1, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    return-object v0
.end method

.method public asyncAnalyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Lcom/huawei/hmf/tasks/Task<",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/face/MLFace;",
            ">;>;"
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->TAG:Ljava/lang/String;

    const-string v1, "enter asyncAnalyseFrame"

    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/base/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->initialize()V

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 3
    invoke-virtual {p1, v0, v1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getFrame(ZZ)Lcom/huawei/hms/mlsdk/common/MLFrame;

    move-result-object p1

    .line 4
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 5
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object v0

    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->rewind()Ljava/nio/Buffer;

    .line 7
    :cond_0
    new-instance v0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$1;

    invoke-direct {v0, p0, p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$1;-><init>(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;)V

    invoke-static {v0}, Lcom/huawei/hmf/tasks/Tasks;->call(Ljava/util/concurrent/Callable;)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    return-object p1
.end method

.method public destroy()V
    .locals 3

    .line 1
    invoke-super {p0}, Lcom/huawei/hms/mlsdk/common/MLAnalyzer;->destroy()V

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->syncObject:Ljava/lang/Object;

    monitor-enter v0

    .line 3
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->getInstance()Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;

    move-result-object v1

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->release(Landroid/content/Context;)V

    .line 4
    monitor-exit v0

    return-void

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method public isAvailable()Z
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->getInstance()Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->isAvailable(Landroid/content/Context;)Z

    move-result v0

    return v0
.end method

.method protected release()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->syncObject:Ljava/lang/Object;

    monitor-enter v0

    .line 2
    :try_start_0
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->destroy()V

    .line 3
    monitor-exit v0

    return-void

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method public setTrackIdentiy(I)Z
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->bidirectionArray:Lcom/huawei/hms/mlsdk/mlvision/f;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/mlvision/f;->a(I)I

    move-result p1

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->syncObject:Ljava/lang/Object;

    monitor-enter v0

    .line 3
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->getInstance()Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;

    move-result-object v1

    invoke-virtual {v1, p1}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->setFocus(I)Z

    move-result p1

    monitor-exit v0

    return p1

    :catchall_0
    move-exception p1

    .line 4
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public stop()V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->syncObject:Ljava/lang/Object;

    monitor-enter v0

    .line 2
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->getInstance()Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;

    move-result-object v1

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->release(Landroid/content/Context;)V

    .line 3
    monitor-exit v0

    return-void

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method
