.class public interface abstract Lcom/huawei/hms/network/embedded/v2$c;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/network/embedded/v2;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "c"
.end annotation


# virtual methods
.method public abstract create(Lcom/huawei/hms/network/httpclient/Submit;)Lcom/huawei/hms/network/embedded/v2;
.end method
