.class public Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationClassification;
.super Ljava/lang/Object;
.source "MLImageSegmentationClassification.java"


# static fields
.field public static final TYPE_BACKGOURND:I = 0x0

.field public static final TYPE_BUILD:I = 0x6

.field public static final TYPE_CAT:I = 0x5

.field public static final TYPE_FLOWER:I = 0x7

.field public static final TYPE_FOOD:I = 0x4

.field public static final TYPE_GRASS:I = 0x3

.field public static final TYPE_HUMAN:I = 0x1

.field public static final TYPE_MOUNTAIN:I = 0xa

.field public static final TYPE_SAND:I = 0x9

.field public static final TYPE_SKY:I = 0x2

.field public static final TYPE_WATER:I = 0x8


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
