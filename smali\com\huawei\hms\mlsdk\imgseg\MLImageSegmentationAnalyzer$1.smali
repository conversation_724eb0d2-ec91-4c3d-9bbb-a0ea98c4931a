.class Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer$1;
.super Ljava/lang/Object;
.source "MLImageSegmentationAnalyzer.java"

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->asyncAnalyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;

.field final synthetic val$mlframe:Lcom/huawei/hms/mlsdk/common/MLFrame;

.field final synthetic val$optionsParcel:Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;


# direct methods
.method constructor <init>(Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;

    iput-object p2, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer$1;->val$mlframe:Lcom/huawei/hms/mlsdk/common/MLFrame;

    iput-object p3, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer$1;->val$optionsParcel:Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public call()Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 2
    invoke-static {}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->getInstance()Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;

    invoke-static {v1}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->access$000(Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v1

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;

    invoke-static {v2}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->access$100(Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;)Landroid/os/Bundle;

    move-result-object v2

    iget-object v3, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer$1;->val$mlframe:Lcom/huawei/hms/mlsdk/common/MLFrame;

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer$1;->val$optionsParcel:Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;

    invoke-virtual {v0, v1, v2, v3, v4}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->detect(Landroid/content/Context;Landroid/os/Bundle;Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;)Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;

    move-result-object v0

    .line 4
    invoke-static {}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->getInstance()Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;

    move-result-object v1

    if-nez v0, :cond_0

    return-object v1

    .line 8
    :cond_0
    iget-object v2, v0, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;->masks:[B

    iput-object v2, v1, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->masks:[B

    .line 9
    iget-object v2, v0, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;->foreground:Landroid/graphics/Bitmap;

    iput-object v2, v1, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->foreground:Landroid/graphics/Bitmap;

    .line 10
    iget-object v2, v0, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;->grayscale:Landroid/graphics/Bitmap;

    iput-object v2, v1, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->grayscale:Landroid/graphics/Bitmap;

    .line 11
    iget-object v0, v0, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;->original:Landroid/graphics/Bitmap;

    iput-object v0, v1, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->original:Landroid/graphics/Bitmap;

    return-object v1
.end method

.method public bridge synthetic call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer$1;->call()Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;

    move-result-object v0

    return-object v0
.end method
