.class public Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;
.super Ljava/lang/Object;
.source "MLRemoteLandmark.java"


# instance fields
.field private border:Landroid/graphics/Rect;

.field private landmark:Ljava/lang/String;

.field private landmarkId:Ljava/lang/String;

.field private positionInfos:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLCoordinate;",
            ">;"
        }
    .end annotation
.end field

.field private possibility:F


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, ""

    .line 2
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->landmarkId:Ljava/lang/String;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;FLandroid/graphics/Rect;Ljava/lang/String;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "F",
            "Landroid/graphics/Rect;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLCoordinate;",
            ">;)V"
        }
    .end annotation

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 120
    iput-object p3, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->border:Landroid/graphics/Rect;

    .line 121
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->landmark:Ljava/lang/String;

    .line 122
    iput-object p4, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->landmarkId:Ljava/lang/String;

    .line 123
    iput-object p5, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->positionInfos:Ljava/util/List;

    const/4 p1, 0x0

    .line 124
    invoke-static {p2, p1}, Ljava/lang/Float;->compare(FF)I

    move-result p3

    const/high16 p4, 0x3f800000    # 1.0f

    if-gez p3, :cond_0

    move p2, p1

    goto :goto_0

    .line 126
    :cond_0
    invoke-static {p2, p4}, Ljava/lang/Float;->compare(FF)I

    move-result p1

    if-lez p1, :cond_1

    move p2, p4

    .line 130
    :cond_1
    :goto_0
    iput p2, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->possibility:F

    return-void
.end method


# virtual methods
.method public getBorder()Landroid/graphics/Rect;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->border:Landroid/graphics/Rect;

    return-object v0
.end method

.method public getLandmark()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->landmark:Ljava/lang/String;

    return-object v0
.end method

.method public getLandmarkIdentity()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->landmarkId:Ljava/lang/String;

    return-object v0
.end method

.method public getPositionInfos()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLCoordinate;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->positionInfos:Ljava/util/List;

    return-object v0
.end method

.method public getPossibility()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->possibility:F

    return v0
.end method

.method public setBorder(Landroid/graphics/Rect;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->border:Landroid/graphics/Rect;

    return-void
.end method

.method public setLandmark(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->landmark:Ljava/lang/String;

    return-void
.end method

.method public setLandmarkId(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->landmarkId:Ljava/lang/String;

    return-void
.end method

.method public setPositionInfos(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLCoordinate;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->positionInfos:Ljava/util/List;

    return-void
.end method

.method public setPossibility(F)V
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->possibility:F

    return-void
.end method
