.class public final Lcom/huawei/hms/mlsdk/ocr/R$string;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/ocr/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "string"
.end annotation


# static fields
.field public static final common_huawei_services_unknown_issue:I = 0x7f1000cc

.field public static final hms_apk_not_installed_hints:I = 0x7f1000eb

.field public static final hms_bindfaildlg_message:I = 0x7f1000ec

.field public static final hms_bindfaildlg_title:I = 0x7f1000ed

.field public static final hms_confirm:I = 0x7f1000ee

.field public static final hms_is_spoof:I = 0x7f1000ef

.field public static final hms_spoof_hints:I = 0x7f1000f0

.field public static final networkkit_httpdns_domain:I = 0x7f1001bf

.field public static final spec_ip_0:I = 0x7f10029a

.field public static final spec_ip_1:I = 0x7f10029b

.field public static final spec_ip_2:I = 0x7f10029c


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
