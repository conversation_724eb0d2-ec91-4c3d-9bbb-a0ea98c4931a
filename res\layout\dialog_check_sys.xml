<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ScrollView android:layout_width="fill_parent" android:layout_height="wrap_content" android:fillViewport="true" android:layout_weight="1.0">
        <LinearLayout android:orientation="vertical" android:background="@color/white" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <TextView android:textSize="14.0sp" android:textColor="@color/black" android:layout_gravity="center" android:padding="@dimen/x8" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="检查是否支持" />
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" />
            <TextView android:textSize="12.0sp" android:textColor="@color/btn_blue" android:id="@id/serviceOpen" android:padding="@dimen/x8" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="辅助服务是否打开" />
            <TextView android:textSize="12.0sp" android:textColor="@color/btn_blue" android:id="@id/jobVersion" android:padding="@dimen/x8" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="当前的欢游系 版本是否支持" />
            <TextView android:textSize="12.0sp" android:textColor="@color/btn_blue" android:id="@id/androidVersion" android:padding="@dimen/x8" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="当前手机安卓系统版本是否支持" />
            <TextView android:textSize="12.0sp" android:textColor="@color/colorLabelYellow" android:autoLink="all" android:id="@id/targetVersion" android:padding="@dimen/x8" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="当前手机安装的目标APP版本是否对应" />
            <LinearLayout android:background="@drawable/background_button" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_margin="@dimen/x10">
                <TextView android:textSize="12.0sp" android:textColor="@color/main_text_color" android:id="@id/useDes" android:padding="@dimen/x8" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="介绍" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
    <LinearLayout android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="48.0dip">
        <Button android:textSize="14.0sp" android:textColor="@color/courses_btn_back_click" android:id="@id/btn_confirm" android:background="@drawable/bg_alert_exitroom_btn" android:layout_width="fill_parent" android:layout_height="fill_parent" android:text="选中运行" android:layout_weight="1.0" android:layout_below="@id/courses_tip" android:layout_centerHorizontal="true" />
        <View android:background="@color/background_gray2" android:visibility="gone" android:layout_width="1.0px" android:layout_height="fill_parent" />
        <Button android:textSize="14.0sp" android:textColor="@color/dimgrey" android:id="@id/btn_cancel" android:background="@drawable/bg_alert_exitroom_btn_r" android:layout_width="fill_parent" android:layout_height="fill_parent" android:text="取消" android:layout_weight="1.0" android:layout_below="@id/courses_tip" android:layout_centerHorizontal="true" />
    </LinearLayout>
</LinearLayout>