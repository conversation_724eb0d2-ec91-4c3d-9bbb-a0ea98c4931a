.class public Lcom/huawei/hms/network/embedded/u9$c;
.super Lcom/huawei/hms/network/embedded/va;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/network/embedded/u9;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = "c"
.end annotation


# instance fields
.field public final synthetic l:Lcom/huawei/hms/network/embedded/u9;


# direct methods
.method public constructor <init>(Lcom/huawei/hms/network/embedded/u9;)V
    .locals 0

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/u9$c;->l:Lcom/huawei/hms/network/embedded/u9;

    invoke-direct {p0}, Lcom/huawei/hms/network/embedded/va;-><init>()V

    return-void
.end method


# virtual methods
.method public b(Ljava/io/IOException;)Ljava/io/IOException;
    .locals 2

    new-instance v0, Ljava/net/SocketTimeoutException;

    const-string v1, "timeout"

    invoke-direct {v0, v1}, Ljava/net/SocketTimeoutException;-><init>(Ljava/lang/String;)V

    if-eqz p1, :cond_0

    invoke-virtual {v0, p1}, Ljava/net/SocketTimeoutException;->initCause(Ljava/lang/Throwable;)Ljava/lang/Throwable;

    :cond_0
    return-object v0
.end method

.method public i()V
    .locals 2

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/u9$c;->l:Lcom/huawei/hms/network/embedded/u9;

    sget-object v1, Lcom/huawei/hms/network/embedded/n9;->g:Lcom/huawei/hms/network/embedded/n9;

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/embedded/u9;->a(Lcom/huawei/hms/network/embedded/n9;)V

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/u9$c;->l:Lcom/huawei/hms/network/embedded/u9;

    iget-object v0, v0, Lcom/huawei/hms/network/embedded/u9;->d:Lcom/huawei/hms/network/embedded/r9;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/r9;->v()V

    return-void
.end method

.method public k()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0}, Lcom/huawei/hms/network/embedded/va;->h()Z

    move-result v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Lcom/huawei/hms/network/embedded/u9$c;->b(Ljava/io/IOException;)Ljava/io/IOException;

    move-result-object v0

    throw v0
.end method
