.class public abstract Lcom/huawei/hms/mlsdk/internal/client/rest/a;
.super Ljava/lang/Object;
.source "AbstractRestClientProvider.java"

# interfaces
.implements Lcom/huawei/hms/mlsdk/internal/client/rest/b;


# instance fields
.field protected a:Lcom/huawei/hms/mlsdk/internal/client/rest/c;


# direct methods
.method public constructor <init>(Lcom/huawei/hms/mlsdk/common/MLApplication;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;

    invoke-direct {v0, p1}, Lcom/huawei/hms/mlsdk/internal/client/rest/c;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;)V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/a;->a:Lcom/huawei/hms/mlsdk/internal/client/rest/c;

    return-void
.end method

.method private a()Lcom/huawei/hms/network/httpclient/HttpClient;
    .locals 5

    const-string v0, "AbstractRestClientProvider"

    .line 17
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/a;->a:Lcom/huawei/hms/mlsdk/internal/client/rest/c;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->a()Landroid/content/Context;

    move-result-object v1

    .line 20
    new-instance v2, Lcom/huawei/hms/network/httpclient/HttpClient$Builder;

    invoke-direct {v2}, Lcom/huawei/hms/network/httpclient/HttpClient$Builder;-><init>()V

    .line 22
    :try_start_0
    invoke-static {v1}, Lcom/huawei/secure/android/common/ssl/SecureSSLSocketFactory;->getInstance(Landroid/content/Context;)Lcom/huawei/secure/android/common/ssl/SecureSSLSocketFactory;

    move-result-object v3

    .line 23
    new-instance v4, Lcom/huawei/secure/android/common/ssl/SecureX509TrustManager;

    invoke-direct {v4, v1}, Lcom/huawei/secure/android/common/ssl/SecureX509TrustManager;-><init>(Landroid/content/Context;)V

    .line 24
    invoke-virtual {v2, v3, v4}, Lcom/huawei/hms/network/httpclient/HttpClient$Builder;->sslSocketFactory(Ljavax/net/ssl/SSLSocketFactory;Ljavax/net/ssl/X509TrustManager;)Lcom/huawei/hms/network/httpclient/HttpClient$Builder;
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_5
    .catch Ljava/security/NoSuchAlgorithmException; {:try_start_0 .. :try_end_0} :catch_4
    .catch Ljava/security/cert/CertificateException; {:try_start_0 .. :try_end_0} :catch_3
    .catch Ljava/security/KeyStoreException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/security/KeyManagementException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v1

    .line 42
    invoke-virtual {v1}, Ljava/security/KeyManagementException;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :catch_1
    move-exception v1

    .line 43
    invoke-virtual {v1}, Ljava/lang/IllegalAccessException;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :catch_2
    move-exception v1

    .line 44
    invoke-virtual {v1}, Ljava/security/KeyStoreException;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :catch_3
    move-exception v1

    .line 45
    invoke-virtual {v1}, Ljava/security/cert/CertificateException;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :catch_4
    move-exception v1

    .line 46
    invoke-virtual {v1}, Ljava/security/NoSuchAlgorithmException;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :catch_5
    move-exception v1

    .line 47
    invoke-virtual {v1}, Ljava/io/IOException;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    .line 64
    :goto_0
    sget-object v0, Lcom/huawei/secure/android/common/ssl/SecureSSLSocketFactory;->STRICT_HOSTNAME_VERIFIER:Lorg/apache/http/conn/ssl/X509HostnameVerifier;

    invoke-virtual {v2, v0}, Lcom/huawei/hms/network/httpclient/HttpClient$Builder;->hostnameVerifier(Ljavax/net/ssl/HostnameVerifier;)Lcom/huawei/hms/network/httpclient/HttpClient$Builder;

    move-result-object v0

    const/16 v1, 0x4e20

    .line 65
    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/httpclient/HttpClient$Builder;->connectTimeout(I)Lcom/huawei/hms/network/httpclient/HttpClient$Builder;

    move-result-object v0

    .line 66
    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/httpclient/HttpClient$Builder;->callTimeout(I)Lcom/huawei/hms/network/httpclient/HttpClient$Builder;

    move-result-object v0

    .line 67
    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/httpclient/HttpClient$Builder;->readTimeout(I)Lcom/huawei/hms/network/httpclient/HttpClient$Builder;

    .line 68
    invoke-virtual {v2}, Lcom/huawei/hms/network/httpclient/HttpClient$Builder;->build()Lcom/huawei/hms/network/httpclient/HttpClient;

    move-result-object v0

    return-object v0
.end method


# virtual methods
.method public a(Ljava/lang/String;)Lcom/huawei/hms/network/restclient/RestClient;
    .locals 2

    if-eqz p1, :cond_0

    :try_start_0
    const-string v0, ""

    .line 1
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_2

    goto :goto_0

    :catch_0
    move-exception p1

    goto :goto_2

    :catch_1
    move-exception p1

    goto :goto_3

    .line 2
    :cond_0
    :goto_0
    iget-object p1, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/a;->a:Lcom/huawei/hms/mlsdk/internal/client/rest/c;

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->b()Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 4
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;->getMLServiceUrls()Ljava/util/List;

    move-result-object p1

    if-eqz p1, :cond_1

    .line 5
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_1

    const/4 v0, 0x0

    .line 6
    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;
    :try_end_0
    .catch Ljava/lang/RuntimeException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :cond_1
    const-string p1, "Unkonwn"

    .line 7
    :cond_2
    :goto_1
    :try_start_1
    new-instance v0, Lcom/huawei/hms/network/restclient/RestClient$Builder;

    invoke-direct {v0}, Lcom/huawei/hms/network/restclient/RestClient$Builder;-><init>()V

    .line 8
    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/restclient/RestClient$Builder;->baseUrl(Ljava/lang/String;)Lcom/huawei/hms/network/restclient/RestClient$Builder;

    move-result-object p1

    .line 9
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/internal/client/rest/a;->a()Lcom/huawei/hms/network/httpclient/HttpClient;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/huawei/hms/network/restclient/RestClient$Builder;->httpClient(Lcom/huawei/hms/network/httpclient/HttpClient;)Lcom/huawei/hms/network/restclient/RestClient$Builder;

    move-result-object p1

    .line 10
    invoke-virtual {p1}, Lcom/huawei/hms/network/restclient/RestClient$Builder;->build()Lcom/huawei/hms/network/restclient/RestClient;

    move-result-object p1
    :try_end_1
    .catch Ljava/lang/RuntimeException; {:try_start_1 .. :try_end_1} :catch_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    return-object p1

    :goto_2
    const-string v0, "AbstractRestClientProvider"

    const-string v1, "Failure to get rest client"

    .line 15
    invoke-static {v0, v1, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    const/4 p1, 0x0

    return-object p1

    .line 16
    :goto_3
    throw p1
.end method
