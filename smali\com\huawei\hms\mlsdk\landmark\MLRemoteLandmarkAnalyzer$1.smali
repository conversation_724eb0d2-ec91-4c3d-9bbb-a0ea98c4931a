.class Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;
.super Ljava/lang/Object;
.source "MLRemoteLandmarkAnalyzer.java"

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->asyncAnalyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Ljava/util/List<",
        "Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;",
        ">;>;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

.field final synthetic val$frame:Lcom/huawei/hms/mlsdk/common/MLFrame;


# direct methods
.method constructor <init>(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    iput-object p2, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->val$frame:Lcom/huawei/hms/mlsdk/common/MLFrame;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->call()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public call()Ljava/util/List;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->access$000(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;)Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->isEnableFingerprintVerification()Z

    move-result v0

    const/4 v1, 0x2

    if-eqz v0, :cond_1

    .line 3
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->access$100(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppSetting()Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;->getCertFingerprint()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 4
    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    new-instance v0, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v2, "Failed to detect cloud landmark."

    invoke-direct {v0, v2, v1}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v0

    .line 9
    :cond_1
    :goto_0
    invoke-static {}, Lcom/huawei/hms/ml/grs/GrsRegionUtils;->getGrsCountryCode()Ljava/lang/String;

    move-result-object v0

    .line 10
    invoke-static {}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->access$200()Ljava/lang/String;

    move-result-object v2

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "GRS countryCode is "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Lcom/huawei/hms/ml/common/utils/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    if-eqz v0, :cond_2

    .line 14
    invoke-static {}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getInstance()Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v2

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v2

    invoke-static {v2, v0}, Lcom/huawei/hms/ml/grs/GrsUtils;->initGrsVisionSearchClientWithCountry(Landroid/content/Context;Ljava/lang/String;)Lcom/huawei/hms/framework/network/grs/GrsClient;

    move-result-object v0

    .line 15
    invoke-static {v0}, Lcom/huawei/hms/ml/grs/GrsUtils;->getVisionSearchUrls(Lcom/huawei/hms/framework/network/grs/GrsClient;)Ljava/util/List;

    move-result-object v0

    goto :goto_1

    .line 17
    :cond_2
    invoke-static {}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getInstance()Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v0

    const/4 v2, 0x0

    invoke-static {v0, v2}, Lcom/huawei/hms/ml/grs/GrsUtils;->getUrls(Landroid/content/Context;Z)Ljava/util/List;

    move-result-object v0

    .line 19
    :goto_1
    invoke-static {v0}, Lcom/huawei/hms/ml/grs/GrsUtils;->addHttpsHeaders(Ljava/util/List;)Ljava/util/List;

    move-result-object v3

    if-eqz v3, :cond_4

    .line 20
    invoke-interface {v3}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_4

    .line 24
    new-instance v0, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a()Lcom/huawei/hms/mlsdk/internal/client/rest/e;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e;->a()Ljava/util/Map;

    move-result-object v6

    .line 25
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    invoke-static {v0, v6}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->access$300(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Ljava/util/Map;)Z

    move-result v0

    if-nez v0, :cond_3

    .line 30
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->val$frame:Lcom/huawei/hms/mlsdk/common/MLFrame;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->readBitmap()Landroid/graphics/Bitmap;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->access$400(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Landroid/graphics/Bitmap;)D

    move-result-wide v0

    .line 31
    iget-object v2, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->val$frame:Lcom/huawei/hms/mlsdk/common/MLFrame;

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLFrame;->readBitmap()Landroid/graphics/Bitmap;

    move-result-object v4

    invoke-static {v2, v4, v0, v1}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->access$500(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Landroid/graphics/Bitmap;D)Landroid/graphics/Bitmap;

    move-result-object v2

    .line 33
    iget-object v4, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    invoke-static {v4, v2}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->access$600(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Landroid/graphics/Bitmap;)Ljava/lang/String;

    move-result-object v2

    .line 34
    iget-object v4, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    invoke-static {v4}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->access$000(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;)Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

    move-result-object v5

    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->getLargestNumOfReturns()I

    move-result v5

    invoke-static {v4, v2, v5}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->access$700(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Ljava/lang/String;I)Ljava/lang/String;

    move-result-object v7

    .line 36
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->a()Lcom/huawei/hms/mlsdk/internal/client/rest/d;

    move-result-object v2

    invoke-static {}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->access$200()Ljava/lang/String;

    move-result-object v8

    const-class v4, Lcom/huawei/hms/mlsdk/cloud/RemoteRequestService;

    const-string v5, "v1/image/recognition/landmark"

    invoke-virtual/range {v2 .. v8}, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->a(Ljava/util/List;Ljava/lang/Class;Ljava/lang/String;Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/network/httpclient/Response;

    move-result-object v2

    .line 38
    iget-object v3, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    invoke-static {v3, v2, v0, v1}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->access$800(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Lcom/huawei/hms/network/httpclient/Response;D)Ljava/util/List;

    move-result-object v0

    return-object v0

    .line 39
    :cond_3
    new-instance v0, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v2, "Header param error, fail to detect cloud landmark."

    invoke-direct {v0, v2, v1}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v0

    .line 40
    :cond_4
    new-instance v0, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v2, "UrlList is empty, fail to detect cloud landmark."

    invoke-direct {v0, v2, v1}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v0
.end method
