.class public Lcom/huawei/hms/mlsdk/text/MLText;
.super Ljava/lang/Object;
.source "MLText.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/text/MLText$Word;,
        Lcom/huawei/hms/mlsdk/text/MLText$TextLine;,
        Lcom/huawei/hms/mlsdk/text/MLText$Block;,
        Lcom/huawei/hms/mlsdk/text/MLText$Base;
    }
.end annotation


# instance fields
.field private final plates:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/MLPlate;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 6
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 7
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLText;->plates:Ljava/util/List;

    return-void
.end method

.method public constructor <init>(Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/MLPlate;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLText;->plates:Ljava/util/List;

    .line 5
    invoke-interface {v0, p1}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    return-void
.end method


# virtual methods
.method public getBlocks()Ljava/util/List;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/MLText$Block;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/text/MLText;->getTextPlate()Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;

    move-result-object v0

    .line 2
    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;->getBlockList()Ljava/util/List;

    move-result-object v0

    .line 3
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 4
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;

    .line 5
    new-instance v10, Lcom/huawei/hms/mlsdk/text/MLText$Block;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getStringValue()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getBorder()Landroid/graphics/Rect;

    move-result-object v5

    .line 6
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getLanguageList()Ljava/util/List;

    move-result-object v6

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;->getContents()Ljava/util/List;

    move-result-object v7

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getVertexes()[Landroid/graphics/Point;

    move-result-object v8

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getConfidence()Ljava/lang/Float;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Float;->floatValue()F

    move-result v9

    move-object v3, v10

    invoke-direct/range {v3 .. v9}, Lcom/huawei/hms/mlsdk/text/MLText$Block;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V

    .line 7
    invoke-interface {v1, v10}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_0
    return-object v1
.end method

.method public getPlates()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/MLPlate;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLText;->plates:Ljava/util/List;

    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public getPlatesByType(Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;)Ljava/util/List;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;",
            ")",
            "Ljava/util/List<",
            "+",
            "Lcom/huawei/hms/mlsdk/text/MLPlate;",
            ">;"
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;->TEXT:Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    if-ne p1, v0, :cond_2

    .line 2
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 3
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLText;->plates:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/text/MLPlate;

    .line 4
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/text/MLPlate;->getMLPlateType()Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    move-result-object v3

    if-ne v3, p1, :cond_0

    .line 5
    check-cast v2, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    return-object v0

    .line 9
    :cond_2
    sget-object v0, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;->IMAGE:Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    if-ne p1, v0, :cond_5

    .line 10
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 11
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLText;->plates:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_3
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_4

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/text/MLPlate;

    .line 12
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/text/MLPlate;->getMLPlateType()Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    move-result-object v3

    if-ne v3, p1, :cond_3

    .line 13
    check-cast v2, Lcom/huawei/hms/mlsdk/text/entity/imageplate/MLImagePlate;

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    :cond_4
    return-object v0

    .line 18
    :cond_5
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    return-object p1
.end method

.method public getStringValue()Ljava/lang/String;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/text/MLText;->getTextPlate()Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;->getStringValue()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getTextPlate()Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLText;->plates:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/mlsdk/text/MLPlate;

    .line 2
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/text/MLPlate;->getMLPlateType()Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    move-result-object v2

    sget-object v3, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;->TEXT:Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    if-ne v2, v3, :cond_0

    .line 3
    check-cast v1, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;

    return-object v1

    .line 7
    :cond_1
    new-instance v0, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    invoke-direct {v0, v1}, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;-><init>(Ljava/util/List;)V

    return-object v0
.end method
