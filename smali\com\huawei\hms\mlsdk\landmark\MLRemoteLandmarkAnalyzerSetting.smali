.class public Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;
.super Ljava/lang/Object;
.source "MLRemoteLandmarkAnalyzerSetting.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;
    }
.end annotation


# static fields
.field public static final DEFAULT_SETTINGS:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

.field public static final NEWEST_PATTERN:I = 0x2

.field public static final STEADY_PATTERN:I = 0x1


# instance fields
.field private final enforceCertFingerprintMatch:Z

.field private final maxResults:I

.field private final modelType:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;->create()Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

    move-result-object v0

    sput-object v0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->DEFAULT_SETTINGS:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

    return-void
.end method

.method private constructor <init>(IIZ)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput p1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->maxResults:I

    .line 4
    iput p2, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->modelType:I

    .line 5
    iput-boolean p3, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->enforceCertFingerprintMatch:Z

    return-void
.end method

.method synthetic constructor <init>(IIZLcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$1;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;-><init>(IIZ)V

    return-void
.end method


# virtual methods
.method public create()Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;
    .locals 1

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;-><init>()V

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 4
    :cond_1
    check-cast p1, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

    .line 5
    iget v1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->maxResults:I

    iget v3, p1, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->maxResults:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->modelType:I

    iget v3, p1, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->modelType:I

    if-ne v1, v3, :cond_2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->enforceCertFingerprintMatch:Z

    iget-boolean p1, p1, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->enforceCertFingerprintMatch:Z

    if-ne v1, p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public getLargestNumOfReturns()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->maxResults:I

    return v0
.end method

.method public getPatternType()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->modelType:I

    return v0
.end method

.method public hashCode()I
    .locals 3

    const/4 v0, 0x3

    new-array v0, v0, [Ljava/lang/Object;

    .line 1
    iget v1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->maxResults:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget v1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->modelType:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->enforceCertFingerprintMatch:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->hashCode([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public final isEnableFingerprintVerification()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;->enforceCertFingerprintMatch:Z

    return v0
.end method
