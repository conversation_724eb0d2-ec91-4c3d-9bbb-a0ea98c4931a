.class public Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;
.super Ljava/lang/Object;
.source "LocalTextAnalyzer.java"


# static fields
.field private static TAG:Ljava/lang/String; = "LocalTextAnalyzer"

.field private static analyzerMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private detectKey:Ljava/lang/String;

.field private hmsMLApp:Lcom/huawei/hms/mlsdk/common/MLApplication;

.field private setting:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->analyzerMap:Ljava/util/Map;

    return-void
.end method

.method public constructor <init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, "isDetect"

    .line 2
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->detectKey:Ljava/lang/String;

    .line 7
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->hmsMLApp:Lcom/huawei/hms/mlsdk/common/MLApplication;

    if-nez p2, :cond_0

    .line 9
    new-instance p1, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;

    invoke-direct {p1}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;-><init>()V

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->create()Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    move-result-object p1

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->setting:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    goto :goto_0

    .line 11
    :cond_0
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->setting:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    :goto_0
    return-void
.end method

.method static synthetic access$000(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->hmsMLApp:Lcom/huawei/hms/mlsdk/common/MLApplication;

    return-object p0
.end method

.method static synthetic access$100(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;)Ljava/lang/String;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->detectKey:Ljava/lang/String;

    return-object p0
.end method

.method static synthetic access$200(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;)Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->setting:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    return-object p0
.end method

.method public static declared-synchronized getInstance(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;)Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;
    .locals 6

    const-class v0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    monitor-enter v0

    if-eqz p0, :cond_1

    .line 1
    :try_start_0
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getUniqueKey()Ljava/lang/String;

    move-result-object v1

    if-eqz v1, :cond_1

    .line 5
    sget-object v1, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->analyzerMap:Ljava/util/Map;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getUniqueKey()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    if-nez v1, :cond_0

    .line 6
    new-instance v1, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    invoke-direct {v1, p0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;)V

    .line 7
    sget-object v2, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->analyzerMap:Ljava/util/Map;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getUniqueKey()Ljava/lang/String;

    move-result-object v3

    invoke-interface {v2, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 10
    :cond_0
    invoke-direct {v1, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->setSetting(Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;)V

    .line 11
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v2

    invoke-direct {v1, v2}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->prepare(Landroid/content/Context;)V

    .line 12
    new-instance v2, Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->getLanguage()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->getOCRMode()I

    move-result v4

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->getPlateEnable()I

    move-result p1

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->toBundle()Landroid/os/Bundle;

    move-result-object v5

    invoke-direct {v2, v3, v4, p1, v5}, Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;-><init>(Ljava/lang/String;IILandroid/os/Bundle;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 14
    :try_start_1
    invoke-static {}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->getInstance()Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;

    move-result-object p1

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object p0

    invoke-virtual {p1, p0, v2}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->initial(Landroid/content/Context;Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;)I
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_0

    .line 16
    :catch_0
    :try_start_2
    sget-object p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->TAG:Ljava/lang/String;

    const-string p1, "Initial failed."

    invoke-static {p0, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :goto_0
    monitor-exit v0

    return-object v1

    .line 17
    :cond_1
    :try_start_3
    new-instance p0, Ljava/lang/NullPointerException;

    invoke-direct {p0}, Ljava/lang/NullPointerException;-><init>()V

    throw p0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method

.method private prepare(Landroid/content/Context;)V
    .locals 1

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->getInstance()Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->prepare(Landroid/content/Context;)V

    return-void
.end method

.method private recognizerImage(Lcom/huawei/hms/mlsdk/common/MLFrame;Z)Lcom/huawei/hmf/tasks/Task;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            "Z)",
            "Lcom/huawei/hmf/tasks/Task<",
            "Lcom/huawei/hms/mlsdk/text/MLText;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;

    invoke-direct {v0, p0, p2, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;-><init>(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;ZLcom/huawei/hms/mlsdk/common/MLFrame;)V

    invoke-static {v0}, Lcom/huawei/hmf/tasks/Tasks;->callInBackground(Ljava/util/concurrent/Callable;)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    return-object p1
.end method

.method private setSetting(Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->setting:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    return-void
.end method


# virtual methods
.method public detect(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Lcom/huawei/hmf/tasks/Task<",
            "Lcom/huawei/hms/mlsdk/text/MLText;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x1

    .line 1
    invoke-direct {p0, p1, v0}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->recognizerImage(Lcom/huawei/hms/mlsdk/common/MLFrame;Z)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    return-object p1
.end method

.method public isAvailable()Z
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->getInstance()Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->hmsMLApp:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->isAvailable(Landroid/content/Context;)Z

    move-result v0

    return v0
.end method

.method public processImage(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Lcom/huawei/hmf/tasks/Task<",
            "Lcom/huawei/hms/mlsdk/text/MLText;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x0

    .line 1
    invoke-direct {p0, p1, v0}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->recognizerImage(Lcom/huawei/hms/mlsdk/common/MLFrame;Z)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    return-object p1
.end method

.method public release()V
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->getInstance()Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->hmsMLApp:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->release(Landroid/content/Context;)V

    return-void
.end method
