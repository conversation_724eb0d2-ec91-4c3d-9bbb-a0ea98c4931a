.class final Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer$Holder;
.super Ljava/lang/Object;
.source "TextAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "Holder"
.end annotation


# static fields
.field static final INSTANCE:Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;-><init>(Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer$1;)V

    sput-object v0, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer$Holder;->INSTANCE:Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
