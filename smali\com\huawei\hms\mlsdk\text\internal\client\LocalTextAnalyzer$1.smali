.class Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;
.super Ljava/lang/Object;
.source "LocalTextAnalyzer.java"

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->recognizerImage(Lcom/huawei/hms/mlsdk/common/MLFrame;Z)Lcom/huawei/hmf/tasks/Task;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Lcom/huawei/hms/mlsdk/text/MLText;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

.field final synthetic val$frame:Lcom/huawei/hms/mlsdk/common/MLFrame;

.field final synthetic val$isDetect:Z


# direct methods
.method constructor <init>(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;ZLcom/huawei/hms/mlsdk/common/MLFrame;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    iput-boolean p2, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->val$isDetect:Z

    iput-object p3, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->val$frame:Lcom/huawei/hms/mlsdk/common/MLFrame;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public call()Lcom/huawei/hms/mlsdk/text/MLText;
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->isAvailable()Z

    move-result v0

    if-nez v0, :cond_0

    .line 3
    new-instance v0, Lcom/huawei/hms/mlsdk/text/MLText;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/text/MLText;-><init>()V

    return-object v0

    .line 6
    :cond_0
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->access$000(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->toBundle()Landroid/os/Bundle;

    move-result-object v0

    .line 7
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    invoke-static {v1}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->access$100(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;)Ljava/lang/String;

    move-result-object v1

    iget-boolean v2, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->val$isDetect:Z

    invoke-virtual {v0, v1, v2}, Landroid/os/Bundle;->putInt(Ljava/lang/String;I)V

    .line 8
    new-instance v1, Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    invoke-static {v2}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->access$200(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;)Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    move-result-object v2

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->getLanguage()Ljava/lang/String;

    move-result-object v2

    iget-object v3, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    invoke-static {v3}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->access$200(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;)Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    move-result-object v3

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->getOCRMode()I

    move-result v3

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    invoke-static {v4}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->access$200(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;)Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    move-result-object v4

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->getPlateEnable()I

    move-result v4

    invoke-direct {v1, v2, v3, v4, v0}, Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;-><init>(Ljava/lang/String;IILandroid/os/Bundle;)V

    .line 10
    invoke-static {}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->getInstance()Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;

    move-result-object v2

    iget-object v3, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    invoke-static {v3}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->access$000(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v3

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v3

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->val$frame:Lcom/huawei/hms/mlsdk/common/MLFrame;

    invoke-static {v4}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->convert(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;

    move-result-object v4

    invoke-virtual {v2, v3, v0, v4, v1}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->recognizeWithBitmap(Landroid/content/Context;Landroid/os/Bundle;Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;)Lcom/huawei/hms/ml/common/ocr/TextParcel;

    move-result-object v0

    if-eqz v0, :cond_2

    .line 11
    invoke-virtual {v0}, Lcom/huawei/hms/ml/common/ocr/TextParcel;->getBlocks()Ljava/util/List;

    move-result-object v1

    if-eqz v1, :cond_2

    invoke-virtual {v0}, Lcom/huawei/hms/ml/common/ocr/TextParcel;->getBlocks()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-nez v1, :cond_1

    goto :goto_0

    .line 14
    :cond_1
    new-instance v1, Lcom/huawei/hms/mlsdk/text/MLText;

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->textParcelToMLPlate(Lcom/huawei/hms/ml/common/ocr/TextParcel;)Ljava/util/List;

    move-result-object v0

    invoke-direct {v1, v0}, Lcom/huawei/hms/mlsdk/text/MLText;-><init>(Ljava/util/List;)V

    return-object v1

    .line 15
    :cond_2
    :goto_0
    new-instance v0, Lcom/huawei/hms/mlsdk/text/MLText;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/text/MLText;-><init>()V

    return-object v0
.end method

.method public bridge synthetic call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer$1;->call()Lcom/huawei/hms/mlsdk/text/MLText;

    move-result-object v0

    return-object v0
.end method
