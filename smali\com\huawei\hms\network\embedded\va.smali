.class public Lcom/huawei/hms/network/embedded/va;
.super Lcom/huawei/hms/network/embedded/wb;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/network/embedded/va$c;
    }
.end annotation


# static fields
.field public static final h:I = 0x10000

.field public static final i:J

.field public static final j:J

.field public static k:Lcom/huawei/hms/network/embedded/va;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field


# instance fields
.field public e:Z

.field public f:Lcom/huawei/hms/network/embedded/va;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field public g:J


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    sget-object v0, Ljava/util/concurrent/TimeUnit;->SECONDS:Ljava/util/concurrent/TimeUnit;

    const-wide/16 v1, 0x3c

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/TimeUnit;->to<PERSON><PERSON><PERSON>(J)J

    move-result-wide v0

    sput-wide v0, Lcom/huawei/hms/network/embedded/va;->i:J

    sget-object v2, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-virtual {v2, v0, v1}, Ljava/util/concurrent/TimeUnit;->toNanos(J)J

    move-result-wide v0

    sput-wide v0, Lcom/huawei/hms/network/embedded/va;->j:J

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/huawei/hms/network/embedded/wb;-><init>()V

    return-void
.end method

.method public static declared-synchronized a(Lcom/huawei/hms/network/embedded/va;JZ)V
    .locals 5

    const-class v0, Lcom/huawei/hms/network/embedded/va;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/huawei/hms/network/embedded/va;->k:Lcom/huawei/hms/network/embedded/va;

    if-nez v1, :cond_0

    new-instance v1, Lcom/huawei/hms/network/embedded/va;

    invoke-direct {v1}, Lcom/huawei/hms/network/embedded/va;-><init>()V

    sput-object v1, Lcom/huawei/hms/network/embedded/va;->k:Lcom/huawei/hms/network/embedded/va;

    new-instance v1, Lcom/huawei/hms/network/embedded/va$c;

    invoke-direct {v1}, Lcom/huawei/hms/network/embedded/va$c;-><init>()V

    invoke-virtual {v1}, Ljava/lang/Thread;->start()V

    :cond_0
    invoke-static {}, Ljava/lang/System;->nanoTime()J

    move-result-wide v1

    const-wide/16 v3, 0x0

    cmp-long v3, p1, v3

    if-eqz v3, :cond_1

    if-eqz p3, :cond_1

    invoke-virtual {p0}, Lcom/huawei/hms/network/embedded/wb;->c()J

    move-result-wide v3

    sub-long/2addr v3, v1

    invoke-static {p1, p2, v3, v4}, Ljava/lang/Math;->min(JJ)J

    move-result-wide p1

    :goto_0
    add-long/2addr p1, v1

    iput-wide p1, p0, Lcom/huawei/hms/network/embedded/va;->g:J

    goto :goto_1

    :cond_1
    if-eqz v3, :cond_2

    goto :goto_0

    :cond_2
    if-eqz p3, :cond_6

    invoke-virtual {p0}, Lcom/huawei/hms/network/embedded/wb;->c()J

    move-result-wide p1

    iput-wide p1, p0, Lcom/huawei/hms/network/embedded/va;->g:J

    :goto_1
    invoke-direct {p0, v1, v2}, Lcom/huawei/hms/network/embedded/va;->b(J)J

    move-result-wide p1

    sget-object p3, Lcom/huawei/hms/network/embedded/va;->k:Lcom/huawei/hms/network/embedded/va;

    :goto_2
    iget-object v3, p3, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    if-eqz v3, :cond_4

    invoke-direct {v3, v1, v2}, Lcom/huawei/hms/network/embedded/va;->b(J)J

    move-result-wide v3

    cmp-long v3, p1, v3

    if-gez v3, :cond_3

    goto :goto_3

    :cond_3
    iget-object p3, p3, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    goto :goto_2

    :cond_4
    :goto_3
    iget-object p1, p3, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    iput-object p0, p3, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    sget-object p0, Lcom/huawei/hms/network/embedded/va;->k:Lcom/huawei/hms/network/embedded/va;

    if-ne p3, p0, :cond_5

    invoke-virtual {v0}, Ljava/lang/Object;->notify()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_5
    monitor-exit v0

    return-void

    :cond_6
    :try_start_1
    new-instance p0, Ljava/lang/AssertionError;

    invoke-direct {p0}, Ljava/lang/AssertionError;-><init>()V

    throw p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method

.method public static declared-synchronized a(Lcom/huawei/hms/network/embedded/va;)Z
    .locals 3

    const-class v0, Lcom/huawei/hms/network/embedded/va;

    monitor-enter v0

    :try_start_0
    sget-object v1, Lcom/huawei/hms/network/embedded/va;->k:Lcom/huawei/hms/network/embedded/va;

    :goto_0
    if-eqz v1, :cond_1

    iget-object v2, v1, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    if-ne v2, p0, :cond_0

    iget-object v2, p0, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    iput-object v2, v1, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    const/4 v1, 0x0

    iput-object v1, p0, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 p0, 0x0

    :goto_1
    monitor-exit v0

    return p0

    :cond_0
    move-object v1, v2

    goto :goto_0

    :cond_1
    const/4 p0, 0x1

    goto :goto_1

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method

.method private b(J)J
    .locals 2

    iget-wide v0, p0, Lcom/huawei/hms/network/embedded/va;->g:J

    sub-long/2addr v0, p1

    return-wide v0
.end method

.method public static j()Lcom/huawei/hms/network/embedded/va;
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/InterruptedException;
        }
    .end annotation

    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation

    const-class v0, Lcom/huawei/hms/network/embedded/va;

    sget-object v1, Lcom/huawei/hms/network/embedded/va;->k:Lcom/huawei/hms/network/embedded/va;

    iget-object v1, v1, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    const/4 v2, 0x0

    invoke-static {}, Ljava/lang/System;->nanoTime()J

    move-result-wide v3

    if-nez v1, :cond_1

    sget-wide v5, Lcom/huawei/hms/network/embedded/va;->i:J

    invoke-virtual {v0, v5, v6}, Ljava/lang/Object;->wait(J)V

    sget-object v0, Lcom/huawei/hms/network/embedded/va;->k:Lcom/huawei/hms/network/embedded/va;

    iget-object v0, v0, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    if-nez v0, :cond_0

    invoke-static {}, Ljava/lang/System;->nanoTime()J

    move-result-wide v0

    sub-long/2addr v0, v3

    sget-wide v3, Lcom/huawei/hms/network/embedded/va;->j:J

    cmp-long v0, v0, v3

    if-ltz v0, :cond_0

    sget-object v2, Lcom/huawei/hms/network/embedded/va;->k:Lcom/huawei/hms/network/embedded/va;

    :cond_0
    return-object v2

    :cond_1
    invoke-direct {v1, v3, v4}, Lcom/huawei/hms/network/embedded/va;->b(J)J

    move-result-wide v3

    const-wide/16 v5, 0x0

    cmp-long v5, v3, v5

    if-lez v5, :cond_2

    const-wide/32 v5, 0xf4240

    div-long v7, v3, v5

    mul-long/2addr v5, v7

    sub-long/2addr v3, v5

    long-to-int v1, v3

    invoke-virtual {v0, v7, v8, v1}, Ljava/lang/Object;->wait(JI)V

    return-object v2

    :cond_2
    sget-object v0, Lcom/huawei/hms/network/embedded/va;->k:Lcom/huawei/hms/network/embedded/va;

    iget-object v3, v1, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    iput-object v3, v0, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    iput-object v2, v1, Lcom/huawei/hms/network/embedded/va;->f:Lcom/huawei/hms/network/embedded/va;

    return-object v1
.end method


# virtual methods
.method public final a(Lcom/huawei/hms/network/embedded/ub;)Lcom/huawei/hms/network/embedded/ub;
    .locals 1

    new-instance v0, Lcom/huawei/hms/network/embedded/va$a;

    invoke-direct {v0, p0, p1}, Lcom/huawei/hms/network/embedded/va$a;-><init>(Lcom/huawei/hms/network/embedded/va;Lcom/huawei/hms/network/embedded/ub;)V

    return-object v0
.end method

.method public final a(Lcom/huawei/hms/network/embedded/vb;)Lcom/huawei/hms/network/embedded/vb;
    .locals 1

    new-instance v0, Lcom/huawei/hms/network/embedded/va$b;

    invoke-direct {v0, p0, p1}, Lcom/huawei/hms/network/embedded/va$b;-><init>(Lcom/huawei/hms/network/embedded/va;Lcom/huawei/hms/network/embedded/vb;)V

    return-object v0
.end method

.method public final a(Ljava/io/IOException;)Ljava/io/IOException;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0}, Lcom/huawei/hms/network/embedded/va;->h()Z

    move-result v0

    if-nez v0, :cond_0

    return-object p1

    :cond_0
    invoke-virtual {p0, p1}, Lcom/huawei/hms/network/embedded/va;->b(Ljava/io/IOException;)Ljava/io/IOException;

    move-result-object p1

    return-object p1
.end method

.method public final a(Z)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-virtual {p0}, Lcom/huawei/hms/network/embedded/va;->h()Z

    move-result v0

    if-eqz v0, :cond_1

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    invoke-virtual {p0, p1}, Lcom/huawei/hms/network/embedded/va;->b(Ljava/io/IOException;)Ljava/io/IOException;

    move-result-object p1

    throw p1

    :cond_1
    :goto_0
    return-void
.end method

.method public b(Ljava/io/IOException;)Ljava/io/IOException;
    .locals 2
    .param p1    # Ljava/io/IOException;
        .annotation runtime Ljavax/annotation/Nullable;
        .end annotation
    .end param

    new-instance v0, Ljava/io/InterruptedIOException;

    const-string v1, "timeout"

    invoke-direct {v0, v1}, Ljava/io/InterruptedIOException;-><init>(Ljava/lang/String;)V

    if-eqz p1, :cond_0

    invoke-virtual {v0, p1}, Ljava/io/InterruptedIOException;->initCause(Ljava/lang/Throwable;)Ljava/lang/Throwable;

    :cond_0
    return-object v0
.end method

.method public final g()V
    .locals 5

    iget-boolean v0, p0, Lcom/huawei/hms/network/embedded/va;->e:Z

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/huawei/hms/network/embedded/wb;->f()J

    move-result-wide v0

    invoke-virtual {p0}, Lcom/huawei/hms/network/embedded/wb;->d()Z

    move-result v2

    const-wide/16 v3, 0x0

    cmp-long v3, v0, v3

    if-nez v3, :cond_0

    if-nez v2, :cond_0

    return-void

    :cond_0
    const/4 v3, 0x1

    iput-boolean v3, p0, Lcom/huawei/hms/network/embedded/va;->e:Z

    invoke-static {p0, v0, v1, v2}, Lcom/huawei/hms/network/embedded/va;->a(Lcom/huawei/hms/network/embedded/va;JZ)V

    return-void

    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "Unbalanced enter/exit"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public final h()Z
    .locals 2

    iget-boolean v0, p0, Lcom/huawei/hms/network/embedded/va;->e:Z

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    iput-boolean v1, p0, Lcom/huawei/hms/network/embedded/va;->e:Z

    invoke-static {p0}, Lcom/huawei/hms/network/embedded/va;->a(Lcom/huawei/hms/network/embedded/va;)Z

    move-result v0

    return v0
.end method

.method public i()V
    .locals 0

    return-void
.end method
