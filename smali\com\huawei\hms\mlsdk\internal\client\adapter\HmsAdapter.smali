.class public Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;
.super Ljava/lang/Object;
.source "HmsAdapter.java"


# static fields
.field private static final HMS_PACKAGE_NAME:Ljava/lang/String; = "com.huawei.hwid"

.field private static final HSF_PACKAGE_NAME:Ljava/lang/String; = "com.huawei.android.hsf"

.field private static final TAG:Ljava/lang/String; = "HmsAdapter"


# instance fields
.field private final callBack:Lcom/huawei/hms/adapter/AvailableAdapter$AvailableCallBack;

.field private isHmsInstalled:Z

.field private isUpdatePopupShown:Z


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 30
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 26
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isHmsInstalled:Z

    .line 28
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isUpdatePopupShown:Z

    .line 81
    new-instance v0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter$1;

    invoke-direct {v0, p0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter$1;-><init>(Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;)V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->callBack:Lcom/huawei/hms/adapter/AvailableAdapter$AvailableCallBack;

    return-void
.end method

.method static synthetic access$002(Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;Z)Z
    .locals 0

    .line 19
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isUpdatePopupShown:Z

    return p1
.end method

.method static synthetic access$102(Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;Z)Z
    .locals 0

    .line 19
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isHmsInstalled:Z

    return p1
.end method

.method private static findActivity(Landroid/content/Context;)Landroid/app/Activity;
    .locals 1

    .line 99
    instance-of v0, p0, Landroid/app/Activity;

    if-eqz v0, :cond_0

    .line 100
    check-cast p0, Landroid/app/Activity;

    return-object p0

    .line 103
    :cond_0
    instance-of v0, p0, Landroid/content/ContextWrapper;

    if-eqz v0, :cond_1

    .line 104
    check-cast p0, Landroid/content/ContextWrapper;

    .line 105
    invoke-virtual {p0}, Landroid/content/ContextWrapper;->getBaseContext()Landroid/content/Context;

    move-result-object p0

    invoke-static {p0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->findActivity(Landroid/content/Context;)Landroid/app/Activity;

    move-result-object p0

    return-object p0

    :cond_1
    const/4 p0, 0x0

    return-object p0
.end method

.method public static isHmsInternal(Landroid/content/Context;)Z
    .locals 1

    const-string v0, "com.huawei.hwid"

    .line 112
    invoke-static {p0, v0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isPackageInternal(Landroid/content/Context;Ljava/lang/String;)Z

    move-result p0

    return p0
.end method

.method public static isHsfInternal(Landroid/content/Context;)Z
    .locals 1

    const-string v0, "com.huawei.android.hsf"

    .line 116
    invoke-static {p0, v0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isPackageInternal(Landroid/content/Context;Ljava/lang/String;)Z

    move-result p0

    return p0
.end method

.method public static final isPackageInternal(Landroid/content/Context;Ljava/lang/String;)Z
    .locals 3

    const/4 v0, 0x0

    .line 121
    :try_start_0
    invoke-virtual {p0}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object p0

    invoke-virtual {p0, p1, v0}, Landroid/content/pm/PackageManager;->getPackageInfo(Ljava/lang/String;I)Landroid/content/pm/PackageInfo;

    move-result-object p0

    .line 122
    iget-object p0, p0, Landroid/content/pm/PackageInfo;->applicationInfo:Landroid/content/pm/ApplicationInfo;

    iget p0, p0, Landroid/content/pm/ApplicationInfo;->flags:I
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    const/4 p1, 0x1

    and-int/2addr p0, p1

    if-eqz p0, :cond_0

    move v0, p1

    :cond_0
    return v0

    :catch_0
    move-exception p0

    .line 124
    sget-object p1, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->TAG:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "isPackageInternal Exception e: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {p1, p0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    return v0
.end method


# virtual methods
.method protected getAvailableCode(Landroid/content/Context;I)I
    .locals 1

    .line 47
    new-instance v0, Lcom/huawei/hms/adapter/AvailableAdapter;

    invoke-direct {v0, p2}, Lcom/huawei/hms/adapter/AvailableAdapter;-><init>(I)V

    .line 48
    invoke-virtual {v0, p1}, Lcom/huawei/hms/adapter/AvailableAdapter;->isHuaweiMobileServicesAvailable(Landroid/content/Context;)I

    move-result p1

    return p1
.end method

.method public isHmsAvailable(Landroid/content/Context;I)Z
    .locals 0

    .line 34
    invoke-virtual {p0, p1, p2}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->getAvailableCode(Landroid/content/Context;I)I

    move-result p1

    if-nez p1, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method public isHmsInstalled()Z
    .locals 1

    .line 78
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isHmsInstalled:Z

    return v0
.end method

.method public isHmsNeededUpdate(Landroid/content/Context;I)Z
    .locals 0

    .line 43
    invoke-virtual {p0, p1, p2}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->getAvailableCode(Landroid/content/Context;I)I

    move-result p1

    const/4 p2, 0x2

    if-ne p1, p2, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public notifyDownloadHmsIfNeeded(Landroid/content/Context;I)V
    .locals 1

    .line 52
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isUpdatePopupShown:Z

    if-eqz v0, :cond_0

    return-void

    .line 57
    :cond_0
    new-instance v0, Lcom/huawei/hms/adapter/AvailableAdapter;

    invoke-direct {v0, p2}, Lcom/huawei/hms/adapter/AvailableAdapter;-><init>(I)V

    .line 58
    invoke-virtual {v0, p1}, Lcom/huawei/hms/adapter/AvailableAdapter;->isHuaweiMobileServicesAvailable(Landroid/content/Context;)I

    move-result p1

    if-nez p1, :cond_1

    return-void

    .line 63
    :cond_1
    invoke-virtual {v0, p1}, Lcom/huawei/hms/adapter/AvailableAdapter;->isUserResolvableError(I)Z

    move-result p1

    if-nez p1, :cond_2

    return-void

    .line 67
    :cond_2
    sget-object p1, Lcom/huawei/hms/ml/common/utils/ActivityMgr;->INST:Lcom/huawei/hms/ml/common/utils/ActivityMgr;

    invoke-virtual {p1}, Lcom/huawei/hms/ml/common/utils/ActivityMgr;->getCurrentActivity()Landroid/app/Activity;

    move-result-object p1

    if-eqz p1, :cond_3

    .line 70
    invoke-virtual {p1}, Landroid/app/Activity;->isDestroyed()Z

    move-result p2

    if-nez p2, :cond_3

    const/4 p2, 0x1

    .line 71
    iput-boolean p2, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isUpdatePopupShown:Z

    .line 73
    iget-object p2, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->callBack:Lcom/huawei/hms/adapter/AvailableAdapter$AvailableCallBack;

    invoke-virtual {v0, p1, p2}, Lcom/huawei/hms/adapter/AvailableAdapter;->startResolution(Landroid/app/Activity;Lcom/huawei/hms/adapter/AvailableAdapter$AvailableCallBack;)V

    :cond_3
    return-void
.end method

.method public release(Landroid/content/Context;)V
    .locals 0

    return-void
.end method

.method public resetStatus()V
    .locals 1

    const/4 v0, 0x0

    .line 91
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->isHmsInstalled:Z

    return-void
.end method
