.class public Lcom/huawei/hms/mlsdk/text/TextLanguage;
.super Ljava/lang/Object;
.source "TextLanguage.java"


# instance fields
.field private final language:Ljava/lang/String;


# direct methods
.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/TextLanguage;->language:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 3

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/huawei/hms/mlsdk/text/TextLanguage;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 4
    :cond_1
    check-cast p1, Lcom/huawei/hms/mlsdk/text/TextLanguage;

    .line 5
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/TextLanguage;->language:Ljava/lang/String;

    if-nez v1, :cond_3

    .line 6
    iget-object p1, p1, Lcom/huawei/hms/mlsdk/text/TextLanguage;->language:Ljava/lang/String;

    if-nez p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0

    .line 8
    :cond_3
    iget-object p1, p1, Lcom/huawei/hms/mlsdk/text/TextLanguage;->language:Ljava/lang/String;

    invoke-virtual {v1, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    return p1
.end method

.method public getLanguage()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/TextLanguage;->language:Ljava/lang/String;

    return-object v0
.end method

.method public hashCode()I
    .locals 3

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    .line 1
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/TextLanguage;->language:Ljava/lang/String;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->hashCode([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method
