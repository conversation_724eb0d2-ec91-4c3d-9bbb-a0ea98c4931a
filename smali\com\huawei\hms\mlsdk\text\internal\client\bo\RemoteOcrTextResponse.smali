.class public Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;
.super Ljava/lang/Object;
.source "RemoteOcrTextResponse.java"


# instance fields
.field private requestId:Ljava/lang/String;

.field private retCode:Ljava/lang/String;

.field private retMsg:Ljava/lang/String;

.field private texts:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;->texts:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public getRequestId()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;->requestId:Ljava/lang/String;

    return-object v0
.end method

.method public getResponses()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;->texts:Ljava/util/List;

    return-object v0
.end method

.method public getRetCode()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;->retCode:Ljava/lang/String;

    return-object v0
.end method

.method public getRetMsg()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;->retMsg:Ljava/lang/String;

    return-object v0
.end method

.method public setRequestId(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;->requestId:Ljava/lang/String;

    return-void
.end method

.method public setResponses(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;->texts:Ljava/util/List;

    return-void
.end method

.method public setRetCode(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;->retCode:Ljava/lang/String;

    return-void
.end method

.method public setRetMsg(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;->retMsg:Ljava/lang/String;

    return-void
.end method
