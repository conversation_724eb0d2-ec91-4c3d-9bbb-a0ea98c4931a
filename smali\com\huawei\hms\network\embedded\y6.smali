.class public interface abstract Lcom/huawei/hms/network/embedded/y6;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final a:Lcom/huawei/hms/network/embedded/y6;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/huawei/hms/network/embedded/y6$a;

    invoke-direct {v0}, Lcom/huawei/hms/network/embedded/y6$a;-><init>()V

    sput-object v0, Lcom/huawei/hms/network/embedded/y6;->a:Lcom/huawei/hms/network/embedded/y6;

    return-void
.end method


# virtual methods
.method public abstract a(Lcom/huawei/hms/network/embedded/i7;)Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/network/embedded/i7;",
            ")",
            "Ljava/util/List<",
            "Lcom/huawei/hms/network/embedded/x6;",
            ">;"
        }
    .end annotation
.end method

.method public abstract a(Lcom/huawei/hms/network/embedded/i7;Ljava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/network/embedded/i7;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/network/embedded/x6;",
            ">;)V"
        }
    .end annotation
.end method
