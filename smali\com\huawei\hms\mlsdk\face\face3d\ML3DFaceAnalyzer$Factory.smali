.class public Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer$Factory;
.super Ljava/lang/Object;
.source "ML3DFaceAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Factory"
.end annotation

.annotation runtime Ljava/lang/Deprecated;
.end annotation


# instance fields
.field private factory:Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;

.field private final mContext:Landroid/content/Context;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;

    .line 11
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer$Factory;->mContext:Landroid/content/Context;

    return-void
.end method


# virtual methods
.method public create()Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer;
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;->create()Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;

    move-result-object v0

    .line 2
    invoke-static {}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->getInstance()Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;

    move-result-object v1

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer$Factory;->mContext:Landroid/content/Context;

    invoke-virtual {v1, v2}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->prepare(Landroid/content/Context;)V

    .line 3
    new-instance v1, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer;

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer$Factory;->mContext:Landroid/content/Context;

    const/4 v3, 0x0

    invoke-direct {v1, v2, v0, v3}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer;-><init>(Landroid/content/Context;Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer$1;)V

    return-object v1
.end method

.method public setPerformanceType(I)Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer$Factory;
    .locals 3

    const/4 v0, 0x1

    if-eq p1, v0, :cond_1

    const/4 v0, 0x2

    if-ne p1, v0, :cond_0

    goto :goto_0

    .line 1
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Invalid performance type: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 2
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;->setPerformanceType(I)Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;

    return-object p0
.end method

.method public setTracingAllowed(Z)Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer$Factory;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;->setTracingAllowed(Z)Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;

    return-object p0
.end method
