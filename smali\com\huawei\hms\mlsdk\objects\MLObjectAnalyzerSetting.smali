.class public Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;
.super Ljava/lang/Object;
.source "MLObjectAnalyzerSetting.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;
    }
.end annotation


# static fields
.field public static final TYPE_PICTURE:I = 0x0

.field public static final TYPE_VIDEO:I = 0x1


# instance fields
.field private final analyzerType:I

.field private final isClassificationAllowed:Z

.field private final isMultipleResultsAllowed:Z


# direct methods
.method private constructor <init>(IZZ)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput p1, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->analyzerType:I

    .line 4
    iput-boolean p2, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isMultipleResultsAllowed:Z

    .line 5
    iput-boolean p3, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isClassificationAllowed:Z

    return-void
.end method

.method synthetic constructor <init>(IZZLcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$1;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;-><init>(IZZ)V

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 3

    .line 1
    instance-of v0, p1, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 4
    :cond_0
    check-cast p1, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    .line 5
    iget v0, p1, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->analyzerType:I

    iget v2, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->analyzerType:I

    if-ne v0, v2, :cond_1

    iget-boolean v0, p1, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isClassificationAllowed:Z

    iget-boolean v2, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isClassificationAllowed:Z

    if-ne v0, v2, :cond_1

    iget-boolean p1, p1, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isMultipleResultsAllowed:Z

    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isMultipleResultsAllowed:Z

    if-ne p1, v0, :cond_1

    const/4 v1, 0x1

    :cond_1
    return v1
.end method

.method public final getAnalyzerType()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->analyzerType:I

    return v0
.end method

.method public hashCode()I
    .locals 3

    const/4 v0, 0x3

    new-array v0, v0, [Ljava/lang/Object;

    .line 1
    iget v1, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->analyzerType:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isClassificationAllowed:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isMultipleResultsAllowed:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    invoke-static {v0}, Ljava/util/Arrays;->hashCode([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public final isClassificationAllowed()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isClassificationAllowed:Z

    return v0
.end method

.method public final isMultipleResultsAllowed()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isMultipleResultsAllowed:Z

    return v0
.end method
