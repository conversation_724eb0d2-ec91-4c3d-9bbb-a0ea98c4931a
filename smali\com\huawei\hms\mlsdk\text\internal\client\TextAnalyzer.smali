.class public Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;
.super Ljava/lang/Object;
.source "TextAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer$Holder;
    }
.end annotation


# static fields
.field private static final TAG:Ljava/lang/String; = "TextAnalyzer"


# instance fields
.field private initialed:Z


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method private constructor <init>()V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 3
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->initialed:Z

    return-void
.end method

.method synthetic constructor <init>(Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer$1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;-><init>()V

    return-void
.end method

.method public static getInstance()Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;
    .locals 1

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer$Holder;->INSTANCE:Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;

    return-object v0
.end method

.method private notifyDownloadIfNeeded(Landroid/content/Context;)V
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    .line 2
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/e;->a()Lcom/huawei/hms/mlsdk/mlvision/e;

    move-result-object v1

    .line 3
    invoke-virtual {v0, p1, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->notifyDownloadIfNeeded(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)V

    return-void
.end method


# virtual methods
.method public declared-synchronized destroy()I
    .locals 5

    monitor-enter p0

    .line 1
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/e;->a()Lcom/huawei/hms/mlsdk/mlvision/e;

    move-result-object v0

    .line 2
    invoke-interface {v0}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 v1, -0x1

    if-nez v0, :cond_0

    monitor-exit p0

    return v1

    .line 9
    :cond_0
    :try_start_1
    check-cast v0, Lcom/huawei/hms/ml/common/ocr/IRemoteTextRecognizerDelegate;

    invoke-interface {v0}, Lcom/huawei/hms/ml/common/ocr/IRemoteTextRecognizerDelegate;->unloadModel()I

    move-result v0
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return v0

    :catch_0
    move-exception v0

    .line 12
    :try_start_2
    sget-object v2, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->TAG:Ljava/lang/String;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "destroy Exception e: "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v2, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    monitor-exit p0

    return v1

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public declared-synchronized initial(Landroid/content/Context;Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;)I
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    monitor-enter p0

    .line 1
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/e;->a()Lcom/huawei/hms/mlsdk/mlvision/e;

    move-result-object p1

    .line 2
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    const/4 v1, -0x1

    if-nez v0, :cond_0

    monitor-exit p0

    return v1

    .line 9
    :cond_0
    :try_start_1
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicContext()Landroid/content/Context;

    move-result-object p1

    .line 10
    check-cast v0, Lcom/huawei/hms/ml/common/ocr/IRemoteTextRecognizerDelegate;

    invoke-static {p1}, Lcom/huawei/hms/feature/dynamic/ObjectWrapper;->wrap(Ljava/lang/Object;)Lcom/huawei/hms/feature/dynamic/IObjectWrapper;

    move-result-object p1

    invoke-interface {v0, p1, p2}, Lcom/huawei/hms/ml/common/ocr/IRemoteTextRecognizerDelegate;->initial(Lcom/huawei/hms/feature/dynamic/IObjectWrapper;Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;)I

    move-result p1

    if-nez p1, :cond_1

    const/4 p2, 0x1

    .line 12
    iput-boolean p2, p0, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->initialed:Z
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :cond_1
    monitor-exit p0

    return p1

    :catchall_0
    move-exception p1

    .line 18
    :try_start_2
    sget-object p2, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->TAG:Ljava/lang/String;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "initial Throwable e: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :catch_0
    move-exception p1

    .line 19
    sget-object p2, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->TAG:Ljava/lang/String;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "initial Exception e: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :goto_0
    monitor-exit p0

    return v1

    :catchall_1
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public isAvailable(Landroid/content/Context;)Z
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    .line 2
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/e;->a()Lcom/huawei/hms/mlsdk/mlvision/e;

    move-result-object v1

    .line 3
    invoke-virtual {v0, p1, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->isAvailable(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)Z

    move-result p1

    return p1
.end method

.method public declared-synchronized prepare(Landroid/content/Context;)V
    .locals 1

    monitor-enter p0

    .line 1
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/e;->a()Lcom/huawei/hms/mlsdk/mlvision/e;

    move-result-object v0

    .line 2
    invoke-interface {v0, p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->initial(Landroid/content/Context;)V

    .line 3
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->notifyDownloadIfNeeded(Landroid/content/Context;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized recognizeWithBitmap(Landroid/content/Context;Landroid/os/Bundle;Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;)Lcom/huawei/hms/ml/common/ocr/TextParcel;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    monitor-enter p0

    const/4 v0, 0x0

    .line 1
    :try_start_0
    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->initialed:Z

    if-nez v1, :cond_0

    invoke-virtual {p0, p1, p4}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->initial(Landroid/content/Context;Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;)I

    move-result p1

    if-ltz p1, :cond_0

    const/4 p1, 0x1

    .line 2
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->initialed:Z

    .line 5
    :cond_0
    iget-boolean p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->initialed:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez p1, :cond_1

    monitor-exit p0

    return-object v0

    .line 6
    :cond_1
    :try_start_1
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/e;->a()Lcom/huawei/hms/mlsdk/mlvision/e;

    move-result-object p1

    .line 7
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object p1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-nez p1, :cond_2

    monitor-exit p0

    return-object v0

    .line 14
    :cond_2
    :try_start_2
    check-cast p1, Lcom/huawei/hms/ml/common/ocr/IRemoteTextRecognizerDelegate;

    invoke-interface {p1, p2, p3, p4}, Lcom/huawei/hms/ml/common/ocr/IRemoteTextRecognizerDelegate;->detect(Landroid/os/Bundle;Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;)Lcom/huawei/hms/ml/common/ocr/TextParcel;

    move-result-object p1
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    monitor-exit p0

    return-object p1

    :catch_0
    move-exception p1

    .line 16
    :try_start_3
    sget-object p2, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->TAG:Ljava/lang/String;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string p4, "recognizeWithBitmap Exception e: "

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized release(Landroid/content/Context;)V
    .locals 1

    monitor-enter p0

    .line 1
    :try_start_0
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->initialed:Z

    if-eqz v0, :cond_0

    .line 2
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->destroy()I

    const/4 v0, 0x0

    .line 3
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->initialed:Z

    .line 6
    :cond_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->release(Landroid/content/Context;)V

    .line 7
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/e;->a()Lcom/huawei/hms/mlsdk/mlvision/e;

    move-result-object p1

    .line 8
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->release()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method
