.class public Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;
.super Ljava/lang/Object;
.source "ML3DFace.java"


# static fields
.field private static final FIVE_KEY_POINTS_INDEX:[I

.field public static final LANDMARK_FIVE:I


# instance fields
.field private all3dvertexs:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;"
        }
    .end annotation
.end field

.field private border:Landroid/graphics/Rect;

.field private coordinatePoint:Landroid/graphics/PointF;

.field private face3DEulerX:F

.field private face3DEulerY:F

.field private face3DEulerZ:F

.field private face3DKeypoints:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;"
        }
    .end annotation
.end field

.field private face3DParams:[F

.field private height:F

.field private mBaseObj:Ljava/lang/String;

.field private trackingId:I

.field private width:F


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const/4 v0, 0x7

    new-array v0, v0, [I

    .line 1
    fill-array-data v0, :array_0

    sput-object v0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->FIVE_KEY_POINTS_INDEX:[I

    return-void

    nop

    :array_0
    .array-data 4
        0x24
        0x27
        0x2a
        0x2d
        0x1e
        0x30
        0x36
    .end array-data
.end method

.method public constructor <init>(ILandroid/graphics/PointF;FFFFFLjava/util/List;Ljava/util/List;[FLjava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Landroid/graphics/PointF;",
            "FFFFF",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;[F",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->trackingId:I

    .line 3
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->coordinatePoint:Landroid/graphics/PointF;

    .line 4
    iput p3, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->width:F

    .line 5
    iput p4, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->height:F

    .line 6
    iput p5, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DEulerX:F

    .line 7
    iput p6, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DEulerY:F

    .line 8
    iput p7, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DEulerZ:F

    .line 9
    iput-object p8, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->all3dvertexs:Ljava/util/List;

    .line 10
    iput-object p9, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DKeypoints:Ljava/util/List;

    .line 11
    iput-object p10, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DParams:[F

    .line 12
    iput-object p11, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->mBaseObj:Ljava/lang/String;

    .line 13
    new-instance p1, Landroid/graphics/Rect;

    iget-object p2, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->coordinatePoint:Landroid/graphics/PointF;

    iget p5, p2, Landroid/graphics/PointF;->x:F

    float-to-int p6, p5

    iget p2, p2, Landroid/graphics/PointF;->y:F

    float-to-int p7, p2

    add-float/2addr p5, p3

    float-to-int p3, p5

    add-float/2addr p2, p4

    float-to-int p2, p2

    invoke-direct {p1, p6, p7, p3, p2}, Landroid/graphics/Rect;-><init>(IIII)V

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->border:Landroid/graphics/Rect;

    return-void
.end method

.method private getFiveKeypoint()Ljava/util/List;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DKeypoints:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object v0

    return-object v0

    .line 4
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 6
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DKeypoints:Ljava/util/List;

    sget-object v2, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->FIVE_KEY_POINTS_INDEX:[I

    const/4 v3, 0x0

    aget v3, v2, v3

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/mlsdk/common/MLPosition;

    .line 7
    iget-object v3, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DKeypoints:Ljava/util/List;

    const/4 v4, 0x1

    aget v4, v2, v4

    invoke-interface {v3, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/huawei/hms/mlsdk/common/MLPosition;

    .line 8
    new-instance v4, Lcom/huawei/hms/mlsdk/common/MLPosition;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getX()Ljava/lang/Float;

    move-result-object v5

    invoke-virtual {v5}, Ljava/lang/Float;->floatValue()F

    move-result v5

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getX()Ljava/lang/Float;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/Float;->floatValue()F

    move-result v6

    add-float/2addr v6, v5

    const/high16 v5, 0x40000000    # 2.0f

    div-float/2addr v6, v5

    invoke-static {v6}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v6

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getY()Ljava/lang/Float;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/Float;->floatValue()F

    move-result v7

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getY()Ljava/lang/Float;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/Float;->floatValue()F

    move-result v8

    add-float/2addr v8, v7

    div-float/2addr v8, v5

    invoke-static {v8}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v7

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getZ()Ljava/lang/Float;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    move-result v1

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getZ()Ljava/lang/Float;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Float;->floatValue()F

    move-result v3

    add-float/2addr v3, v1

    div-float/2addr v3, v5

    invoke-static {v3}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    invoke-direct {v4, v6, v7, v1}, Lcom/huawei/hms/mlsdk/common/MLPosition;-><init>(Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/Float;)V

    invoke-interface {v0, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 10
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DKeypoints:Ljava/util/List;

    const/4 v3, 0x2

    aget v3, v2, v3

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/mlsdk/common/MLPosition;

    .line 11
    iget-object v3, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DKeypoints:Ljava/util/List;

    const/4 v4, 0x3

    aget v4, v2, v4

    invoke-interface {v3, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/huawei/hms/mlsdk/common/MLPosition;

    .line 12
    new-instance v4, Lcom/huawei/hms/mlsdk/common/MLPosition;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getX()Ljava/lang/Float;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/Float;->floatValue()F

    move-result v6

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getX()Ljava/lang/Float;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/Float;->floatValue()F

    move-result v7

    add-float/2addr v7, v6

    div-float/2addr v7, v5

    invoke-static {v7}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v6

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getY()Ljava/lang/Float;

    move-result-object v7

    invoke-virtual {v7}, Ljava/lang/Float;->floatValue()F

    move-result v7

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getY()Ljava/lang/Float;

    move-result-object v8

    invoke-virtual {v8}, Ljava/lang/Float;->floatValue()F

    move-result v8

    add-float/2addr v8, v7

    div-float/2addr v8, v5

    invoke-static {v8}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v7

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getZ()Ljava/lang/Float;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    move-result v1

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getZ()Ljava/lang/Float;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Float;->floatValue()F

    move-result v3

    add-float/2addr v3, v1

    div-float/2addr v3, v5

    invoke-static {v3}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    invoke-direct {v4, v6, v7, v1}, Lcom/huawei/hms/mlsdk/common/MLPosition;-><init>(Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/Float;)V

    invoke-interface {v0, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 14
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DKeypoints:Ljava/util/List;

    const/4 v3, 0x4

    aget v3, v2, v3

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 16
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DKeypoints:Ljava/util/List;

    const/4 v3, 0x5

    aget v3, v2, v3

    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 18
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DKeypoints:Ljava/util/List;

    const/4 v3, 0x6

    aget v2, v2, v3

    invoke-interface {v1, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object v0
.end method


# virtual methods
.method public get3DAllVertexs()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->all3dvertexs:Ljava/util/List;

    if-nez v0, :cond_0

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    :cond_0
    return-object v0
.end method

.method public get3DFaceEulerX()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DEulerX:F

    return v0
.end method

.method public get3DFaceEulerY()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DEulerY:F

    return v0
.end method

.method public get3DFaceEulerZ()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DEulerZ:F

    return v0
.end method

.method public get3DKeyPoints(I)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;"
        }
    .end annotation

    if-eqz p1, :cond_0

    .line 1
    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    return-object p1

    .line 2
    :cond_0
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->getFiveKeypoint()Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public get3DProjectionMatrix([FFF)V
    .locals 7

    sub-float v0, p2, p3

    float-to-double v0, v0

    const-wide/high16 v2, 0x3ff0000000000000L    # 1.0

    div-double/2addr v2, v0

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DParams:[F

    const/16 v1, 0x10

    aget v1, v0, v1

    const/4 v4, 0x0

    aput v1, p1, v4

    const/16 v1, 0x11

    .line 2
    aget v1, v0, v1

    const/4 v4, 0x1

    aput v1, p1, v4

    const/16 v1, 0x12

    .line 3
    aget v1, v0, v1

    const/4 v4, 0x2

    aput v1, p1, v4

    const/4 v1, 0x3

    const/4 v4, 0x0

    .line 4
    aput v4, p1, v1

    const/16 v1, 0x13

    .line 6
    aget v1, v0, v1

    const/4 v5, 0x4

    aput v1, p1, v5

    const/16 v1, 0x14

    .line 7
    aget v1, v0, v1

    const/4 v5, 0x5

    aput v1, p1, v5

    const/16 v1, 0x15

    .line 8
    aget v0, v0, v1

    const/4 v1, 0x6

    aput v0, p1, v1

    const/4 v0, 0x7

    .line 9
    aput v4, p1, v0

    const/16 v0, 0x8

    .line 11
    aput v4, p1, v0

    const/16 v0, 0x9

    .line 12
    aput v4, p1, v0

    add-float v0, p3, p2

    float-to-double v0, v0

    mul-double/2addr v0, v2

    double-to-float v0, v0

    const/16 v1, 0xa

    .line 13
    aput v0, p1, v1

    float-to-double v0, p3

    const-wide/high16 v5, 0x4000000000000000L    # 2.0

    mul-double/2addr v0, v5

    float-to-double p2, p2

    mul-double/2addr v0, p2

    mul-double/2addr v0, v2

    double-to-float p2, v0

    const/16 p3, 0xb

    .line 14
    aput p2, p1, p3

    const/16 p2, 0xc

    .line 16
    aput v4, p1, p2

    const/16 p2, 0xd

    .line 17
    aput v4, p1, p2

    const/16 p2, 0xe

    const/high16 p3, -0x40800000    # -1.0f

    .line 18
    aput p3, p1, p2

    const/16 p2, 0xf

    .line 19
    aput v4, p1, p2

    return-void
.end method

.method public get3DViewMatrix([F)V
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DParams:[F

    const/16 v1, 0x19

    aget v1, v0, v1

    const/4 v2, 0x0

    aput v1, p1, v2

    const/16 v1, 0x1a

    .line 2
    aget v1, v0, v1

    const/4 v2, 0x1

    aput v1, p1, v2

    const/16 v1, 0x1b

    .line 3
    aget v1, v0, v1

    const/4 v2, 0x2

    aput v1, p1, v2

    const/16 v1, 0x22

    .line 4
    aget v1, v0, v1

    const/4 v2, 0x3

    aput v1, p1, v2

    const/16 v1, 0x1c

    .line 6
    aget v1, v0, v1

    const/4 v2, 0x4

    aput v1, p1, v2

    const/16 v1, 0x1d

    .line 7
    aget v1, v0, v1

    const/4 v2, 0x5

    aput v1, p1, v2

    const/16 v1, 0x1e

    .line 8
    aget v1, v0, v1

    const/4 v2, 0x6

    aput v1, p1, v2

    const/16 v1, 0x23

    .line 9
    aget v1, v0, v1

    const/4 v2, 0x7

    aput v1, p1, v2

    const/16 v1, 0x1f

    .line 11
    aget v1, v0, v1

    const/16 v2, 0x8

    aput v1, p1, v2

    const/16 v1, 0x20

    .line 12
    aget v1, v0, v1

    const/16 v2, 0x9

    aput v1, p1, v2

    const/16 v1, 0x21

    .line 13
    aget v1, v0, v1

    const/16 v2, 0xa

    aput v1, p1, v2

    const/16 v1, 0x24

    .line 14
    aget v0, v0, v1

    const/16 v1, 0xb

    aput v0, p1, v1

    const/16 v0, 0xc

    const/4 v1, 0x0

    .line 16
    aput v1, p1, v0

    const/16 v0, 0xd

    .line 17
    aput v1, p1, v0

    const/16 v0, 0xe

    .line 18
    aput v1, p1, v0

    const/16 v0, 0xf

    const/high16 v1, 0x3f800000    # 1.0f

    .line 19
    aput v1, p1, v0

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 1
    invoke-static {p0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->toStringHelper(Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->border:Landroid/graphics/Rect;

    const-string v2, "boundingBox"

    .line 2
    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->trackingId:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "trackingId"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DEulerX:F

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "face3DEulerX"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DEulerY:F

    .line 3
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "face3DEulerY"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DEulerZ:F

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "face3DEulerZ"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->all3dvertexs:Ljava/util/List;

    const-string v2, "all3dvertexs"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;->face3DKeypoints:Ljava/util/List;

    const-string v2, "face3DKeypoints"

    .line 4
    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
