.class public Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$Factory;
.super Ljava/lang/Object;
.source "MLTextAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Factory"
.end annotation


# instance fields
.field private localTextSettingFactory:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;

.field private mContext:Landroid/content/Context;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$Factory;->localTextSettingFactory:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;

    .line 10
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$Factory;->mContext:Landroid/content/Context;

    return-void
.end method


# virtual methods
.method public create()Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;
    .locals 3

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$Factory;->mContext:Landroid/content/Context;

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$Factory;->localTextSettingFactory:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->create()Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    move-result-object v2

    invoke-direct {v0, v1, v2}, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;-><init>(Landroid/content/Context;Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;)V

    .line 2
    new-instance v1, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;

    const/4 v2, 0x0

    invoke-direct {v1, v0, v2}, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;-><init>(Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$1;)V

    return-object v1
.end method

.method public setLanguage(Ljava/lang/String;)Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$Factory;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$Factory;->localTextSettingFactory:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->setLanguage(Ljava/lang/String;)Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;

    return-object p0
.end method

.method public setLocalOCRMode(I)Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$Factory;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$Factory;->localTextSettingFactory:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->setOCRMode(I)Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;

    return-object p0
.end method
