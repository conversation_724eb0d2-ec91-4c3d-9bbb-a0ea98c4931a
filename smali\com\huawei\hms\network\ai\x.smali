.class public Lcom/huawei/hms/network/ai/x;
.super Ljava/lang/Object;
.source ""


# instance fields
.field public a:Ljava/lang/String;

.field public b:Ljava/lang/String;

.field public c:I

.field public d:I

.field public e:I

.field public f:I

.field public g:I

.field public h:I

.field public i:Ljava/lang/String;

.field public j:I

.field public k:Z

.field public l:Z

.field public m:I

.field public n:I

.field public o:I

.field public p:I

.field public q:I


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/x;->c:I

    return v0
.end method

.method public a(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/x;->c:I

    return-void
.end method

.method public a(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, <PERSON><PERSON>/huawei/hms/network/ai/x;->b:Ljava/lang/String;

    return-void
.end method

.method public a(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/huawei/hms/network/ai/x;->l:Z

    return-void
.end method

.method public b()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/x;->n:I

    return v0
.end method

.method public b(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/x;->n:I

    return-void
.end method

.method public b(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/huawei/hms/network/ai/x;->a:Ljava/lang/String;

    return-void
.end method

.method public b(Z)V
    .locals 0

    iput-boolean p1, p0, Lcom/huawei/hms/network/ai/x;->k:Z

    return-void
.end method

.method public c()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/x;->o:I

    return v0
.end method

.method public c(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/x;->o:I

    return-void
.end method

.method public c(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/huawei/hms/network/ai/x;->i:Ljava/lang/String;

    return-void
.end method

.method public d()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/x;->p:I

    return v0
.end method

.method public d(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/x;->p:I

    return-void
.end method

.method public e()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/x;->q:I

    return v0
.end method

.method public e(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/x;->q:I

    return-void
.end method

.method public f()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/ai/x;->b:Ljava/lang/String;

    return-object v0
.end method

.method public f(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/x;->m:I

    return-void
.end method

.method public g()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/x;->m:I

    return v0
.end method

.method public g(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/x;->g:I

    return-void
.end method

.method public h()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/ai/x;->a:Ljava/lang/String;

    return-object v0
.end method

.method public h(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/x;->h:I

    return-void
.end method

.method public i()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/x;->g:I

    return v0
.end method

.method public i(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/x;->j:I

    return-void
.end method

.method public j()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/x;->h:I

    return v0
.end method

.method public j(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/x;->d:I

    return-void
.end method

.method public k()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/x;->j:I

    return v0
.end method

.method public k(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/x;->e:I

    return-void
.end method

.method public l()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/x;->d:I

    return v0
.end method

.method public l(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/x;->f:I

    return-void
.end method

.method public m()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/ai/x;->i:Ljava/lang/String;

    return-object v0
.end method

.method public n()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/x;->e:I

    return v0
.end method

.method public o()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/x;->f:I

    return v0
.end method

.method public p()Z
    .locals 1

    iget-boolean v0, p0, Lcom/huawei/hms/network/ai/x;->l:Z

    return v0
.end method

.method public q()Z
    .locals 1

    iget-boolean v0, p0, Lcom/huawei/hms/network/ai/x;->k:Z

    return v0
.end method
