.class public Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;
.super Ljava/lang/Object;
.source "MLAnalyzerFactory.java"


# static fields
.field private static face3dAnalyzerSetting:Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;

.field private static faceAnalyzerSetting:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

.field private static fileConfig:Lcom/huawei/hms/mlsdk/document/MLDocumentSetting;

.field private static imgsegSetting:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

.field private static localClassificationSetting:Lcom/huawei/hms/mlsdk/classification/MLLocalClassificationAnalyzerSetting;

.field private static localTextSetting:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

.field private static objectSetting:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

.field private static remoteAnalyzerSetting:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

.field private static remoteClassificationSetting:Lcom/huawei/hms/mlsdk/classification/MLRemoteClassificationAnalyzerSetting;

.field private static remoteProductVisionSearchSetting:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;

.field private static remoteTextSetting:Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;


# instance fields
.field private final application:Lcom/huawei/hms/mlsdk/common/MLApplication;

.field private final spStore:Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;->create()Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

    move-result-object v0

    sput-object v0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->remoteAnalyzerSetting:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

    .line 3
    new-instance v0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->create()Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    move-result-object v0

    sput-object v0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->faceAnalyzerSetting:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    .line 5
    new-instance v0, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$Factory;->create()Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;

    move-result-object v0

    sput-object v0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->face3dAnalyzerSetting:Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;

    .line 7
    new-instance v0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->create()Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    move-result-object v0

    sput-object v0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->localTextSetting:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    .line 9
    new-instance v0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->create()Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    move-result-object v0

    sput-object v0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->remoteTextSetting:Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    .line 11
    new-instance v0, Lcom/huawei/hms/mlsdk/document/MLDocumentSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/document/MLDocumentSetting$Factory;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/document/MLDocumentSetting$Factory;->create()Lcom/huawei/hms/mlsdk/document/MLDocumentSetting;

    move-result-object v0

    sput-object v0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->fileConfig:Lcom/huawei/hms/mlsdk/document/MLDocumentSetting;

    .line 13
    new-instance v0, Lcom/huawei/hms/mlsdk/classification/MLLocalClassificationAnalyzerSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/classification/MLLocalClassificationAnalyzerSetting$Factory;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/classification/MLLocalClassificationAnalyzerSetting$Factory;->create()Lcom/huawei/hms/mlsdk/classification/MLLocalClassificationAnalyzerSetting;

    move-result-object v0

    sput-object v0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->localClassificationSetting:Lcom/huawei/hms/mlsdk/classification/MLLocalClassificationAnalyzerSetting;

    .line 15
    new-instance v0, Lcom/huawei/hms/mlsdk/classification/MLRemoteClassificationAnalyzerSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/classification/MLRemoteClassificationAnalyzerSetting$Factory;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/classification/MLRemoteClassificationAnalyzerSetting$Factory;->create()Lcom/huawei/hms/mlsdk/classification/MLRemoteClassificationAnalyzerSetting;

    move-result-object v0

    sput-object v0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->remoteClassificationSetting:Lcom/huawei/hms/mlsdk/classification/MLRemoteClassificationAnalyzerSetting;

    .line 17
    new-instance v0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting$Factory;->create()Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    move-result-object v0

    sput-object v0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->objectSetting:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    .line 19
    new-instance v0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;->create()Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    move-result-object v0

    sput-object v0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->imgsegSetting:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    .line 21
    new-instance v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;-><init>()V

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->create()Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;

    move-result-object v0

    sput-object v0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->remoteProductVisionSearchSetting:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;

    return-void
.end method

.method constructor <init>(Lcom/huawei/hms/mlsdk/common/MLApplication;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    .line 3
    invoke-static {p1}, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->getAppStore(Lcom/huawei/hms/mlsdk/common/MLApplication;)Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;

    move-result-object p1

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->spStore:Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;

    return-void
.end method

.method public static getInstance()Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;
    .locals 1

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getInstance()Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v0

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->getInstance(Lcom/huawei/hms/mlsdk/common/MLApplication;)Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;

    move-result-object v0

    return-object v0
.end method

.method public static getInstance(Lcom/huawei/hms/mlsdk/common/MLApplication;)Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;
    .locals 1

    .line 2
    new-instance v0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;

    invoke-direct {v0, p0}, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;)V

    return-object v0
.end method


# virtual methods
.method public get3DFaceAnalyzer()Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer;
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    sget-object v1, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->face3dAnalyzerSetting:Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;)Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer;

    move-result-object v0

    return-object v0
.end method

.method public get3DFaceAnalyzer(Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;)Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-static {v0, p1}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;)Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzer;

    move-result-object p1

    return-object p1
.end method

.method public getFaceAnalyzer()Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    sget-object v1, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->faceAnalyzerSetting:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;

    move-result-object v0

    return-object v0
.end method

.method public getFaceAnalyzer(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-static {v0, p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;

    move-result-object p1

    return-object p1
.end method

.method public getImageSegmentationAnalyzer()Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    sget-object v1, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->imgsegSetting:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;)Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;

    move-result-object v0

    return-object v0
.end method

.method public getImageSegmentationAnalyzer(Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;)Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-static {v0, p1}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;)Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;

    move-result-object p1

    return-object p1
.end method

.method public getLocalImageClassificationAnalyzer()Lcom/huawei/hms/mlsdk/classification/MLImageClassificationAnalyzer;
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    sget-object v1, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->localClassificationSetting:Lcom/huawei/hms/mlsdk/classification/MLLocalClassificationAnalyzerSetting;

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/classification/MLImageClassificationAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/classification/MLLocalClassificationAnalyzerSetting;)Lcom/huawei/hms/mlsdk/classification/MLImageClassificationAnalyzer;

    move-result-object v0

    return-object v0
.end method

.method public getLocalImageClassificationAnalyzer(Lcom/huawei/hms/mlsdk/classification/MLLocalClassificationAnalyzerSetting;)Lcom/huawei/hms/mlsdk/classification/MLImageClassificationAnalyzer;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-static {v0, p1}, Lcom/huawei/hms/mlsdk/classification/MLImageClassificationAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/classification/MLLocalClassificationAnalyzerSetting;)Lcom/huawei/hms/mlsdk/classification/MLImageClassificationAnalyzer;

    move-result-object p1

    return-object p1
.end method

.method public getLocalObjectAnalyzer()Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    sget-object v1, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->objectSetting:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;)Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;

    move-result-object v0

    return-object v0
.end method

.method public getLocalObjectAnalyzer(Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;)Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-static {v0, p1}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;)Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;

    move-result-object p1

    return-object p1
.end method

.method public getLocalTextAnalyzer()Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    sget-object v1, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->localTextSetting:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    const/4 v2, 0x0

    const/4 v3, 0x1

    invoke-static {v0, v1, v2, v3}, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;Z)Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;

    move-result-object v0

    return-object v0
.end method

.method public getLocalTextAnalyzer(Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;)Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;
    .locals 3

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-static {v0, p1, v1, v2}, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;Z)Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;

    move-result-object p1

    return-object p1
.end method

.method public getRemoteDocumentAnalyzer()Lcom/huawei/hms/mlsdk/document/MLDocumentAnalyzer;
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    sget-object v1, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->fileConfig:Lcom/huawei/hms/mlsdk/document/MLDocumentSetting;

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/document/MLDocumentAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/document/MLDocumentSetting;)Lcom/huawei/hms/mlsdk/document/MLDocumentAnalyzer;

    move-result-object v0

    return-object v0
.end method

.method public getRemoteDocumentAnalyzer(Lcom/huawei/hms/mlsdk/document/MLDocumentSetting;)Lcom/huawei/hms/mlsdk/document/MLDocumentAnalyzer;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-static {v0, p1}, Lcom/huawei/hms/mlsdk/document/MLDocumentAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/document/MLDocumentSetting;)Lcom/huawei/hms/mlsdk/document/MLDocumentAnalyzer;

    move-result-object p1

    return-object p1
.end method

.method public getRemoteImageClassificationAnalyzer()Lcom/huawei/hms/mlsdk/classification/MLImageClassificationAnalyzer;
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    sget-object v1, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->remoteClassificationSetting:Lcom/huawei/hms/mlsdk/classification/MLRemoteClassificationAnalyzerSetting;

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/classification/MLImageClassificationAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/classification/MLRemoteClassificationAnalyzerSetting;)Lcom/huawei/hms/mlsdk/classification/MLImageClassificationAnalyzer;

    move-result-object v0

    return-object v0
.end method

.method public getRemoteImageClassificationAnalyzer(Lcom/huawei/hms/mlsdk/classification/MLRemoteClassificationAnalyzerSetting;)Lcom/huawei/hms/mlsdk/classification/MLImageClassificationAnalyzer;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-static {v0, p1}, Lcom/huawei/hms/mlsdk/classification/MLImageClassificationAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/classification/MLRemoteClassificationAnalyzerSetting;)Lcom/huawei/hms/mlsdk/classification/MLImageClassificationAnalyzer;

    move-result-object p1

    return-object p1
.end method

.method public getRemoteLandmarkAnalyzer()Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    sget-object v1, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->remoteAnalyzerSetting:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;)Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    move-result-object v0

    return-object v0
.end method

.method public getRemoteLandmarkAnalyzer(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;)Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;
    .locals 1

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-static {v0, p1}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;)Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    move-result-object p1

    return-object p1
.end method

.method public getRemoteProductVisionSearchAnalyzer()Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;
    .locals 2

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    sget-object v1, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->remoteProductVisionSearchSetting:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;)Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;

    move-result-object v0

    return-object v0
.end method

.method public getRemoteProductVisionSearchAnalyzer(Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;)Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-static {v0, p1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;)Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;

    move-result-object p1

    return-object p1
.end method

.method public getRemoteTextAnalyzer()Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    sget-object v1, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->remoteTextSetting:Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    const/4 v2, 0x0

    const/4 v3, 0x0

    invoke-static {v0, v2, v1, v3}, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;Z)Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;

    move-result-object v0

    return-object v0
.end method

.method public getRemoteTextAnalyzer(Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;)Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;
    .locals 3

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->application:Lcom/huawei/hms/mlsdk/common/MLApplication;

    const/4 v1, 0x0

    const/4 v2, 0x0

    invoke-static {v0, v1, p1, v2}, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;Z)Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;

    move-result-object p1

    return-object p1
.end method

.method public isStatisticsAllowed()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->spStore:Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->isStatisticsAllowed()Z

    move-result v0

    return v0
.end method

.method public setStatisticsAllowed(Z)V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/MLAnalyzerFactory;->spStore:Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/MLSharedPreferenceStore;->setStatisticsAllowed(Z)V

    return-void
.end method
