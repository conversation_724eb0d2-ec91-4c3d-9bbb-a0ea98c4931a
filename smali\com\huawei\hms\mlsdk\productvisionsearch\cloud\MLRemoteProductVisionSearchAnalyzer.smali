.class public Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;
.super Ljava/lang/Object;
.source "MLRemoteProductVisionSearchAnalyzer.java"


# static fields
.field private static e:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lcom/huawei/hms/mlsdk/common/AppSettingHolder<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;",
            ">;",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private a:Lcom/huawei/hms/network/httpclient/Submit;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/huawei/hms/network/httpclient/Submit<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private b:Lcom/huawei/hms/mlsdk/common/MLApplication;

.field private c:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;

.field private d:Lcom/huawei/hms/framework/network/grs/GrsClient;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->e:Ljava/util/Map;

    return-void
.end method

.method private constructor <init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->b:Lcom/huawei/hms/mlsdk/common/MLApplication;

    .line 3
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->c:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;

    .line 7
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object p1

    invoke-virtual {p2}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->a()Ljava/lang/String;

    move-result-object p2

    invoke-static {p1, p2}, Lcom/huawei/hms/ml/grs/GrsUtils;->initGrsVisionSearchClientWithCountry(Landroid/content/Context;Ljava/lang/String;)Lcom/huawei/hms/framework/network/grs/GrsClient;

    move-result-object p1

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->d:Lcom/huawei/hms/framework/network/grs/GrsClient;

    return-void
.end method

.method private a(Lcom/huawei/hms/mlsdk/common/MLFrame;)Ljava/util/List;
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    move-object/from16 v0, p0

    .line 2
    iget-object v1, v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->c:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->isEnableFingerprintVerification()Z

    move-result v1

    const/4 v2, 0x2

    if-eqz v1, :cond_1

    .line 3
    iget-object v1, v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->b:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppSetting()Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;->getCertFingerprint()Ljava/lang/String;

    move-result-object v1

    .line 4
    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v3, "Failed to detect cloud product vision search."

    invoke-direct {v1, v3, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1

    .line 9
    :cond_1
    :goto_0
    iget-object v1, v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->d:Lcom/huawei/hms/framework/network/grs/GrsClient;

    if-eqz v1, :cond_1d

    .line 13
    invoke-static {v1}, Lcom/huawei/hms/ml/grs/GrsUtils;->getVisionSearchUrls(Lcom/huawei/hms/framework/network/grs/GrsClient;)Ljava/util/List;

    move-result-object v1

    .line 14
    invoke-static {v1}, Lcom/huawei/hms/ml/grs/GrsUtils;->addHttpsHeaders(Ljava/util/List;)Ljava/util/List;

    move-result-object v5

    .line 15
    invoke-static {v5}, Lcom/huawei/hms/mlsdk/dynamic/a;->a(Ljava/util/Collection;)Z

    move-result v1

    if-nez v1, :cond_1c

    .line 19
    new-instance v1, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    invoke-direct {v1}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;-><init>()V

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a()Lcom/huawei/hms/mlsdk/internal/client/rest/e;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/internal/client/rest/e;->a()Ljava/util/Map;

    move-result-object v8

    const-string v1, "appId"

    .line 20
    invoke-interface {v8, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    const/4 v4, 0x0

    const/4 v6, 0x1

    const-string v11, "RProductVisionSearch"

    if-eqz v1, :cond_6

    .line 21
    invoke-virtual {v1}, Ljava/lang/String;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_2

    goto :goto_2

    :cond_2
    const-string v1, "Authorization"

    .line 26
    invoke-interface {v8, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    const-string v7, "Bearer "

    const-string v9, ""

    invoke-virtual {v1, v7, v9}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v1

    .line 27
    invoke-virtual {v1}, Ljava/lang/String;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_3

    const-string v1, "header file api_key is empty"

    .line 28
    invoke-static {v11, v1}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_3

    :cond_3
    const-string v1, "X-Package-Name"

    .line 32
    invoke-interface {v8, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    if-eqz v1, :cond_5

    .line 33
    invoke-virtual {v1}, Ljava/lang/String;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_4

    goto :goto_1

    :cond_4
    move v1, v4

    goto :goto_4

    :cond_5
    :goto_1
    const-string v1, "header file package_name is empty"

    .line 34
    invoke-static {v11, v1}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_3

    :cond_6
    :goto_2
    const-string v1, "header app_id is empty"

    .line 35
    invoke-static {v11, v1}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    :goto_3
    move v1, v6

    :goto_4
    if-nez v1, :cond_1b

    .line 36
    iget-object v1, v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->c:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->getProductSetId()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_7

    const-string v1, "productSetId is required.5"

    .line 37
    invoke-static {v11, v1}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 41
    :cond_7
    invoke-virtual/range {p1 .. p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->readBitmap()Landroid/graphics/Bitmap;

    move-result-object v1

    .line 42
    invoke-virtual {v1}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v7

    .line 43
    invoke-virtual {v1}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v1

    .line 44
    invoke-static {v7, v1}, Ljava/lang/Math;->min(II)I

    move-result v1

    int-to-float v1, v1

    const/high16 v7, 0x3f800000    # 1.0f

    mul-float/2addr v1, v7

    const/high16 v9, 0x44200000    # 640.0f

    div-float/2addr v1, v9

    cmpg-float v9, v1, v7

    if-gez v9, :cond_8

    goto :goto_5

    :cond_8
    move v7, v1

    .line 49
    :goto_5
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v9, "calcByLongSize = "

    invoke-virtual {v1, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v9, ", scaleFactor = "

    invoke-virtual {v1, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v7}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v11, v1}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    float-to-double v12, v7

    .line 50
    invoke-virtual/range {p1 .. p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->readBitmap()Landroid/graphics/Bitmap;

    move-result-object v1

    .line 51
    invoke-virtual {v1}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v7

    int-to-double v9, v7

    div-double/2addr v9, v12

    double-to-int v7, v9

    .line 52
    invoke-virtual {v1}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v9

    int-to-double v9, v9

    div-double/2addr v9, v12

    double-to-int v9, v9

    .line 53
    invoke-static {v1, v7, v9, v6}, Landroid/graphics/Bitmap;->createScaledBitmap(Landroid/graphics/Bitmap;IIZ)Landroid/graphics/Bitmap;

    move-result-object v1

    const/16 v6, 0x5a

    const/16 v7, 0x64

    .line 54
    invoke-static {v1, v7}, Lcom/huawei/hms/mlkit/common/internal/client/ImageConvertUtils;->bitmap2Jpeg(Landroid/graphics/Bitmap;I)[B

    move-result-object v9

    .line 56
    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    const-string v14, "width = "

    invoke-virtual {v10, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 57
    invoke-virtual {v1}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v15

    invoke-virtual {v10, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v15, ", height = "

    invoke-virtual {v10, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v3

    invoke-virtual {v10, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v3, ", expected kb = "

    invoke-virtual {v10, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v3, ", expected quality = "

    invoke-virtual {v10, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v3, ", conditionalCompression = "

    invoke-virtual {v10, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10, v4}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v3, ", original kb = "

    invoke-virtual {v10, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    array-length v3, v9

    div-int/lit16 v3, v3, 0x400

    invoke-virtual {v10, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v10}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 58
    invoke-static {v11, v3}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 66
    invoke-static {v1, v6}, Lcom/huawei/hms/mlkit/common/internal/client/ImageConvertUtils;->bitmap2Jpeg(Landroid/graphics/Bitmap;I)[B

    move-result-object v3

    .line 69
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v6

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v1

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v1, ", resized kb = "

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    array-length v1, v3

    div-int/lit16 v1, v1, 0x400

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v11, v1}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 72
    invoke-static {v3, v2}, Landroid/util/Base64;->encodeToString([BI)Ljava/lang/String;

    move-result-object v1

    .line 73
    iget-object v3, v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->c:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;

    .line 74
    new-instance v4, Lcom/google/gson/JsonObject;

    invoke-direct {v4}, Lcom/google/gson/JsonObject;-><init>()V

    const-string v6, "imgBase64"

    .line 75
    invoke-virtual {v4, v6, v1}, Lcom/google/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/String;)V

    .line 76
    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->getLargestNumOfReturns()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v6, "topNum"

    invoke-virtual {v4, v6, v1}, Lcom/google/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 77
    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->getProductSetId()Ljava/lang/String;

    move-result-object v1

    const-string v6, "productSetId"

    invoke-virtual {v4, v6, v1}, Lcom/google/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/String;)V

    .line 78
    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->isEnableBorderExtract()Z

    move-result v1

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const-string v6, "globalRegion"

    invoke-virtual {v4, v6, v1}, Lcom/google/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Boolean;)V

    .line 79
    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->getClassType()Ljava/lang/Integer;

    move-result-object v1

    const-string v3, "classType"

    invoke-virtual {v4, v3, v1}, Lcom/google/gson/JsonObject;->addProperty(Ljava/lang/String;Ljava/lang/Number;)V

    .line 80
    invoke-virtual {v4}, Lcom/google/gson/JsonObject;->toString()Ljava/lang/String;

    move-result-object v9

    .line 81
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->a()Lcom/huawei/hms/mlsdk/internal/client/rest/d;

    move-result-object v4

    const-class v6, Lcom/huawei/hms/mlsdk/cloud/RemoteRequestService;

    const-string v7, "v1/image/recognition/snapshop"

    const-string v10, "RProductVisionSearch"

    invoke-virtual/range {v4 .. v10}, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->a(Ljava/util/List;Ljava/lang/Class;Ljava/lang/String;Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/network/httpclient/Response;

    move-result-object v1

    .line 82
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 84
    invoke-virtual {v1}, Lcom/huawei/hms/network/httpclient/Response;->getBody()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    .line 87
    new-instance v4, Lcom/google/gson/Gson;

    invoke-direct {v4}, Lcom/google/gson/Gson;-><init>()V

    const-class v5, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;

    invoke-virtual {v4, v1, v5}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;

    if-eqz v1, :cond_1a

    .line 92
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "handleResult message = "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 93
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->b()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, ", code = "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->a()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    .line 94
    invoke-static {v11, v4}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 99
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->a()Ljava/lang/String;

    move-result-object v4

    const-string v5, "0"

    invoke-virtual {v5, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_11

    .line 100
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->c()Ljava/util/List;

    move-result-object v1

    .line 122
    invoke-static {v1}, Lcom/huawei/hms/mlsdk/dynamic/a;->a(Ljava/util/Collection;)Z

    move-result v2

    if-eqz v2, :cond_9

    goto/16 :goto_d

    .line 126
    :cond_9
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_6
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_10

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResult;

    .line 127
    new-instance v4, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;

    invoke-direct {v4}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;-><init>()V

    .line 128
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResult;->b()Ljava/lang/String;

    move-result-object v5

    invoke-static {v5}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v5

    if-eqz v5, :cond_a

    const-string v5, "UNKNOWN"

    goto :goto_7

    :cond_a
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResult;->b()Ljava/lang/String;

    move-result-object v5

    :goto_7
    invoke-virtual {v4, v5}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->setType(Ljava/lang/String;)V

    .line 130
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResult;->a()Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;

    move-result-object v5

    if-eqz v5, :cond_b

    .line 132
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;->c()I

    move-result v6

    int-to-double v6, v6

    mul-double/2addr v6, v12

    double-to-int v6, v6

    .line 133
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;->d()I

    move-result v7

    int-to-double v7, v7

    mul-double/2addr v7, v12

    double-to-int v7, v7

    .line 134
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;->a()I

    move-result v8

    int-to-double v8, v8

    mul-double/2addr v8, v12

    double-to-int v8, v8

    .line 135
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;->b()I

    move-result v5

    int-to-double v9, v5

    mul-double/2addr v9, v12

    double-to-int v5, v9

    .line 136
    new-instance v9, Landroid/graphics/Rect;

    invoke-direct {v9, v6, v7, v8, v5}, Landroid/graphics/Rect;-><init>(IIII)V

    invoke-virtual {v4, v9}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->setBorder(Landroid/graphics/Rect;)V

    goto :goto_8

    .line 138
    :cond_b
    new-instance v5, Landroid/graphics/Rect;

    invoke-direct {v5}, Landroid/graphics/Rect;-><init>()V

    invoke-virtual {v4, v5}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->setBorder(Landroid/graphics/Rect;)V

    .line 139
    :goto_8
    new-instance v5, Ljava/util/ArrayList;

    invoke-direct {v5}, Ljava/util/ArrayList;-><init>()V

    .line 140
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResult;->c()Ljava/util/List;

    move-result-object v6

    invoke-static {v6}, Lcom/huawei/hms/mlsdk/dynamic/a;->a(Ljava/util/Collection;)Z

    move-result v6

    if-eqz v6, :cond_c

    goto/16 :goto_c

    .line 144
    :cond_c
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResult;->c()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_9
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v6

    if-eqz v6, :cond_f

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;

    .line 145
    new-instance v7, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;

    invoke-direct {v7}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;-><init>()V

    .line 146
    invoke-virtual {v6}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;->c()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->setProductId(Ljava/lang/String;)V

    .line 147
    new-instance v8, Ljava/util/ArrayList;

    invoke-direct {v8}, Ljava/util/ArrayList;-><init>()V

    .line 148
    invoke-virtual {v6}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;->b()Ljava/util/List;

    move-result-object v9

    invoke-static {v9}, Lcom/huawei/hms/mlsdk/dynamic/a;->a(Ljava/util/Collection;)Z

    move-result v9

    if-eqz v9, :cond_d

    goto :goto_b

    .line 152
    :cond_d
    invoke-virtual {v6}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;->b()Ljava/util/List;

    move-result-object v9

    invoke-interface {v9}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v9

    :goto_a
    invoke-interface {v9}, Ljava/util/Iterator;->hasNext()Z

    move-result v10

    if-eqz v10, :cond_e

    invoke-interface {v9}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v10

    check-cast v10, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ImageResult;

    .line 153
    new-instance v11, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;

    invoke-direct {v11}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;-><init>()V

    .line 154
    invoke-virtual {v6}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;->c()Ljava/lang/String;

    move-result-object v14

    invoke-virtual {v11, v14}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->setProductId(Ljava/lang/String;)V

    .line 155
    invoke-virtual {v10}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ImageResult;->a()Ljava/lang/String;

    move-result-object v14

    invoke-virtual {v11, v14}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->setImageId(Ljava/lang/String;)V

    .line 156
    invoke-virtual {v10}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ImageResult;->c()F

    move-result v14

    invoke-virtual {v11, v14}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->setPossibility(F)V

    .line 157
    invoke-virtual {v10}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ImageResult;->b()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v11, v10}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->setInnerUrl(Ljava/lang/String;)V

    .line 158
    invoke-interface {v8, v11}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_a

    .line 159
    :cond_e
    :goto_b
    invoke-virtual {v7, v8}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->setImageList(Ljava/util/List;)V

    .line 160
    invoke-virtual {v6}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;->d()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7, v8}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->setProductUrl(Ljava/lang/String;)V

    .line 161
    invoke-virtual {v6}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;->a()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v7, v6}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->setCustomContent(Ljava/lang/String;)V

    .line 163
    invoke-interface {v5, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_9

    .line 164
    :cond_f
    :goto_c
    invoke-virtual {v4, v5}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->setProductList(Ljava/util/List;)V

    .line 166
    invoke-interface {v3, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_6

    :cond_10
    :goto_d
    return-object v3

    .line 167
    :cond_11
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->a()Ljava/lang/String;

    move-result-object v3

    const-string v4, "2001"

    invoke-virtual {v4, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_19

    .line 169
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->a()Ljava/lang/String;

    move-result-object v3

    const-string v4, "2002"

    invoke-virtual {v4, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_18

    .line 171
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->a()Ljava/lang/String;

    move-result-object v3

    const-string v4, "2005"

    invoke-virtual {v4, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_17

    .line 173
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->a()Ljava/lang/String;

    move-result-object v3

    const-string v4, "2034"

    invoke-virtual {v4, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_16

    .line 175
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->a()Ljava/lang/String;

    move-result-object v3

    const-string v4, "4005"

    invoke-virtual {v4, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    const/16 v4, 0x9

    if-nez v3, :cond_15

    .line 177
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->a()Ljava/lang/String;

    move-result-object v3

    const-string v5, "4006"

    invoke-virtual {v5, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_14

    .line 179
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->a()Ljava/lang/String;

    move-result-object v3

    const-string v5, "4007"

    invoke-virtual {v5, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_13

    .line 182
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->a()Ljava/lang/String;

    move-result-object v3

    const-string v4, "001001"

    invoke-virtual {v4, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_12

    .line 183
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const/16 v2, 0x13

    const-string v3, "Token is invalid or expired."

    invoke-direct {v1, v3, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1

    .line 185
    :cond_12
    new-instance v3, Lcom/huawei/hms/mlsdk/common/MLException;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "Internal error, code = "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->a()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v4, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v3, v1, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v3

    .line 186
    :cond_13
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v2, "The free quota has been used up,please upgrade package on https://developer.huawei.com."

    invoke-direct {v1, v2, v4}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1

    .line 187
    :cond_14
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v2, "Please subscribe package first on https://developer.huawei.com."

    invoke-direct {v1, v2, v4}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1

    .line 188
    :cond_15
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v2, "The project has been out of credit and grace period is over."

    invoke-direct {v1, v2, v4}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1

    .line 189
    :cond_16
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v2, "productSetId does not exist in the project"

    const/4 v3, 0x5

    invoke-direct {v1, v2, v3}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1

    .line 190
    :cond_17
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v3, "Picture is not recognized."

    invoke-direct {v1, v3, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1

    :cond_18
    const/4 v3, 0x5

    .line 191
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v2, "Incorrect parameter. This exception is irrelevant to services."

    invoke-direct {v1, v2, v3}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1

    .line 192
    :cond_19
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const/16 v2, 0xf

    const-string v3, "Identity authentication required."

    invoke-direct {v1, v3, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1

    .line 193
    :cond_1a
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v3, "Cloud service return the empty result."

    invoke-direct {v1, v3, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1

    .line 194
    :cond_1b
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v3, "Header param error, fail to detect cloud product vision search."

    invoke-direct {v1, v3, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1

    .line 195
    :cond_1c
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v3, "UrlList is empty, fail to detect cloud product vision search."

    invoke-direct {v1, v3, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1

    .line 196
    :cond_1d
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v2, "region is required."

    const/4 v3, 0x5

    invoke-direct {v1, v2, v3}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v1
.end method

.method static synthetic a(Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->a(Lcom/huawei/hms/mlsdk/common/MLFrame;)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method public static declared-synchronized create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;)Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;
    .locals 3

    const-class v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;

    monitor-enter v0

    .line 1
    :try_start_0
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getUniqueKey()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, p1}, Lcom/huawei/hms/mlsdk/common/AppSettingHolder;->create(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/common/AppSettingHolder;

    move-result-object v1

    .line 3
    sget-object v2, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->e:Ljava/util/Map;

    invoke-interface {v2, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;

    if-nez v2, :cond_0

    .line 4
    new-instance v2, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;

    invoke-direct {v2, p0, p1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;)V

    .line 5
    sget-object p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->e:Ljava/util/Map;

    invoke-interface {p0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_0
    monitor-exit v0

    return-object v2

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method


# virtual methods
.method public analyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    const-string v0, "RProductVisionSearch"

    const-string v1, "analyseFrame"

    .line 1
    invoke-static {v0, v1}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->a(Lcom/huawei/hms/mlsdk/common/MLFrame;)Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public asyncAnalyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Lcom/huawei/hmf/tasks/Task<",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;",
            ">;>;"
        }
    .end annotation

    const-string v0, "RProductVisionSearch"

    const-string v1, "asyncAnalyseFrame"

    .line 1
    invoke-static {v0, v1}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    new-instance v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer$a;

    invoke-direct {v0, p0, p1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer$a;-><init>(Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;)V

    invoke-static {v0}, Lcom/huawei/hmf/tasks/Tasks;->callInBackground(Ljava/util/concurrent/Callable;)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    return-object p1
.end method

.method public stop()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->a:Lcom/huawei/hms/network/httpclient/Submit;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/Submit;->isCanceled()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->a:Lcom/huawei/hms/network/httpclient/Submit;

    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/Submit;->isExecuted()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->a:Lcom/huawei/hms/network/httpclient/Submit;

    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/Submit;->cancel()V

    const/4 v0, 0x0

    .line 3
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->a:Lcom/huawei/hms/network/httpclient/Submit;

    :cond_0
    return-void
.end method
