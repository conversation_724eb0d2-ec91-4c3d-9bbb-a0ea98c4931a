.class final Lcom/huawei/hms/mlsdk/mlvision/b$b;
.super Ljava/lang/Object;
.source "RemoteImageSegmentationInitializer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/mlvision/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "b"
.end annotation


# static fields
.field static a:Lcom/huawei/hms/mlsdk/mlvision/b;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/mlvision/b;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/huawei/hms/mlsdk/mlvision/b;-><init>(Lcom/huawei/hms/mlsdk/mlvision/b$a;)V

    sput-object v0, Lcom/huawei/hms/mlsdk/mlvision/b$b;->a:Lcom/huawei/hms/mlsdk/mlvision/b;

    return-void
.end method
