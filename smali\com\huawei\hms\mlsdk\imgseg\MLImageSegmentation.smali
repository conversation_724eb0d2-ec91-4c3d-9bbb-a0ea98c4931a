.class public Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;
.super Ljava/lang/Object;
.source "MLImageSegmentation.java"


# static fields
.field public static instance:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;


# instance fields
.field public foreground:Landroid/graphics/Bitmap;

.field public grayscale:Landroid/graphics/Bitmap;

.field public masks:[B

.field public original:Landroid/graphics/Bitmap;


# direct methods
.method public constructor <init>([BLandroid/graphics/Bitmap;Landroid/graphics/Bitmap;)V
    .locals 1

    const/4 v0, 0x0

    .line 1
    invoke-direct {p0, p1, p2, p3, v0}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;-><init>([BLandroid/graphics/Bitmap;Landroid/graphics/Bitmap;Landroid/graphics/Bitmap;)V

    return-void
.end method

.method public constructor <init>([BLandroid/graphics/Bitmap;Landroid/graphics/Bitmap;Landroid/graphics/Bitmap;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->masks:[B

    .line 4
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->foreground:Landroid/graphics/Bitmap;

    .line 5
    iput-object p3, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->grayscale:Landroid/graphics/Bitmap;

    .line 6
    iput-object p4, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->original:Landroid/graphics/Bitmap;

    return-void
.end method

.method public static getInstance()Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;
    .locals 2

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->instance:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;

    if-nez v0, :cond_0

    new-instance v0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;

    const/4 v1, 0x0

    invoke-direct {v0, v1, v1, v1}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;-><init>([BLandroid/graphics/Bitmap;Landroid/graphics/Bitmap;)V

    :cond_0
    return-object v0
.end method


# virtual methods
.method public getForeground()Landroid/graphics/Bitmap;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->foreground:Landroid/graphics/Bitmap;

    return-object v0
.end method

.method public getGrayscale()Landroid/graphics/Bitmap;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->grayscale:Landroid/graphics/Bitmap;

    return-object v0
.end method

.method public getMasks()[B
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->masks:[B

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, [B->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [B

    :goto_0
    return-object v0
.end method

.method public getOriginal()Landroid/graphics/Bitmap;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;->original:Landroid/graphics/Bitmap;

    return-object v0
.end method
