.class public Lcom/huawei/hms/mlsdk/text/MLText$TextLine;
.super Lcom/huawei/hms/mlsdk/text/MLText$Base;
.source "MLText.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/text/MLText;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "TextLine"
.end annotation


# instance fields
.field private wordList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/MLText$Word;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroid/graphics/Rect;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/TextLanguage;",
            ">;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;",
            ">;[",
            "Landroid/graphics/Point;",
            "F)V"
        }
    .end annotation

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p5

    move v5, p6

    .line 1
    invoke-direct/range {v0 .. v5}, Lcom/huawei/hms/mlsdk/text/MLText$Base;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;[Landroid/graphics/Point;F)V

    .line 2
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLText$TextLine;->wordList:Ljava/util/List;

    .line 6
    invoke-direct {p0, p4}, Lcom/huawei/hms/mlsdk/text/MLText$TextLine;->initMLTextWords(Ljava/util/List;)V

    return-void
.end method

.method private initMLTextWords(Ljava/util/List;)V
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;

    .line 2
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLText$TextLine;->wordList:Ljava/util/List;

    new-instance v8, Lcom/huawei/hms/mlsdk/text/MLText$Word;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getStringValue()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getBorder()Landroid/graphics/Rect;

    move-result-object v4

    .line 3
    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getLanguageList()Ljava/util/List;

    move-result-object v5

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getVertexes()[Landroid/graphics/Point;

    move-result-object v6

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getConfidence()Ljava/lang/Float;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    move-result v7

    move-object v2, v8

    invoke-direct/range {v2 .. v7}, Lcom/huawei/hms/mlsdk/text/MLText$Word;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;[Landroid/graphics/Point;F)V

    .line 4
    invoke-interface {v1, v8}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_0
    return-void
.end method


# virtual methods
.method public getContents()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/MLText$Word;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLText$TextLine;->wordList:Ljava/util/List;

    return-object v0
.end method

.method public getRotatingDegree()F
    .locals 6

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getVertexes()[Landroid/graphics/Point;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 2
    array-length v1, v0

    const/4 v2, 0x2

    if-lt v1, v2, :cond_0

    const/4 v1, 0x1

    .line 3
    aget-object v2, v0, v1

    iget v2, v2, Landroid/graphics/Point;->y:I

    const/4 v3, 0x0

    aget-object v4, v0, v3

    iget v4, v4, Landroid/graphics/Point;->y:I

    sub-int/2addr v2, v4

    int-to-double v4, v2

    aget-object v1, v0, v1

    iget v1, v1, Landroid/graphics/Point;->x:I

    aget-object v0, v0, v3

    iget v0, v0, Landroid/graphics/Point;->x:I

    sub-int/2addr v1, v0

    int-to-double v0, v1

    invoke-static {v4, v5, v0, v1}, Ljava/lang/Math;->atan2(DD)D

    move-result-wide v0

    const-wide v2, 0x4066800000000000L    # 180.0

    mul-double/2addr v0, v2

    const-wide v2, 0x400921fb54442d18L    # Math.PI

    div-double/2addr v0, v2

    double-to-float v0, v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    .line 6
    :goto_0
    invoke-static {v0}, Ljava/lang/String;->valueOf(F)Ljava/lang/String;

    move-result-object v1

    const-string v2, "MLText-Angle:"

    invoke-static {v2, v1}, Lcom/huawei/hms/ml/common/base/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v0
.end method

.method public isVertical()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method
