.class public Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;
.super Ljava/lang/Object;
.source "MLProductVisionSearch.java"


# instance fields
.field private a:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;",
            ">;"
        }
    .end annotation
.end field

.field private b:Ljava/lang/String;

.field private c:Landroid/graphics/Rect;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroid/graphics/Rect;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;",
            ">;)V"
        }
    .end annotation

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->b:Ljava/lang/String;

    .line 4
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->c:Landroid/graphics/Rect;

    .line 5
    iput-object p3, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->a:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public getBorder()Landroid/graphics/Rect;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->c:Landroid/graphics/Rect;

    return-object v0
.end method

.method public getProductList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->a:Ljava/util/List;

    return-object v0
.end method

.method public getType()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->b:Ljava/lang/String;

    return-object v0
.end method

.method public setBorder(Landroid/graphics/Rect;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->c:Landroid/graphics/Rect;

    return-void
.end method

.method public setProductList(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->a:Ljava/util/List;

    return-void
.end method

.method public setType(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;->b:Ljava/lang/String;

    return-void
.end method
