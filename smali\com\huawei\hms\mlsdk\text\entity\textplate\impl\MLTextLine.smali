.class public Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;
.super Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;
.source "MLTextLine.java"


# instance fields
.field private words:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroid/graphics/Rect;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/TextLanguage;",
            ">;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;",
            ">;[",
            "Landroid/graphics/Point;",
            "F)V"
        }
    .end annotation

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p5

    move v5, p6

    .line 1
    invoke-direct/range {v0 .. v5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;[Landroid/graphics/Point;F)V

    .line 2
    iput-object p4, p0, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;->words:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public declared-synchronized getContents()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;",
            ">;"
        }
    .end annotation

    monitor-enter p0

    .line 1
    :try_start_0
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;->words:Ljava/util/List;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public getRotatingDegree()F
    .locals 6

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getVertexes()[Landroid/graphics/Point;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 2
    array-length v1, v0

    const/4 v2, 0x2

    if-lt v1, v2, :cond_0

    const/4 v1, 0x1

    .line 3
    aget-object v2, v0, v1

    iget v2, v2, Landroid/graphics/Point;->y:I

    const/4 v3, 0x0

    aget-object v4, v0, v3

    iget v4, v4, Landroid/graphics/Point;->y:I

    sub-int/2addr v2, v4

    int-to-double v4, v2

    aget-object v1, v0, v1

    iget v1, v1, Landroid/graphics/Point;->x:I

    aget-object v0, v0, v3

    iget v0, v0, Landroid/graphics/Point;->x:I

    sub-int/2addr v1, v0

    int-to-double v0, v1

    invoke-static {v4, v5, v0, v1}, Ljava/lang/Math;->atan2(DD)D

    move-result-wide v0

    const-wide v2, 0x4066800000000000L    # 180.0

    mul-double/2addr v0, v2

    const-wide v2, 0x400921fb54442d18L    # Math.PI

    div-double/2addr v0, v2

    double-to-float v0, v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    .line 6
    :goto_0
    invoke-static {v0}, Ljava/lang/String;->valueOf(F)Ljava/lang/String;

    move-result-object v1

    const-string v2, "MLText-Angle:"

    invoke-static {v2, v1}, Lcom/huawei/hms/ml/common/base/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v0
.end method

.method public isVertical()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method
