.class public final enum Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;
.super Ljava/lang/Enum;
.source "MLPlateType.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;",
        ">;"
    }
.end annotation


# static fields
.field private static final synthetic $VALUES:[Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

.field public static final enum IMAGE:Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

.field public static final enum TEXT:Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;


# direct methods
.method static constructor <clinit>()V
    .locals 5

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    const-string v1, "TEXT"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;->TEXT:Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    new-instance v1, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    const-string v3, "IMAGE"

    const/4 v4, 0x1

    invoke-direct {v1, v3, v4}, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;-><init>(Ljava/lang/String;I)V

    sput-object v1, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;->IMAGE:Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    const/4 v3, 0x2

    new-array v3, v3, [Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    aput-object v0, v3, v2

    aput-object v1, v3, v4

    .line 2
    sput-object v3, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;->$VALUES:[Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;
    .locals 1

    .line 1
    const-class v0, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object p0

    check-cast p0, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    return-object p0
.end method

.method public static values()[Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;
    .locals 1

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;->$VALUES:[Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    invoke-virtual {v0}, [Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    return-object v0
.end method
