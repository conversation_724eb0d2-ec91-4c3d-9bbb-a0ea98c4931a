.class public interface abstract annotation Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting$PerformanceType;
.super Ljava/lang/Object;
.source "ML3DFaceAnalyzerSetting.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2609
    name = "PerformanceType"
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->CLASS:Ljava/lang/annotation/RetentionPolicy;
.end annotation
