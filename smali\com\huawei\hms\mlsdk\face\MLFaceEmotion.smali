.class public Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;
.super Ljava/lang/Object;
.source "MLFaceEmotion.java"


# instance fields
.field private angryProbability:F

.field private disgustProbability:F

.field private fearProbability:F

.field private neutralProbability:F

.field private sadProbability:F

.field private smilingProbability:F

.field private surpriseProbability:F


# direct methods
.method public constructor <init>(FFFFFFF)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->smilingProbability:F

    .line 4
    iput p2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->neutralProbability:F

    .line 5
    iput p3, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->angryProbability:F

    .line 6
    iput p4, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->disgustProbability:F

    .line 7
    iput p5, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->fearProbability:F

    .line 8
    iput p6, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->sadProbability:F

    .line 9
    iput p7, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->surpriseProbability:F

    return-void
.end method


# virtual methods
.method public getAngryProbability()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->angryProbability:F

    return v0
.end method

.method public getDisgustProbability()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->disgustProbability:F

    return v0
.end method

.method public getFearProbability()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->fearProbability:F

    return v0
.end method

.method public getNeutralProbability()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->neutralProbability:F

    return v0
.end method

.method public getSadProbability()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->sadProbability:F

    return v0
.end method

.method public getSmilingProbability()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->smilingProbability:F

    return v0
.end method

.method public getSurpriseProbability()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->surpriseProbability:F

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 1
    invoke-static {p0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->toStringHelper(Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->smilingProbability:F

    .line 2
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "smilingProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->neutralProbability:F

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "neutralProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->angryProbability:F

    .line 3
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "angryProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->disgustProbability:F

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "disgustProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->fearProbability:F

    .line 4
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "fearProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->sadProbability:F

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "sadProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->surpriseProbability:F

    .line 5
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "surpriseProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
