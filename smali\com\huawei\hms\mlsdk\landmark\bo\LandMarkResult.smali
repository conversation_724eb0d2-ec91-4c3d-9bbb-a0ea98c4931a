.class public Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;
.super Ljava/lang/Object;
.source "LandMarkResult.java"


# instance fields
.field private description:Ljava/lang/String;

.field private locations:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/landmark/bo/Location;",
            ">;"
        }
    .end annotation
.end field

.field private relativity:D

.field private score:D

.field private vertices:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getDescription()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->description:Ljava/lang/String;

    return-object v0
.end method

.method public getLocations()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/landmark/bo/Location;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->locations:Ljava/util/List;

    return-object v0
.end method

.method public getRelativity()D
    .locals 2

    .line 1
    iget-wide v0, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->relativity:D

    return-wide v0
.end method

.method public getScore()D
    .locals 2

    .line 1
    iget-wide v0, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->score:D

    return-wide v0
.end method

.method public getVertices()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->vertices:Ljava/util/List;

    return-object v0
.end method

.method public setDescription(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->description:Ljava/lang/String;

    return-void
.end method

.method public setLocations(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/landmark/bo/Location;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->locations:Ljava/util/List;

    return-void
.end method

.method public setRelativity(D)V
    .locals 0

    .line 1
    iput-wide p1, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->relativity:D

    return-void
.end method

.method public setScore(D)V
    .locals 0

    .line 1
    iput-wide p1, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->score:D

    return-void
.end method

.method public setVertices(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->vertices:Ljava/util/List;

    return-void
.end method
