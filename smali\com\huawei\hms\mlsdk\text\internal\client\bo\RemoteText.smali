.class public Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;
.super Ljava/lang/Object;
.source "RemoteText.java"


# instance fields
.field private confidence:D

.field private coords:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/cloud/Coord;",
            ">;"
        }
    .end annotation
.end field

.field private language:Ljava/lang/String;

.field private text:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getConfidence()D
    .locals 2

    .line 1
    iget-wide v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->confidence:D

    return-wide v0
.end method

.method public getCoords()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/cloud/Coord;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->coords:Ljava/util/List;

    return-object v0
.end method

.method public getLanguage()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->language:Ljava/lang/String;

    return-object v0
.end method

.method public getText()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->text:Ljava/lang/String;

    return-object v0
.end method

.method public setConfidence(D)V
    .locals 0

    .line 1
    iput-wide p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->confidence:D

    return-void
.end method

.method public setCoords(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/cloud/Coord;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->coords:Ljava/util/List;

    return-void
.end method

.method public setLanguage(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->language:Ljava/lang/String;

    return-void
.end method

.method public setText(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->text:Ljava/lang/String;

    return-void
.end method
