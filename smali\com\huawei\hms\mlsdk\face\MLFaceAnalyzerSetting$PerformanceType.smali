.class public interface abstract annotation Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$PerformanceType;
.super Ljava/lang/Object;
.source "MLFaceAnalyzerSetting.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x2609
    name = "PerformanceType"
.end annotation

.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->CLASS:Ljava/lang/annotation/RetentionPolicy;
.end annotation
