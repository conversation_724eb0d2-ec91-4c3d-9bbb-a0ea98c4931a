.class public Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;
.super Lcom/huawei/hms/mlsdk/common/MLAnalyzer;
.source "MLImageSegmentationAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/huawei/hms/mlsdk/common/MLAnalyzer<",
        "Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;",
        ">;"
    }
.end annotation


# static fields
.field private static final TAG:Ljava/lang/String; = "MLImageSegmentationAnalyzer"

.field private static appOptionDetectMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lcom/huawei/hms/mlsdk/common/AppSettingHolder<",
            "Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;",
            ">;",
            "Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private app:Lcom/huawei/hms/mlsdk/common/MLApplication;

.field private bundle:Landroid/os/Bundle;

.field private options:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 3
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->appOptionDetectMap:Ljava/util/Map;

    return-void
.end method

.method private constructor <init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/common/MLAnalyzer;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    .line 3
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->options:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    .line 4
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->toBundle()Landroid/os/Bundle;

    move-result-object p1

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->bundle:Landroid/os/Bundle;

    return-void
.end method

.method static synthetic access$000(Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    return-object p0
.end method

.method static synthetic access$100(Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;)Landroid/os/Bundle;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->bundle:Landroid/os/Bundle;

    return-object p0
.end method

.method private static convert(Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;)Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;
    .locals 3

    .line 1
    iget-object p1, p0, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;->masks:[B

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;->foreground:Landroid/graphics/Bitmap;

    .line 3
    iget-object v1, p0, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;->grayscale:Landroid/graphics/Bitmap;

    .line 4
    iget-object p0, p0, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;->original:Landroid/graphics/Bitmap;

    .line 5
    new-instance v2, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;

    invoke-direct {v2, p1, v0, v1, p0}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;-><init>([BLandroid/graphics/Bitmap;Landroid/graphics/Bitmap;Landroid/graphics/Bitmap;)V

    return-object v2
.end method

.method public static declared-synchronized create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;)Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;
    .locals 10

    const-class v0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;

    monitor-enter v0

    .line 1
    :try_start_0
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getUniqueKey()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, p1}, Lcom/huawei/hms/mlsdk/common/AppSettingHolder;->create(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/common/AppSettingHolder;

    move-result-object v1

    .line 2
    sget-object v2, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->appOptionDetectMap:Ljava/util/Map;

    invoke-interface {v2, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;

    if-nez v2, :cond_0

    .line 4
    new-instance v2, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;

    invoke-direct {v2, p0, p1}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;)V

    .line 5
    sget-object v3, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->appOptionDetectMap:Ljava/util/Map;

    invoke-interface {v3, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 7
    :cond_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->getInstance()Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;

    move-result-object v1

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v3

    invoke-virtual {v1, v3}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->prepare(Landroid/content/Context;)V

    .line 8
    new-instance v1, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->getAnalyzerType()I

    move-result v5

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->isExact()Z

    move-result v6

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->getScene()I

    move-result v7

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->toBundle()Landroid/os/Bundle;

    move-result-object v8

    const/4 v9, 0x1

    move-object v4, v1

    invoke-direct/range {v4 .. v9}, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;-><init>(IZILandroid/os/Bundle;I)V

    .line 9
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/b;->a()Lcom/huawei/hms/mlsdk/mlvision/b;

    move-result-object p0

    .line 10
    invoke-interface {p0}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object p1

    .line 11
    invoke-static {}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->getInstance()Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;

    move-result-object v3

    invoke-virtual {v3, v1, p0, p1}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->initial(Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;Landroid/os/IInterface;)I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit v0

    return-object v2

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method


# virtual methods
.method public analyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Landroid/util/SparseArray;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Landroid/util/SparseArray<",
            "Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;",
            ">;"
        }
    .end annotation

    if-eqz p1, :cond_1

    .line 1
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->initialize()V

    const/4 v0, 0x1

    const/4 v1, 0x0

    .line 2
    invoke-virtual {p1, v1, v0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getFrame(ZZ)Lcom/huawei/hms/mlsdk/common/MLFrame;

    move-result-object p1

    .line 3
    new-instance v0, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->options:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->getAnalyzerType()I

    move-result v3

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->options:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->isExact()Z

    move-result v4

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->options:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->getScene()I

    move-result v5

    iget-object v6, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->bundle:Landroid/os/Bundle;

    const/4 v7, 0x1

    move-object v2, v0

    invoke-direct/range {v2 .. v7}, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;-><init>(IZILandroid/os/Bundle;I)V

    .line 4
    invoke-static {}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->getInstance()Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;

    move-result-object v2

    iget-object v3, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v3

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->bundle:Landroid/os/Bundle;

    invoke-virtual {v2, v3, v4, p1, v0}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->detect(Landroid/content/Context;Landroid/os/Bundle;Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;)Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;

    move-result-object p1

    .line 5
    new-instance v0, Landroid/util/SparseArray;

    invoke-direct {v0}, Landroid/util/SparseArray;-><init>()V

    if-eqz p1, :cond_0

    .line 7
    new-instance v2, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;

    iget-object v3, p1, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;->masks:[B

    iget-object v4, p1, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;->foreground:Landroid/graphics/Bitmap;

    iget-object v5, p1, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;->grayscale:Landroid/graphics/Bitmap;

    iget-object p1, p1, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;->original:Landroid/graphics/Bitmap;

    invoke-direct {v2, v3, v4, v5, p1}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;-><init>([BLandroid/graphics/Bitmap;Landroid/graphics/Bitmap;Landroid/graphics/Bitmap;)V

    invoke-virtual {v0, v1, v2}, Landroid/util/SparseArray;->put(ILjava/lang/Object;)V

    :cond_0
    return-object v0

    .line 8
    :cond_1
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "No frame found."

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public asyncAnalyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Lcom/huawei/hmf/tasks/Task<",
            "Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentation;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->initialize()V

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 2
    invoke-virtual {p1, v0, v1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getFrame(ZZ)Lcom/huawei/hms/mlsdk/common/MLFrame;

    move-result-object p1

    .line 4
    new-instance v6, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;

    iget-object v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->options:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->getAnalyzerType()I

    move-result v1

    iget-object v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->options:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->isExact()Z

    move-result v2

    iget-object v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->options:Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->getScene()I

    move-result v3

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->bundle:Landroid/os/Bundle;

    const/4 v5, 0x1

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;-><init>(IZILandroid/os/Bundle;I)V

    .line 5
    new-instance v0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer$1;

    invoke-direct {v0, p0, p1, v6}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer$1;-><init>(Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;)V

    invoke-static {v0}, Lcom/huawei/hmf/tasks/Tasks;->callInBackground(Ljava/util/concurrent/Callable;)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    return-object p1
.end method

.method public stop()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->getInstance()Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->release(Landroid/content/Context;)V

    return-void
.end method
