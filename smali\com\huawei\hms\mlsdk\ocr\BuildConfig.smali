.class public final Lcom/huawei/hms/mlsdk/ocr/BuildConfig;
.super Ljava/lang/Object;
.source "BuildConfig.java"


# static fields
.field public static final APPLICATION_ID:Ljava/lang/String; = "com.huawei.hms.mlsdk.ocr"
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final BUILD_TYPE:Ljava/lang/String; = "release"

.field public static final DEBUG:Z = false

.field public static final FLAVOR:Ljava/lang/String; = ""

.field public static final LIBRARY_PACKAGE_NAME:Ljava/lang/String; = "com.huawei.hms.mlsdk.ocr"

.field public static final VERSION_CODE:I = 0x1da8d8d

.field public static final VERSION_NAME:Ljava/lang/String; = "3.11.0.301"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 6
    invoke-direct {p0}, <PERSON><PERSON><PERSON>/lang/Object;-><init>()V

    return-void
.end method
