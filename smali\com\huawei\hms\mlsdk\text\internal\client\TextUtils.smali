.class final Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;
.super Ljava/lang/Object;
.source "TextUtils.java"


# direct methods
.method constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private static compare(IIIILcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;)[Landroid/graphics/Point;
    .locals 10

    .line 1
    invoke-virtual {p4}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getVertexes()[Landroid/graphics/Point;

    move-result-object v0

    const/4 v1, 0x0

    aget-object v0, v0, v1

    iget v0, v0, Landroid/graphics/Point;->x:I

    .line 2
    invoke-virtual {p4}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getVertexes()[Landroid/graphics/Point;

    move-result-object v2

    aget-object v2, v2, v1

    iget v2, v2, Landroid/graphics/Point;->y:I

    .line 3
    invoke-virtual {p4}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;->getRotatingDegree()F

    move-result v3

    float-to-double v3, v3

    invoke-static {v3, v4}, Ljava/lang/Math;->toRadians(D)D

    move-result-wide v3

    invoke-static {v3, v4}, Ljava/lang/Math;->sin(D)D

    move-result-wide v3

    .line 4
    invoke-virtual {p4}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;->getRotatingDegree()F

    move-result p4

    float-to-double v5, p4

    invoke-static {v5, v6}, Ljava/lang/Math;->toRadians(D)D

    move-result-wide v5

    invoke-static {v5, v6}, Ljava/lang/Math;->cos(D)D

    move-result-wide v5

    const/4 p4, 0x4

    new-array v7, p4, [Landroid/graphics/Point;

    .line 6
    new-instance v8, Landroid/graphics/Point;

    invoke-direct {v8, p0, p1}, Landroid/graphics/Point;-><init>(II)V

    aput-object v8, v7, v1

    new-instance v8, Landroid/graphics/Point;

    invoke-direct {v8, p2, p1}, Landroid/graphics/Point;-><init>(II)V

    const/4 p1, 0x1

    aput-object v8, v7, p1

    new-instance p1, Landroid/graphics/Point;

    invoke-direct {p1, p2, p3}, Landroid/graphics/Point;-><init>(II)V

    const/4 p2, 0x2

    aput-object p1, v7, p2

    new-instance p1, Landroid/graphics/Point;

    invoke-direct {p1, p0, p3}, Landroid/graphics/Point;-><init>(II)V

    const/4 p0, 0x3

    aput-object p1, v7, p0

    :goto_0
    if-ge v1, p4, :cond_0

    .line 10
    aget-object p0, v7, v1

    iget p0, p0, Landroid/graphics/Point;->x:I

    int-to-double p0, p0

    mul-double/2addr p0, v5

    aget-object p2, v7, v1

    iget p2, p2, Landroid/graphics/Point;->y:I

    int-to-double p2, p2

    mul-double/2addr p2, v3

    sub-double/2addr p0, p2

    double-to-int p0, p0

    .line 11
    aget-object p1, v7, v1

    iget p1, p1, Landroid/graphics/Point;->x:I

    int-to-double p1, p1

    mul-double/2addr p1, v3

    aget-object p3, v7, v1

    iget p3, p3, Landroid/graphics/Point;->y:I

    int-to-double v8, p3

    mul-double/2addr v8, v5

    add-double/2addr v8, p1

    double-to-int p1, v8

    .line 12
    aget-object p2, v7, v1

    iput p0, p2, Landroid/graphics/Point;->x:I

    .line 13
    aget-object p0, v7, v1

    iput p1, p0, Landroid/graphics/Point;->y:I

    .line 14
    aget-object p0, v7, v1

    invoke-virtual {p0, v0, v2}, Landroid/graphics/Point;->offset(II)V

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object v7
.end method

.method private static compare(Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;)[Landroid/graphics/Point;
    .locals 12

    .line 15
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getVertexes()[Landroid/graphics/Point;

    move-result-object v0

    const/4 v1, 0x0

    aget-object v0, v0, v1

    iget v0, v0, Landroid/graphics/Point;->x:I

    neg-int v0, v0

    .line 16
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getVertexes()[Landroid/graphics/Point;

    move-result-object v2

    aget-object v2, v2, v1

    iget v2, v2, Landroid/graphics/Point;->y:I

    neg-int v2, v2

    .line 17
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;->getRotatingDegree()F

    move-result v3

    float-to-double v3, v3

    invoke-static {v3, v4}, Ljava/lang/Math;->toRadians(D)D

    move-result-wide v3

    invoke-static {v3, v4}, Ljava/lang/Math;->sin(D)D

    move-result-wide v3

    .line 18
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;->getRotatingDegree()F

    move-result p1

    float-to-double v5, p1

    invoke-static {v5, v6}, Ljava/lang/Math;->toRadians(D)D

    move-result-wide v5

    invoke-static {v5, v6}, Ljava/lang/Math;->cos(D)D

    move-result-wide v5

    .line 20
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getVertexes()[Landroid/graphics/Point;

    move-result-object p0

    .line 21
    array-length p1, p0

    new-array p1, p1, [Landroid/graphics/Point;

    .line 22
    :goto_0
    array-length v7, p0

    if-ge v1, v7, :cond_0

    .line 23
    new-instance v7, Landroid/graphics/Point;

    aget-object v8, p0, v1

    iget v8, v8, Landroid/graphics/Point;->x:I

    aget-object v9, p0, v1

    iget v9, v9, Landroid/graphics/Point;->y:I

    invoke-direct {v7, v8, v9}, Landroid/graphics/Point;-><init>(II)V

    aput-object v7, p1, v1

    .line 24
    invoke-virtual {v7, v0, v2}, Landroid/graphics/Point;->offset(II)V

    .line 26
    aget-object v7, p1, v1

    iget v7, v7, Landroid/graphics/Point;->x:I

    int-to-double v7, v7

    mul-double/2addr v7, v5

    aget-object v9, p1, v1

    iget v9, v9, Landroid/graphics/Point;->y:I

    int-to-double v9, v9

    mul-double/2addr v9, v3

    add-double/2addr v9, v7

    double-to-int v7, v9

    .line 27
    aget-object v8, p1, v1

    iget v8, v8, Landroid/graphics/Point;->x:I

    neg-int v8, v8

    int-to-double v8, v8

    mul-double/2addr v8, v3

    aget-object v10, p1, v1

    iget v10, v10, Landroid/graphics/Point;->y:I

    int-to-double v10, v10

    mul-double/2addr v10, v5

    add-double/2addr v10, v8

    double-to-int v8, v10

    .line 28
    aget-object v9, p1, v1

    iput v7, v9, Landroid/graphics/Point;->x:I

    .line 29
    aget-object v7, p1, v1

    iput v8, v7, Landroid/graphics/Point;->y:I

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_0
    return-object p1
.end method

.method public static convert(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;
    .locals 3

    .line 1
    new-instance v0, Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;

    invoke-direct {v0}, Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;-><init>()V

    .line 3
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v1

    .line 4
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getWidth()I

    move-result v2

    iput v2, v0, Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;->width:I

    .line 5
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getHeight()I

    move-result v2

    iput v2, v0, Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;->height:I

    .line 6
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getFormatType()I

    move-result v2

    iput v2, v0, Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;->format:I

    .line 7
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getQuadrant()I

    move-result v1

    iput v1, v0, Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;->rotation:I

    .line 9
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object v1

    if-eqz v1, :cond_0

    .line 11
    invoke-virtual {v1}, Ljava/nio/ByteBuffer;->array()[B

    move-result-object v1

    iput-object v1, v0, Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;->bytes:[B

    .line 14
    :cond_0
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->readBitmap()Landroid/graphics/Bitmap;

    move-result-object p0

    iput-object p0, v0, Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;->bitmap:Landroid/graphics/Bitmap;

    return-object v0
.end method

.method static elementParcelToElement(Lcom/huawei/hms/ml/common/ocr/ElementParcel;)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;
    .locals 7

    .line 1
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 2
    new-instance v0, Lcom/huawei/hms/mlsdk/text/TextLanguage;

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/ElementParcel;->getLanguage()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/huawei/hms/mlsdk/text/TextLanguage;-><init>(Ljava/lang/String;)V

    invoke-interface {v3, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 3
    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/ElementParcel;->getCornerPoints()Ljava/util/List;

    move-result-object v0

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->pointsToArray(Ljava/util/List;)[Landroid/graphics/Point;

    move-result-object v4

    .line 4
    new-instance v6, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/ElementParcel;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/ElementParcel;->getElementRect()Landroid/graphics/Rect;

    move-result-object v2

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/ElementParcel;->getConfidence()F

    move-result v5

    move-object v0, v6

    invoke-direct/range {v0 .. v5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;[Landroid/graphics/Point;F)V

    return-object v6
.end method

.method static getCornerPoints(Ljava/util/List;)[Landroid/graphics/Point;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;",
            ">;)[",
            "Landroid/graphics/Point;"
        }
    .end annotation

    .line 1
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    new-array p0, v1, [Landroid/graphics/Point;

    goto :goto_2

    :cond_0
    const/high16 v0, -0x80000000

    const v2, 0x7fffffff

    move v5, v1

    move v3, v2

    move v4, v3

    move v2, v0

    .line 9
    :goto_0
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v6

    if-ge v5, v6, :cond_2

    .line 10
    invoke-interface {p0, v5}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;

    invoke-interface {p0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;

    invoke-static {v6, v7}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->compare(Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;)[Landroid/graphics/Point;

    move-result-object v6

    move v7, v1

    .line 11
    :goto_1
    array-length v8, v6

    if-ge v7, v8, :cond_1

    .line 12
    aget-object v8, v6, v7

    .line 13
    iget v9, v8, Landroid/graphics/Point;->x:I

    invoke-static {v3, v9}, Ljava/lang/Math;->min(II)I

    move-result v3

    .line 14
    iget v9, v8, Landroid/graphics/Point;->x:I

    invoke-static {v0, v9}, Ljava/lang/Math;->max(II)I

    move-result v0

    .line 15
    iget v9, v8, Landroid/graphics/Point;->y:I

    invoke-static {v4, v9}, Ljava/lang/Math;->min(II)I

    move-result v4

    .line 16
    iget v8, v8, Landroid/graphics/Point;->y:I

    invoke-static {v2, v8}, Ljava/lang/Math;->max(II)I

    move-result v2

    add-int/lit8 v7, v7, 0x1

    goto :goto_1

    :cond_1
    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    .line 19
    :cond_2
    invoke-interface {p0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;

    invoke-static {v3, v4, v0, v2, p0}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->compare(IIIILcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;)[Landroid/graphics/Point;

    move-result-object p0

    :goto_2
    return-object p0
.end method

.method static lineParcelToLine(Lcom/huawei/hms/ml/common/ocr/LineParcel;)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;
    .locals 8

    .line 1
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 2
    new-instance v0, Lcom/huawei/hms/mlsdk/text/TextLanguage;

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/LineParcel;->getLanguage()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Lcom/huawei/hms/mlsdk/text/TextLanguage;-><init>(Ljava/lang/String;)V

    invoke-interface {v3, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 3
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 4
    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/LineParcel;->getElements()Ljava/util/List;

    move-result-object v0

    .line 5
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/ml/common/ocr/ElementParcel;

    .line 6
    invoke-static {v1}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->elementParcelToElement(Lcom/huawei/hms/ml/common/ocr/ElementParcel;)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;

    move-result-object v1

    .line 7
    invoke-interface {v4, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 9
    :cond_0
    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/LineParcel;->getCornerPoints()Ljava/util/List;

    move-result-object v0

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->pointsToArray(Ljava/util/List;)[Landroid/graphics/Point;

    move-result-object v5

    .line 10
    new-instance v7, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/LineParcel;->getText()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/LineParcel;->getLineRect()Landroid/graphics/Rect;

    move-result-object v2

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/LineParcel;->getConfidence()F

    move-result v6

    move-object v0, v7

    invoke-direct/range {v0 .. v6}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V

    return-object v7
.end method

.method static lineSparseArrayToBlock(Landroid/util/SparseArray;)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/util/SparseArray<",
            "Lcom/huawei/hms/ml/common/ocr/LineParcel;",
            ">;)",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;"
        }
    .end annotation

    if-eqz p0, :cond_3

    .line 1
    invoke-virtual {p0}, Landroid/util/SparseArray;->size()I

    move-result v0

    if-nez v0, :cond_0

    goto :goto_1

    .line 4
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 6
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 7
    new-instance v5, Ljava/util/ArrayList;

    invoke-direct {v5}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x0

    .line 11
    invoke-virtual {p0}, Landroid/util/SparseArray;->size()I

    move-result v2

    const/4 v3, 0x0

    :goto_0
    if-ge v3, v2, :cond_2

    .line 13
    invoke-virtual {p0, v3}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Lcom/huawei/hms/ml/common/ocr/LineParcel;

    .line 14
    invoke-static {v6}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->lineParcelToLine(Lcom/huawei/hms/ml/common/ocr/LineParcel;)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;

    move-result-object v7

    .line 15
    invoke-interface {v5, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 16
    invoke-virtual {v6}, Lcom/huawei/hms/ml/common/ocr/LineParcel;->getConfidence()F

    move-result v8

    add-float/2addr v1, v8

    .line 17
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->length()I

    move-result v8

    if-lez v8, :cond_1

    .line 18
    invoke-static {}, Ljava/lang/System;->lineSeparator()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v0, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 20
    :cond_1
    invoke-virtual {v7}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getStringValue()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v0, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 21
    new-instance v7, Lcom/huawei/hms/mlsdk/text/TextLanguage;

    invoke-virtual {v6}, Lcom/huawei/hms/ml/common/ocr/LineParcel;->getLanguage()Ljava/lang/String;

    move-result-object v6

    invoke-direct {v7, v6}, Lcom/huawei/hms/mlsdk/text/TextLanguage;-><init>(Ljava/lang/String;)V

    invoke-interface {v4, v7}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_2
    int-to-float p0, v2

    div-float v7, v1, p0

    .line 24
    invoke-static {v5}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->getCornerPoints(Ljava/util/List;)[Landroid/graphics/Point;

    move-result-object v6

    .line 25
    invoke-static {v5}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->pointsToRect(Ljava/util/List;)Landroid/graphics/Rect;

    move-result-object v3

    .line 27
    new-instance p0, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    move-object v1, p0

    invoke-direct/range {v1 .. v7}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V

    return-object p0

    :cond_3
    :goto_1
    const/4 p0, 0x0

    return-object p0
.end method

.method static pointsToArray(Ljava/util/List;)[Landroid/graphics/Point;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroid/graphics/Point;",
            ">;)[",
            "Landroid/graphics/Point;"
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p0, :cond_1

    .line 1
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_1

    .line 2
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v1

    new-array v1, v1, [Landroid/graphics/Point;

    .line 3
    :goto_0
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v2

    if-ge v0, v2, :cond_0

    .line 4
    invoke-interface {p0, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/graphics/Point;

    aput-object v2, v1, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-object v1

    :cond_1
    new-array p0, v0, [Landroid/graphics/Point;

    return-object p0
.end method

.method static pointsToRect(Ljava/util/List;)Landroid/graphics/Rect;
    .locals 9
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;",
            ">;)",
            "Landroid/graphics/Rect;"
        }
    .end annotation

    .line 1
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    const/high16 v0, -0x80000000

    const v1, 0x7fffffff

    move v2, v1

    move v3, v2

    move v1, v0

    :cond_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;

    .line 2
    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getVertexes()[Landroid/graphics/Point;

    move-result-object v4

    .line 3
    array-length v5, v4

    const/4 v6, 0x0

    :goto_0
    if-ge v6, v5, :cond_0

    .line 5
    aget-object v7, v4, v6

    .line 6
    iget v8, v7, Landroid/graphics/Point;->x:I

    invoke-static {v2, v8}, Ljava/lang/Math;->min(II)I

    move-result v2

    .line 7
    iget v8, v7, Landroid/graphics/Point;->x:I

    invoke-static {v0, v8}, Ljava/lang/Math;->max(II)I

    move-result v0

    .line 8
    iget v8, v7, Landroid/graphics/Point;->y:I

    invoke-static {v3, v8}, Ljava/lang/Math;->min(II)I

    move-result v3

    .line 9
    iget v7, v7, Landroid/graphics/Point;->y:I

    invoke-static {v1, v7}, Ljava/lang/Math;->max(II)I

    move-result v1

    add-int/lit8 v6, v6, 0x1

    goto :goto_0

    .line 12
    :cond_1
    new-instance p0, Landroid/graphics/Rect;

    invoke-direct {p0, v2, v3, v0, v1}, Landroid/graphics/Rect;-><init>(IIII)V

    return-object p0
.end method

.method static textParcelToBlock(Lcom/huawei/hms/ml/common/ocr/TextParcel;)Landroid/util/SparseArray;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/ml/common/ocr/TextParcel;",
            ")",
            "Landroid/util/SparseArray<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;",
            ">;"
        }
    .end annotation

    if-eqz p0, :cond_6

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/TextParcel;->getBlocks()Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_6

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/TextParcel;->getBlocks()Ljava/util/List;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-nez v0, :cond_0

    goto/16 :goto_3

    .line 4
    :cond_0
    new-instance v0, Landroid/util/SparseArray;

    invoke-direct {v0}, Landroid/util/SparseArray;-><init>()V

    .line 5
    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/TextParcel;->getBlocks()Ljava/util/List;

    move-result-object p0

    .line 6
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 7
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_1
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/ml/common/ocr/TextBlockParcel;

    .line 8
    invoke-virtual {v2}, Lcom/huawei/hms/ml/common/ocr/TextBlockParcel;->getLines()Ljava/util/List;

    move-result-object v2

    if-eqz v2, :cond_1

    .line 9
    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v3

    if-eqz v3, :cond_1

    .line 10
    invoke-interface {v1, v2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    .line 13
    :cond_2
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result p0

    const/4 v2, 0x0

    move v3, v2

    :goto_1
    if-ge v3, p0, :cond_4

    .line 15
    invoke-interface {v1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/huawei/hms/ml/common/ocr/LineParcel;

    .line 16
    invoke-virtual {v4}, Lcom/huawei/hms/ml/common/ocr/LineParcel;->getBlockId()I

    move-result v5

    invoke-virtual {v0, v5}, Landroid/util/SparseArray;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Landroid/util/SparseArray;

    if-nez v5, :cond_3

    .line 18
    new-instance v5, Landroid/util/SparseArray;

    invoke-direct {v5}, Landroid/util/SparseArray;-><init>()V

    .line 19
    invoke-virtual {v4}, Lcom/huawei/hms/ml/common/ocr/LineParcel;->getBlockId()I

    move-result v6

    invoke-virtual {v0, v6, v5}, Landroid/util/SparseArray;->append(ILjava/lang/Object;)V

    .line 21
    :cond_3
    invoke-virtual {v4}, Lcom/huawei/hms/ml/common/ocr/LineParcel;->getLineNum()I

    move-result v6

    invoke-virtual {v5, v6, v4}, Landroid/util/SparseArray;->append(ILjava/lang/Object;)V

    add-int/lit8 v3, v3, 0x1

    goto :goto_1

    .line 23
    :cond_4
    new-instance p0, Landroid/util/SparseArray;

    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v1

    invoke-direct {p0, v1}, Landroid/util/SparseArray;-><init>(I)V

    .line 24
    :goto_2
    invoke-virtual {v0}, Landroid/util/SparseArray;->size()I

    move-result v1

    if-ge v2, v1, :cond_5

    .line 25
    invoke-virtual {v0, v2}, Landroid/util/SparseArray;->keyAt(I)I

    move-result v1

    invoke-virtual {v0, v2}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Landroid/util/SparseArray;

    invoke-static {v3}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->lineSparseArrayToBlock(Landroid/util/SparseArray;)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;

    move-result-object v3

    invoke-virtual {p0, v1, v3}, Landroid/util/SparseArray;->append(ILjava/lang/Object;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_2

    :cond_5
    return-object p0

    .line 26
    :cond_6
    :goto_3
    new-instance p0, Landroid/util/SparseArray;

    invoke-direct {p0}, Landroid/util/SparseArray;-><init>()V

    return-object p0
.end method

.method static textParcelToBlockList(Lcom/huawei/hms/ml/common/ocr/TextParcel;)Ljava/util/List;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/ml/common/ocr/TextParcel;",
            ")",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/TextParcel;->getBlocks()Ljava/util/List;

    move-result-object p0

    .line 2
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 3
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/ml/common/ocr/TextBlockParcel;

    .line 4
    invoke-virtual {v1}, Lcom/huawei/hms/ml/common/ocr/TextBlockParcel;->getLines()Ljava/util/List;

    move-result-object v2

    .line 5
    new-instance v6, Ljava/util/ArrayList;

    invoke-direct {v6}, Ljava/util/ArrayList;-><init>()V

    .line 6
    new-instance v7, Ljava/util/ArrayList;

    invoke-direct {v7}, Ljava/util/ArrayList;-><init>()V

    .line 7
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_1
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_0

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/huawei/hms/ml/common/ocr/LineParcel;

    .line 8
    invoke-static {v3}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->lineParcelToLine(Lcom/huawei/hms/ml/common/ocr/LineParcel;)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;

    move-result-object v3

    .line 9
    invoke-interface {v7, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 10
    new-instance v4, Lcom/huawei/hms/mlsdk/text/TextLanguage;

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getLanguage()Ljava/lang/String;

    move-result-object v3

    invoke-direct {v4, v3}, Lcom/huawei/hms/mlsdk/text/TextLanguage;-><init>(Ljava/lang/String;)V

    invoke-interface {v6, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 12
    :cond_0
    new-instance v2, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;

    iget-object v4, v1, Lcom/huawei/hms/ml/common/ocr/TextBlockParcel;->text:Ljava/lang/String;

    invoke-virtual {v1}, Lcom/huawei/hms/ml/common/ocr/TextBlockParcel;->getBlockRect()Landroid/graphics/Rect;

    move-result-object v5

    invoke-virtual {v1}, Lcom/huawei/hms/ml/common/ocr/TextBlockParcel;->getCornerPoints()Ljava/util/List;

    move-result-object v3

    invoke-static {v3}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->pointsToArray(Ljava/util/List;)[Landroid/graphics/Point;

    move-result-object v8

    invoke-virtual {v1}, Lcom/huawei/hms/ml/common/ocr/TextBlockParcel;->getConfidence()F

    move-result v9

    move-object v3, v2

    invoke-direct/range {v3 .. v9}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method static textParcelToMLPlate(Lcom/huawei/hms/ml/common/ocr/TextParcel;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/ml/common/ocr/TextParcel;",
            ")",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/MLPlate;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 2
    invoke-static {p0}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->textParcelToTextPlate(Lcom/huawei/hms/ml/common/ocr/TextParcel;)Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 3
    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/ocr/TextParcel;->getPlateParcels()Ljava/util/List;

    move-result-object p0

    invoke-static {p0}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->textParcelToMLPlateList(Ljava/util/List;)Ljava/util/List;

    move-result-object p0

    invoke-interface {v0, p0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    return-object v0
.end method

.method static textParcelToMLPlateList(Ljava/util/List;)Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/ml/common/ocr/TextPlateParcel;",
            ">;)",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/MLPlate;",
            ">;"
        }
    .end annotation

    if-eqz p0, :cond_3

    .line 1
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v0

    if-nez v0, :cond_0

    goto :goto_1

    .line 5
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 6
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_1
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/ml/common/ocr/TextPlateParcel;

    .line 8
    invoke-virtual {v1}, Lcom/huawei/hms/ml/common/ocr/TextPlateParcel;->getType()I

    move-result v2

    const/4 v3, 0x1

    if-ne v2, v3, :cond_1

    .line 9
    new-instance v2, Lcom/huawei/hms/mlsdk/text/entity/imageplate/MLImagePlate;

    invoke-virtual {v1}, Lcom/huawei/hms/ml/common/ocr/TextPlateParcel;->getRect()Landroid/graphics/Rect;

    move-result-object v3

    invoke-virtual {v1}, Lcom/huawei/hms/ml/common/ocr/TextPlateParcel;->getPoints()Ljava/util/List;

    move-result-object v4

    const/4 v5, 0x0

    new-array v5, v5, [Landroid/graphics/Point;

    invoke-interface {v4, v5}, Ljava/util/List;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    move-result-object v4

    check-cast v4, [Landroid/graphics/Point;

    invoke-virtual {v1}, Lcom/huawei/hms/ml/common/ocr/TextPlateParcel;->getPossibility()F

    move-result v1

    invoke-direct {v2, v3, v4, v1}, Lcom/huawei/hms/mlsdk/text/entity/imageplate/MLImagePlate;-><init>(Landroid/graphics/Rect;[Landroid/graphics/Point;F)V

    .line 10
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_2
    return-object v0

    .line 11
    :cond_3
    :goto_1
    new-instance p0, Ljava/util/ArrayList;

    invoke-direct {p0}, Ljava/util/ArrayList;-><init>()V

    return-object p0
.end method

.method static textParcelToTextPlate(Lcom/huawei/hms/ml/common/ocr/TextParcel;)Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;
    .locals 1

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;

    invoke-static {p0}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->textParcelToBlockList(Lcom/huawei/hms/ml/common/ocr/TextParcel;)Ljava/util/List;

    move-result-object p0

    invoke-direct {v0, p0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;-><init>(Ljava/util/List;)V

    return-object v0
.end method
