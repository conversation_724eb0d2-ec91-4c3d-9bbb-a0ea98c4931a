.class public Lcom/huawei/hms/mlsdk/face/MLFaceShape;
.super Ljava/lang/Object;
.source "MLFaceShape.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/face/MLFaceShape$ShapeType;
    }
.end annotation


# static fields
.field public static final TYPE_ALL:I = 0x0

.field public static final TYPE_BOTTOM_OF_LEFT_EYEBROW:I = 0x4

.field public static final TYPE_BOTTOM_OF_LOWER_LIP:I = 0x8

.field public static final TYPE_BOTTOM_OF_NOSE:I = 0xc

.field public static final TYPE_BOTTOM_OF_RIGHT_EYEBROW:I = 0x5

.field public static final TYPE_BOTTOM_OF_UPPER_LIP:I = 0xa

.field public static final TYPE_BRIDGE_OF_NOSE:I = 0xd

.field public static final TYPE_FACE:I = 0x1

.field public static final TYPE_LEFT_EYE:I = 0x2

.field public static final TYPE_RIGHT_EYE:I = 0x3

.field public static final TYPE_TOP_OF_LEFT_EYEBROW:I = 0x6

.field public static final TYPE_TOP_OF_LOWER_LIP:I = 0x9

.field public static final TYPE_TOP_OF_RIGHT_EYEBROW:I = 0x7

.field public static final TYPE_TOP_OF_UPPER_LIP:I = 0xb


# instance fields
.field private coordinatePoints:[Landroid/graphics/PointF;

.field private faceShapeType:I

.field private points:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(ILjava/util/List;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v0

    new-array v0, v0, [Landroid/graphics/PointF;

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->coordinatePoints:[Landroid/graphics/PointF;

    const/4 v0, 0x0

    .line 3
    :goto_0
    invoke-interface {p2}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_0

    .line 4
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->coordinatePoints:[Landroid/graphics/PointF;

    new-instance v2, Landroid/graphics/PointF;

    invoke-interface {p2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/huawei/hms/mlsdk/common/MLPosition;

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getX()Ljava/lang/Float;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Float;->floatValue()F

    move-result v3

    invoke-interface {p2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/huawei/hms/mlsdk/common/MLPosition;

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getY()Ljava/lang/Float;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Float;->floatValue()F

    move-result v4

    invoke-direct {v2, v3, v4}, Landroid/graphics/PointF;-><init>(FF)V

    aput-object v2, v1, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 6
    :cond_0
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->faceShapeType:I

    .line 7
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->points:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public getCoordinatePoints()[Landroid/graphics/PointF;
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->coordinatePoints:[Landroid/graphics/PointF;

    if-eqz v0, :cond_0

    array-length v1, v0

    if-lez v1, :cond_0

    .line 2
    array-length v1, v0

    invoke-static {v0, v1}, Ljava/util/Arrays;->copyOf([Ljava/lang/Object;I)[Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Landroid/graphics/PointF;

    return-object v0

    .line 5
    :cond_0
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->points:Ljava/util/List;

    const/4 v1, 0x0

    if-eqz v0, :cond_4

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-gtz v0, :cond_1

    goto :goto_1

    .line 9
    :cond_1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->points:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    new-array v0, v0, [Landroid/graphics/PointF;

    .line 10
    :goto_0
    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->points:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_3

    .line 11
    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->points:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    if-eqz v2, :cond_2

    .line 12
    new-instance v2, Landroid/graphics/PointF;

    iget-object v3, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->points:Ljava/util/List;

    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/huawei/hms/mlsdk/common/MLPosition;

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getX()Ljava/lang/Float;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/Float;->floatValue()F

    move-result v3

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->points:Ljava/util/List;

    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/huawei/hms/mlsdk/common/MLPosition;

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getY()Ljava/lang/Float;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/Float;->floatValue()F

    move-result v4

    invoke-direct {v2, v3, v4}, Landroid/graphics/PointF;-><init>(FF)V

    aput-object v2, v0, v1

    :cond_2
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_3
    return-object v0

    :cond_4
    :goto_1
    new-array v0, v1, [Landroid/graphics/PointF;

    return-object v0
.end method

.method public getFaceShapeType()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->faceShapeType:I

    return v0
.end method

.method public getPoints()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->points:Ljava/util/List;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 1
    invoke-static {p0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->toStringHelper(Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->faceShapeType:I

    .line 2
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "type"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->points:Ljava/util/List;

    const-string v2, "position"

    .line 3
    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    .line 4
    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
