.class public abstract Lcom/huawei/hms/mlsdk/text/MLPlate;
.super Ljava/lang/Object;
.source "MLPlate.java"

# interfaces
.implements Lcom/huawei/hms/mlsdk/text/IMLPlate;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract getMLPlate()Lcom/huawei/hms/mlsdk/text/MLPlate;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Lcom/huawei/hms/mlsdk/text/MLPlate;",
            ">()TT;"
        }
    .end annotation
.end method

.method public abstract getMLPlateType()Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;
.end method
