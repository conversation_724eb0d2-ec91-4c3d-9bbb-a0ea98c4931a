.class public Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;
.super Lcom/huawei/hms/mlsdk/common/MLAnalyzer;
.source "MLTextAnalyzer.java"

# interfaces
.implements Ljava/io/Closeable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$Factory;,
        Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$HmsMLVisionTextTask;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/huawei/hms/mlsdk/common/MLAnalyzer<",
        "Lcom/huawei/hms/mlsdk/text/MLText$Block;",
        ">;",
        "Ljava/io/Closeable;"
    }
.end annotation


# static fields
.field public static final OCR_LOCAL_TYPE:I = 0x0

.field public static final OCR_REMOTE_TYPE:I = 0x1

.field private static cloudTextRecognizer:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;",
            "Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;",
            ">;"
        }
    .end annotation
.end field

.field private static localTextRecognizer:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;",
            "Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private final analyseType:I

.field private final cloudTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

.field private localTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

.field private nativeTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextRecognizer:Ljava/util/Map;

    .line 2
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->cloudTextRecognizer:Ljava/util/Map;

    return-void
.end method

.method private constructor <init>(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;I)V
    .locals 0

    .line 2
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/common/MLAnalyzer;-><init>()V

    .line 3
    iput p3, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->analyseType:I

    .line 4
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    .line 5
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->cloudTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    const/4 p1, 0x0

    .line 6
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->nativeTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;

    return-void
.end method

.method private constructor <init>(Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;)V
    .locals 0

    .line 7
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/common/MLAnalyzer;-><init>()V

    .line 8
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->nativeTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;

    const/4 p1, 0x0

    .line 9
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    .line 10
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->cloudTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    const/4 p1, 0x0

    .line 11
    iput p1, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->analyseType:I

    return-void
.end method

.method synthetic constructor <init>(Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$1;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;-><init>(Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;)V

    return-void
.end method

.method public static declared-synchronized create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;Z)Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;
    .locals 2

    const-class v0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;

    monitor-enter v0

    if-nez p3, :cond_0

    if-nez p2, :cond_0

    .line 1
    :try_start_0
    new-instance p2, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;

    invoke-direct {p2}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;-><init>()V

    invoke-virtual {p2}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->create()Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    move-result-object p2

    goto :goto_0

    :catchall_0
    move-exception p0

    goto :goto_1

    :cond_0
    :goto_0
    if-eqz p3, :cond_1

    if-nez p1, :cond_1

    .line 4
    new-instance p1, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;

    invoke-direct {p1}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;-><init>()V

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->create()Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    move-result-object p1

    :cond_1
    const/4 v1, 0x0

    if-eqz p3, :cond_4

    .line 7
    invoke-static {p0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->getInstance(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;)Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    move-result-object p0

    .line 8
    sget-object p1, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextRecognizer:Ljava/util/Map;

    invoke-interface {p1, p0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;

    if-nez p1, :cond_2

    .line 9
    new-instance p1, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;

    const/4 p2, 0x0

    invoke-direct {p1, p0, v1, p2}, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;-><init>(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;I)V

    .line 10
    sget-object p2, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextRecognizer:Ljava/util/Map;

    invoke-interface {p2, p0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 12
    :cond_2
    iget-object p2, p1, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    if-nez p2, :cond_3

    .line 13
    iput-object p0, p1, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_3
    monitor-exit v0

    return-object p1

    .line 17
    :cond_4
    :try_start_1
    invoke-static {p0, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;)Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    move-result-object p0

    .line 18
    sget-object p1, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->cloudTextRecognizer:Ljava/util/Map;

    invoke-interface {p1, p0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;

    if-nez p1, :cond_5

    .line 19
    new-instance p1, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;

    const/4 p2, 0x1

    invoke-direct {p1, v1, p0, p2}, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;-><init>(Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;I)V

    .line 20
    sget-object p2, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->cloudTextRecognizer:Ljava/util/Map;

    invoke-interface {p2, p0, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :cond_5
    monitor-exit v0

    return-object p1

    :goto_1
    monitor-exit v0

    throw p0
.end method


# virtual methods
.method public analyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Landroid/util/SparseArray;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Landroid/util/SparseArray<",
            "Lcom/huawei/hms/mlsdk/text/MLText$Block;",
            ">;"
        }
    .end annotation

    if-eqz p1, :cond_0

    .line 1
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->initialize()V

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 2
    invoke-virtual {p1, v0, v1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getFrame(ZZ)Lcom/huawei/hms/mlsdk/common/MLFrame;

    move-result-object p1

    .line 4
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->nativeTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->analyse(Lcom/huawei/hms/mlsdk/common/MLFrame;)Landroid/util/SparseArray;

    move-result-object p1

    return-object p1

    .line 5
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "No frame supplied."

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public asyncAnalyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Lcom/huawei/hmf/tasks/Task<",
            "Lcom/huawei/hms/mlsdk/text/MLText;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->cloudTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    if-nez v0, :cond_0

    .line 2
    new-instance p1, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$HmsMLVisionTextTask;

    invoke-direct {p1}, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer$HmsMLVisionTextTask;-><init>()V

    invoke-static {p1}, Lcom/huawei/hmf/tasks/Tasks;->call(Ljava/util/concurrent/Callable;)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    return-object p1

    .line 5
    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->initialize()V

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 6
    invoke-virtual {p1, v0, v1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getFrame(ZZ)Lcom/huawei/hms/mlsdk/common/MLFrame;

    move-result-object p1

    .line 7
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    if-eqz v0, :cond_1

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->processImage(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->cloudTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->processImage(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    :goto_0
    return-object p1
.end method

.method public asyncDetectFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Lcom/huawei/hmf/tasks/Task<",
            "Lcom/huawei/hms/mlsdk/text/MLText;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    if-nez v0, :cond_0

    .line 2
    new-instance p1, Ljava/lang/Exception;

    const-string v0, "The asyncDetectFrame function can be called only by the LocalTextAnalyzer object."

    invoke-direct {p1, v0}, Ljava/lang/Exception;-><init>(Ljava/lang/String;)V

    invoke-static {p1}, Lcom/huawei/hmf/tasks/Tasks;->fromException(Ljava/lang/Exception;)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    return-object p1

    .line 4
    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->initialize()V

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 5
    invoke-virtual {p1, v0, v1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getFrame(ZZ)Lcom/huawei/hms/mlsdk/common/MLFrame;

    move-result-object p1

    .line 6
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->detect(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    return-object p1
.end method

.method public close()V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->release()V

    return-void
.end method

.method public detectFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Landroid/util/SparseArray;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Landroid/util/SparseArray<",
            "Lcom/huawei/hms/mlsdk/text/MLText$Block;",
            ">;"
        }
    .end annotation

    if-eqz p1, :cond_0

    .line 1
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->initialize()V

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 2
    invoke-virtual {p1, v0, v1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getFrame(ZZ)Lcom/huawei/hms/mlsdk/common/MLFrame;

    move-result-object p1

    .line 4
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->nativeTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->detect(Lcom/huawei/hms/mlsdk/common/MLFrame;)Landroid/util/SparseArray;

    move-result-object p1

    return-object p1

    .line 5
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "No frame supplied."

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public getAnalyseType()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->analyseType:I

    return v0
.end method

.method public isAvailable()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->nativeTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;

    if-eqz v0, :cond_0

    .line 2
    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->isAvailable()Z

    move-result v0

    return v0

    .line 4
    :cond_0
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    if-eqz v0, :cond_1

    .line 5
    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->isAvailable()Z

    move-result v0

    return v0

    :cond_1
    const/4 v0, 0x0

    return v0
.end method

.method public release()V
    .locals 2

    .line 1
    invoke-super {p0}, Lcom/huawei/hms/mlsdk/common/MLAnalyzer;->destroy()V

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->nativeTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 3
    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->release()V

    .line 4
    iput-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->nativeTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;

    .line 6
    :cond_0
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    if-eqz v0, :cond_1

    .line 7
    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;->release()V

    .line 8
    iput-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->localTextAnalyzer:Lcom/huawei/hms/mlsdk/text/internal/client/LocalTextAnalyzer;

    :cond_1
    return-void
.end method

.method public stop()V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/text/MLTextAnalyzer;->release()V

    return-void
.end method
