.class public Lcom/huawei/hms/network/ai/m0$a;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/network/ai/m0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static a(Ljava/util/Map;)Lcom/huawei/hms/network/ai/m0;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "+",
            "Ljava/lang/Number;",
            ">;)",
            "Lcom/huawei/hms/network/ai/m0;"
        }
    .end annotation

    const/4 p0, 0x0

    return-object p0
.end method

.method public static a([DZ)Lcom/huawei/hms/network/ai/m0;
    .locals 0

    const/4 p0, 0x0

    return-object p0
.end method

.method public static a([FZ)Lcom/huawei/hms/network/ai/m0;
    .locals 0

    const/4 p0, 0x0

    return-object p0
.end method
