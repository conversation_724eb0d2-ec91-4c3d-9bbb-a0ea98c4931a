.class Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;
.super Ljava/lang/Object;
.source "CloudTextAnalyzer.java"

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->processImage(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Lcom/huawei/hms/mlsdk/text/MLText;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

.field final synthetic val$frame:Lcom/huawei/hms/mlsdk/common/MLFrame;


# direct methods
.method constructor <init>(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    iput-object p2, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->val$frame:Lcom/huawei/hms/mlsdk/common/MLFrame;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public call()Lcom/huawei/hms/mlsdk/text/MLText;
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->access$000(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;)Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->isEnableFingerprintVerification()Z

    move-result v0

    const/4 v1, 0x2

    if-eqz v0, :cond_1

    .line 3
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->access$100(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppSetting()Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;->getCertFingerprint()Ljava/lang/String;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 4
    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    .line 5
    :cond_0
    new-instance v0, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v2, "Failed to analyse."

    invoke-direct {v0, v2, v1}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v0

    .line 9
    :cond_1
    :goto_0
    invoke-static {}, Lcom/huawei/hms/ml/grs/GrsRegionUtils;->getGrsCountryCode()Ljava/lang/String;

    move-result-object v0

    .line 10
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "GRS countryCode is "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    const-string v3, "CloudTextAnalyzer"

    invoke-static {v3, v2}, Lcom/huawei/hms/ml/common/utils/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    if-eqz v0, :cond_2

    .line 14
    invoke-static {}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getInstance()Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v2

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v2

    invoke-static {v2, v0}, Lcom/huawei/hms/ml/grs/GrsUtils;->initGrsVisionSearchClientWithCountry(Landroid/content/Context;Ljava/lang/String;)Lcom/huawei/hms/framework/network/grs/GrsClient;

    move-result-object v0

    .line 15
    invoke-static {v0}, Lcom/huawei/hms/ml/grs/GrsUtils;->getVisionSearchUrls(Lcom/huawei/hms/framework/network/grs/GrsClient;)Ljava/util/List;

    move-result-object v0

    goto :goto_1

    .line 17
    :cond_2
    invoke-static {}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getInstance()Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v0

    const/4 v2, 0x0

    invoke-static {v0, v2}, Lcom/huawei/hms/ml/grs/GrsUtils;->getUrls(Landroid/content/Context;Z)Ljava/util/List;

    move-result-object v0

    .line 19
    :goto_1
    invoke-static {v0}, Lcom/huawei/hms/ml/grs/GrsUtils;->addHttpsHeaders(Ljava/util/List;)Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_4

    .line 20
    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_4

    .line 24
    new-instance v2, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    invoke-direct {v2}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;-><init>()V

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a()Lcom/huawei/hms/mlsdk/internal/client/rest/e;

    move-result-object v2

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/internal/client/rest/e;->a()Ljava/util/Map;

    move-result-object v2

    .line 25
    iget-object v3, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    invoke-static {v3, v2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->access$200(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Ljava/util/Map;)Z

    move-result v3

    if-nez v3, :cond_3

    .line 29
    iget-object v3, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->val$frame:Lcom/huawei/hms/mlsdk/common/MLFrame;

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLFrame;->readBitmap()Landroid/graphics/Bitmap;

    move-result-object v4

    invoke-static {v3, v4}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->access$300(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Landroid/graphics/Bitmap;)F

    move-result v3

    .line 31
    iget-object v4, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    iget-object v5, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->val$frame:Lcom/huawei/hms/mlsdk/common/MLFrame;

    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/common/MLFrame;->readBitmap()Landroid/graphics/Bitmap;

    move-result-object v5

    invoke-static {v4, v5, v3}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->access$400(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Landroid/graphics/Bitmap;F)Landroid/graphics/Bitmap;

    move-result-object v4

    const/16 v5, 0x5a

    .line 32
    invoke-static {v4, v5}, Lcom/huawei/hms/ml/common/utils/ImageConvertUtils;->bitmap2Jpeg(Landroid/graphics/Bitmap;I)[B

    move-result-object v4

    .line 33
    invoke-static {v4, v1}, Landroid/util/Base64;->encodeToString([BI)Ljava/lang/String;

    move-result-object v1

    .line 36
    iget-object v4, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    invoke-static {v4}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->access$000(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;)Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    move-result-object v5

    invoke-static {v4, v1, v5}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->access$500(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Ljava/lang/String;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;)Ljava/lang/String;

    move-result-object v1

    .line 39
    iget-object v4, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    invoke-static {v4, v0, v2, v1}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->access$600(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Ljava/util/List;Ljava/util/Map;Ljava/lang/String;)Lcom/huawei/hms/network/httpclient/Response;

    move-result-object v0

    .line 40
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    invoke-static {v1, v0, v3}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->access$700(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Lcom/huawei/hms/network/httpclient/Response;F)Lcom/huawei/hms/mlsdk/text/MLText;

    move-result-object v0

    return-object v0

    .line 41
    :cond_3
    new-instance v0, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v2, "Header param error, fail to detect cloud text"

    invoke-direct {v0, v2, v1}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v0

    .line 42
    :cond_4
    new-instance v0, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v2, "UrlList is empty, failed to detect cloud text."

    invoke-direct {v0, v2, v1}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v0
.end method

.method public bridge synthetic call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;->call()Lcom/huawei/hms/mlsdk/text/MLText;

    move-result-object v0

    return-object v0
.end method
