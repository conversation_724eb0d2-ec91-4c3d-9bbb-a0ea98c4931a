.class public interface abstract Lcom/huawei/hms/network/ai/g;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract a()Ljava/lang/Object;
.end method

.method public abstract a(Landroid/database/sqlite/SQLiteDatabase;)V
.end method

.method public abstract a(Landroid/database/sqlite/SQLiteDatabase;II)V
.end method

.method public abstract a(Ljava/lang/Object;)V
.end method

.method public abstract b()Ljava/lang/Object;
.end method

.method public abstract c()V
.end method

.method public abstract d()V
.end method
