.class public interface abstract Lcom/huawei/hms/mlsdk/internal/client/adapter/IAdapter;
.super Ljava/lang/Object;
.source "IAdapter.java"


# virtual methods
.method public abstract isAvailable(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)Z
.end method

.method public abstract notifyDownloadIfNeeded(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)V
.end method

.method public abstract release(Landroid/content/Context;)V
.end method
