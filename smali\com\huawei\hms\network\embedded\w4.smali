.class public Lcom/huawei/hms/network/embedded/w4;
.super Lcom/huawei/hms/network/embedded/v4;
.source ""


# instance fields
.field public a:Lcom/huawei/hms/network/embedded/c5;

.field public b:Lcom/huawei/hms/network/embedded/s4;

.field public c:Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;

.field public d:Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;

.field public e:Lcom/huawei/hms/network/embedded/y4;

.field public f:I

.field public g:I

.field public h:I


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Lcom/huawei/hms/network/embedded/v4;-><init>()V

    new-instance v0, Lcom/huawei/hms/network/embedded/b5;

    invoke-direct {v0}, Lcom/huawei/hms/network/embedded/b5;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/network/embedded/w4;->a:Lcom/huawei/hms/network/embedded/c5;

    new-instance v0, Lcom/huawei/hms/network/embedded/r4;

    invoke-direct {v0}, Lcom/huawei/hms/network/embedded/r4;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/network/embedded/w4;->b:Lcom/huawei/hms/network/embedded/s4;

    new-instance v0, Lcom/huawei/hms/network/embedded/a5;

    invoke-direct {v0}, Lcom/huawei/hms/network/embedded/a5;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/network/embedded/w4;->c:Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;

    iput-object v0, p0, Lcom/huawei/hms/network/embedded/w4;->d:Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;

    new-instance v0, Lcom/huawei/hms/network/embedded/x4;

    invoke-direct {v0}, Lcom/huawei/hms/network/embedded/x4;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/network/embedded/w4;->e:Lcom/huawei/hms/network/embedded/y4;

    const/4 v0, 0x0

    iput v0, p0, Lcom/huawei/hms/network/embedded/w4;->f:I

    iput v0, p0, Lcom/huawei/hms/network/embedded/w4;->g:I

    iput v0, p0, Lcom/huawei/hms/network/embedded/w4;->h:I

    return-void
.end method


# virtual methods
.method public a()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/embedded/w4;->g:I

    return v0
.end method

.method public a(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/embedded/w4;->g:I

    return-void
.end method

.method public a(Lcom/huawei/hms/network/embedded/c5;)V
    .locals 0

    if-eqz p1, :cond_0

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/w4;->a:Lcom/huawei/hms/network/embedded/c5;

    :cond_0
    return-void
.end method

.method public a(Lcom/huawei/hms/network/embedded/s4;)V
    .locals 0

    if-eqz p1, :cond_0

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/w4;->b:Lcom/huawei/hms/network/embedded/s4;

    :cond_0
    return-void
.end method

.method public a(Lcom/huawei/hms/network/embedded/y4;)V
    .locals 0

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/w4;->e:Lcom/huawei/hms/network/embedded/y4;

    return-void
.end method

.method public a(Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;)V
    .locals 0

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/w4;->d:Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;

    return-void
.end method

.method public b()Lcom/huawei/hms/network/embedded/s4;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/w4;->b:Lcom/huawei/hms/network/embedded/s4;

    return-object v0
.end method

.method public b(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/embedded/w4;->f:I

    return-void
.end method

.method public b(Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;)V
    .locals 0

    if-eqz p1, :cond_0

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/w4;->c:Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;

    :cond_0
    return-void
.end method

.method public c()Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/w4;->d:Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;

    return-object v0
.end method

.method public c(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/embedded/w4;->h:I

    return-void
.end method

.method public d()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/embedded/w4;->h:I

    return v0
.end method

.method public e()Lcom/huawei/hms/network/embedded/y4;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/w4;->e:Lcom/huawei/hms/network/embedded/y4;

    return-object v0
.end method

.method public f()Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/w4;->c:Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;

    return-object v0
.end method

.method public g()Lcom/huawei/hms/network/embedded/c5;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/w4;->a:Lcom/huawei/hms/network/embedded/c5;

    return-object v0
.end method

.method public h()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/embedded/w4;->f:I

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "NetDiagInfoImpl{systemControlInfo="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/huawei/hms/network/embedded/w4;->a:Lcom/huawei/hms/network/embedded/c5;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", allDetectInfo="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/huawei/hms/network/embedded/w4;->b:Lcom/huawei/hms/network/embedded/s4;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", signalInfo="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/huawei/hms/network/embedded/w4;->c:Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", networkInfo="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lcom/huawei/hms/network/embedded/w4;->e:Lcom/huawei/hms/network/embedded/y4;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
