.class public final synthetic Lcom/huawei/hms/network/embedded/v8$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic f$0:Lcom/huawei/hms/network/embedded/v8;


# direct methods
.method public synthetic constructor <init>(Lcom/huawei/hms/network/embedded/v8;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/v8$$ExternalSyntheticLambda0;->f$0:Lcom/huawei/hms/network/embedded/v8;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/v8$$ExternalSyntheticLambda0;->f$0:Lcom/huawei/hms/network/embedded/v8;

    invoke-static {v0}, Lcom/huawei/hms/network/embedded/v8;->$r8$lambda$nXz7tfDLN9q3FLfut9dEHkbA2yY(Lcom/huawei/hms/network/embedded/v8;)V

    return-void
.end method
