.class public interface abstract Lcom/huawei/hms/network/ai/i;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract a()V
.end method

.method public abstract a(Lcom/huawei/hms/network/ai/l;)V
.end method

.method public abstract a(Lcom/huawei/hms/network/inner/api/RequestContext;)V
.end method

.method public abstract b()V
.end method

.method public abstract c()V
.end method
