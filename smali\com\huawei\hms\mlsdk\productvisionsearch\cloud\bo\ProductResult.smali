.class public Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;
.super Ljava/lang/Object;
.source "ProductResult.java"


# instance fields
.field private customContent:Ljava/lang/String;

.field private images:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ImageResult;",
            ">;"
        }
    .end annotation
.end field

.field private productId:Ljava/lang/String;

.field private productUrl:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;->customContent:Ljava/lang/String;

    return-object v0
.end method

.method public b()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ImageResult;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;->images:Ljava/util/List;

    return-object v0
.end method

.method public c()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;->productId:Ljava/lang/String;

    return-object v0
.end method

.method public d()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;->productUrl:Ljava/lang/String;

    return-object v0
.end method
