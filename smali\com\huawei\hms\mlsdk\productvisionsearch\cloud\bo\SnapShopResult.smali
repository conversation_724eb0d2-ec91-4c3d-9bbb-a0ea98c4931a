.class public Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResult;
.super Ljava/lang/Object;
.source "SnapShopResult.java"


# instance fields
.field private boxResult:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;

.field private classType:Ljava/lang/String;

.field private productResults:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResult;->boxResult:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;

    return-object v0
.end method

.method public b()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResult;->classType:Ljava/lang/String;

    return-object v0
.end method

.method public c()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ProductResult;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResult;->productResults:Ljava/util/List;

    return-object v0
.end method
