.class public Lcom/huawei/hms/network/ai/f0$c;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/huawei/hms/network/ai/f0;->a(Ljava/util/List;Ljava/util/List;ZZ)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Z

.field public final synthetic b:Z

.field public final synthetic c:Ljava/util/List;

.field public final synthetic d:Ljava/util/List;

.field public final synthetic e:Lcom/huawei/hms/network/ai/f0;


# direct methods
.method public constructor <init>(Lcom/huawei/hms/network/ai/f0;ZZLjava/util/List;Ljava/util/List;)V
    .locals 0

    iput-object p1, p0, Lcom/huawei/hms/network/ai/f0$c;->e:Lcom/huawei/hms/network/ai/f0;

    iput-boolean p2, p0, Lcom/huawei/hms/network/ai/f0$c;->a:Z

    iput-boolean p3, p0, Lcom/huawei/hms/network/ai/f0$c;->b:Z

    iput-object p4, p0, Lcom/huawei/hms/network/ai/f0$c;->c:Ljava/util/List;

    iput-object p5, p0, Lcom/huawei/hms/network/ai/f0$c;->d:Ljava/util/List;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 8

    iget-boolean v0, p0, Lcom/huawei/hms/network/ai/f0$c;->a:Z

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-nez v0, :cond_1

    iget-boolean v0, p0, Lcom/huawei/hms/network/ai/f0$c;->b:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/huawei/hms/network/ai/f0$c;->e:Lcom/huawei/hms/network/ai/f0;

    invoke-static {v0}, Lcom/huawei/hms/network/ai/f0;->a(Lcom/huawei/hms/network/ai/f0;)Ljava/util/Map;

    move-result-object v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/huawei/hms/network/ai/f0$c;->c:Ljava/util/List;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-gtz v0, :cond_0

    goto :goto_0

    :cond_0
    move v0, v2

    goto :goto_1

    :cond_1
    :goto_0
    move v0, v1

    :goto_1
    if-eqz v0, :cond_2

    return-void

    :cond_2
    move v0, v2

    move v3, v0

    move v4, v3

    :goto_2
    iget-object v5, p0, Lcom/huawei/hms/network/ai/f0$c;->c:Ljava/util/List;

    invoke-interface {v5}, Ljava/util/List;->size()I

    move-result v5

    if-ge v0, v5, :cond_6

    iget-object v3, p0, Lcom/huawei/hms/network/ai/f0$c;->c:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/net/InetAddress;

    invoke-virtual {v3}, Ljava/net/InetAddress;->getHostAddress()Ljava/lang/String;

    move-result-object v3

    iget-object v5, p0, Lcom/huawei/hms/network/ai/f0$c;->d:Ljava/util/List;

    invoke-interface {v5, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/net/InetAddress;

    invoke-virtual {v5}, Ljava/net/InetAddress;->getHostAddress()Ljava/lang/String;

    move-result-object v5

    if-nez v4, :cond_3

    if-eqz v3, :cond_3

    invoke-virtual {v3, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v6

    if-nez v6, :cond_3

    move v4, v1

    :cond_3
    iget-object v6, p0, Lcom/huawei/hms/network/ai/f0$c;->e:Lcom/huawei/hms/network/ai/f0;

    invoke-static {v6}, Lcom/huawei/hms/network/ai/f0;->a(Lcom/huawei/hms/network/ai/f0;)Ljava/util/Map;

    move-result-object v6

    invoke-interface {v6, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [I

    aget v6, v6, v2

    iget-object v7, p0, Lcom/huawei/hms/network/ai/f0$c;->e:Lcom/huawei/hms/network/ai/f0;

    invoke-static {v7}, Lcom/huawei/hms/network/ai/f0;->a(Lcom/huawei/hms/network/ai/f0;)Ljava/util/Map;

    move-result-object v7

    invoke-interface {v7, v5}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, [I

    aget v7, v7, v2

    sub-int/2addr v6, v7

    if-eqz v6, :cond_4

    mul-int/lit16 v0, v0, 0x3e8

    add-int v3, v6, v0

    goto :goto_3

    :cond_4
    if-eqz v3, :cond_5

    invoke-virtual {v3, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-nez v3, :cond_5

    move v3, v6

    goto :goto_3

    :cond_5
    add-int/lit8 v0, v0, 0x1

    move v3, v6

    goto :goto_2

    :cond_6
    :goto_3
    if-eqz v4, :cond_7

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    const-string v1, "model_type"

    const-string v2, "ipsort.model"

    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v1, p0, Lcom/huawei/hms/network/ai/f0$c;->e:Lcom/huawei/hms/network/ai/f0;

    iget-object v2, p0, Lcom/huawei/hms/network/ai/f0$c;->d:Ljava/util/List;

    invoke-virtual {v1, v2}, Lcom/huawei/hms/network/ai/f0;->b(Ljava/util/List;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "sorted_ip"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-static {v3}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v1

    const-string v2, "dif_connect_time"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-static {v0}, Lcom/huawei/hms/network/ai/j;->a(Ljava/util/Map;)V

    :cond_7
    return-void
.end method
