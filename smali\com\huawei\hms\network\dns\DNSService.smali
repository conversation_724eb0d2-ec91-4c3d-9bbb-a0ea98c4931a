.class public Lcom/huawei/hms/network/dns/DNSService;
.super Lcom/huawei/hms/network/inner/api/DnsNetworkService;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/network/dns/DNSService$b;
    }
.end annotation


# static fields
.field public static final TAG:Ljava/lang/String; = "DNSService"


# instance fields
.field public falseStr:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Lcom/huawei/hms/network/inner/api/DnsNetworkService;-><init>()V

    const-string v0, "false"

    iput-object v0, p0, Lcom/huawei/hms/network/dns/DNSService;->falseStr:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public beginEachRequest(Lcom/huawei/hms/network/inner/api/RequestContext;)V
    .locals 1

    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object v0

    invoke-virtual {p1}, Lcom/huawei/hms/network/inner/api/RequestContext;->request()Lcom/huawei/hms/network/httpclient/Request;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/network/httpclient/Request;->getUrl()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lcom/huawei/hms/framework/common/NetworkUtil;->getHost(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/embedded/t;->g(Ljava/lang/String;)V

    return-void
.end method

.method public clear()V
    .locals 3

    :try_start_0
    invoke-static {}, Lcom/huawei/hms/network/embedded/y;->b()Ljava/util/concurrent/ExecutorService;

    move-result-object v0

    new-instance v1, Lcom/huawei/hms/network/dns/DNSService$b;

    const/4 v2, 0x0

    invoke-direct {v1, v2}, Lcom/huawei/hms/network/dns/DNSService$b;-><init>(Lcom/huawei/hms/network/dns/DNSService$a;)V

    invoke-interface {v0, v1}, Ljava/util/concurrent/ExecutorService;->execute(Ljava/lang/Runnable;)V
    :try_end_0
    .catch Ljava/util/concurrent/RejectedExecutionException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    const-string v1, "DNSService"

    const-string v2, "Execute clear error"

    invoke-static {v1, v2, v0}, Lcom/huawei/hms/framework/common/Logger;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    :goto_0
    return-void
.end method

.method public dnsPrefetch(Ljava/lang/String;)V
    .locals 0

    invoke-static {p1}, Lcom/huawei/hms/network/embedded/t;->i(Ljava/lang/String;)V

    return-void
.end method

.method public endEachRequest(Lcom/huawei/hms/network/inner/api/RequestContext;)V
    .locals 2

    invoke-virtual {p1}, Lcom/huawei/hms/network/inner/api/RequestContext;->response()Lcom/huawei/hms/network/httpclient/Response;

    move-result-object v0

    invoke-virtual {p1}, Lcom/huawei/hms/network/inner/api/RequestContext;->throwable()Ljava/lang/Throwable;

    move-result-object v1

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object v1

    invoke-virtual {p1}, Lcom/huawei/hms/network/inner/api/RequestContext;->request()Lcom/huawei/hms/network/httpclient/Request;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/network/httpclient/Request;->getUrl()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lcom/huawei/hms/framework/common/NetworkUtil;->getHost(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/Response;->getCode()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    invoke-virtual {v1, p1, v0}, Lcom/huawei/hms/network/embedded/t;->a(Ljava/lang/String;Ljava/lang/Object;)V

    goto :goto_0

    :cond_0
    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object v0

    invoke-virtual {p1}, Lcom/huawei/hms/network/inner/api/RequestContext;->request()Lcom/huawei/hms/network/httpclient/Request;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/network/httpclient/Request;->getUrl()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lcom/huawei/hms/framework/common/NetworkUtil;->getHost(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1, v1}, Lcom/huawei/hms/network/embedded/t;->a(Ljava/lang/String;Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public getDnsCache()I
    .locals 1

    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/t;->f()I

    move-result v0

    return v0
.end method

.method public getDnsType()Ljava/lang/String;
    .locals 2

    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object v0

    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/network/embedded/t;->g()I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/embedded/t;->a(I)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getServiceName()Ljava/lang/String;
    .locals 1

    const-string v0, "dns"

    return-object v0
.end method

.method public getServiceType()Ljava/lang/String;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getVersion()Ljava/lang/String;
    .locals 1

    const-string v0, "7.0.3.300"

    return-object v0
.end method

.method public lookup(Ljava/lang/String;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Ljava/net/InetAddress;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/net/UnknownHostException;
        }
    .end annotation

    :try_start_0
    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/embedded/t;->c(Ljava/lang/String;)Ljava/util/List;

    move-result-object v0
    :try_end_0
    .catch Ljava/net/UnknownHostException; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception v0

    const-string v1, "DNSService"

    const-string v2, "may be error"

    invoke-static {v1, v2, v0}, Lcom/huawei/hms/framework/common/Logger;->e(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_1

    invoke-interface {v0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-eqz v1, :cond_0

    goto :goto_1

    :cond_0
    return-object v0

    :cond_1
    :goto_1
    invoke-static {p1}, Lcom/huawei/hms/network/embedded/y;->b(Ljava/lang/String;)Ljava/util/List;

    move-result-object p1

    return-object p1

    :catch_0
    move-exception p1

    throw p1
.end method

.method public onCreate(Landroid/content/Context;Landroid/os/Bundle;)V
    .locals 1

    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object p2

    invoke-static {}, Lcom/huawei/hms/framework/common/ContextHolder;->getAppContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {p1}, Lcom/huawei/hms/network/embedded/v;->a(Landroid/content/Context;)Lcom/huawei/hms/network/embedded/v;

    move-result-object p1

    invoke-virtual {p2, v0, p1}, Lcom/huawei/hms/network/embedded/t;->a(Landroid/content/Context;Lcom/huawei/hms/network/embedded/l0;)V

    return-void
.end method

.method public onDestroy(Landroid/content/Context;)V
    .locals 0

    return-void
.end method

.method public serviceOptions(Ljava/util/Map;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    const-string v0, "DNSService"

    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object v1

    const-string v2, "core_enable_httpdns"

    invoke-interface {p1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    invoke-static {v2}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result v2

    invoke-virtual {v1, v2}, Lcom/huawei/hms/network/embedded/t;->a(Z)V

    iget-object v1, p0, Lcom/huawei/hms/network/dns/DNSService;->falseStr:Ljava/lang/String;

    const-string v2, "core_enable_ipv6_preferred"

    invoke-interface {p1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/CharSequence;

    invoke-static {v1, v2}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object v1

    const/4 v2, 0x0

    goto :goto_0

    :cond_0
    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object v1

    const/4 v2, 0x1

    :goto_0
    invoke-virtual {v1, v2}, Lcom/huawei/hms/network/embedded/t;->b(Z)V

    :try_start_0
    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object v1

    const-string v2, "core_enable_site_detect"

    invoke-interface {p1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    invoke-static {v2}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result v2

    invoke-virtual {v1, v2}, Lcom/huawei/hms/network/embedded/t;->c(Z)V
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    const-string v1, "Parse enable_site_detect error"

    invoke-static {v0, v1}, Lcom/huawei/hms/framework/common/Logger;->w(Ljava/lang/String;Ljava/lang/Object;)V

    :goto_1
    :try_start_1
    invoke-static {}, Lcom/huawei/hms/network/embedded/t;->k()Lcom/huawei/hms/network/embedded/t;

    move-result-object v1

    const-string v2, "core_site_detect_threshold"

    invoke-interface {p1, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    invoke-static {p1}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    move-result-wide v2

    invoke-virtual {v1, v2, v3}, Lcom/huawei/hms/network/embedded/t;->a(J)V
    :try_end_1
    .catch Ljava/lang/NumberFormatException; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_2

    :catch_1
    const-string p1, "Parse site_detect_threshold error"

    invoke-static {v0, p1}, Lcom/huawei/hms/framework/common/Logger;->w(Ljava/lang/String;Ljava/lang/Object;)V

    :goto_2
    return-void
.end method
