.class synthetic Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder$1;
.super Ljava/lang/Object;
.source "RemoteImageSegmentationDecoder.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1008
    name = null
.end annotation
