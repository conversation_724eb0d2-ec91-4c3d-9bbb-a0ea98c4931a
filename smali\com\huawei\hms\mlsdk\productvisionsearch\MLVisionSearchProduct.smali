.class public Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;
.super Ljava/lang/Object;
.source "MLVisionSearchProduct.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct$b;
    }
.end annotation


# instance fields
.field private a:Ljava/lang/String;

.field private b:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;",
            ">;"
        }
    .end annotation
.end field

.field private c:Ljava/lang/String;

.field private d:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 1

    const/4 v0, 0x0

    .line 1
    invoke-direct {p0, v0, v0}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;-><init>(Ljava/lang/String;Ljava/util/List;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/util/List;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x0

    .line 2
    invoke-direct {p0, p1, p2, v0, v0}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;-><init>(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;",
            ">;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->a:Ljava/lang/String;

    .line 5
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->b:Ljava/util/List;

    .line 6
    iput-object p3, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->c:Ljava/lang/String;

    .line 7
    iput-object p4, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->d:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public getCustomContent()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->d:Ljava/lang/String;

    return-object v0
.end method

.method public getImageList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->b:Ljava/util/List;

    return-object v0
.end method

.method public getPossibility()F
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->b:Ljava/util/List;

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/dynamic/a;->a(Ljava/util/Collection;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    return v0

    .line 5
    :cond_0
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->b:Ljava/util/List;

    new-instance v1, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct$b;

    const/4 v2, 0x0

    invoke-direct {v1, v2}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct$b;-><init>(Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct$a;)V

    invoke-static {v0, v1}, Ljava/util/Collections;->max(Ljava/util/Collection;Ljava/util/Comparator;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;

    .line 7
    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->getPossibility()F

    move-result v0

    return v0
.end method

.method public getProductId()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->a:Ljava/lang/String;

    return-object v0
.end method

.method public getProductUrl()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->c:Ljava/lang/String;

    return-object v0
.end method

.method public setCustomContent(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->d:Ljava/lang/String;

    return-void
.end method

.method public setImageList(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->b:Ljava/util/List;

    return-void
.end method

.method public setProductId(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->a:Ljava/lang/String;

    return-void
.end method

.method public setProductUrl(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;->c:Ljava/lang/String;

    return-void
.end method
