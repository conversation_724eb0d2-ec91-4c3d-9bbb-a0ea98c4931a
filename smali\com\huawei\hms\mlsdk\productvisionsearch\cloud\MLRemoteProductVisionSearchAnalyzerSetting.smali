.class public Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;
.super Ljava/lang/Object;
.source "MLRemoteProductVisionSearchAnalyzerSetting.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;
    }
.end annotation


# static fields
.field public static final REGION_DR_AFILA:I = 0x3eb
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final REGION_DR_CHINA:I = 0x3ea

.field public static final REGION_DR_EUROPE:I = 0x3ec
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final REGION_DR_GERMAN:I = 0x3ee

.field public static final REGION_DR_RUSSIA:I = 0x3ed

.field public static final REGION_DR_SINGAPORE:I = 0x3ef

.field public static final REGION_DR_UNKNOWN:I = 0x3e9


# instance fields
.field private final a:F

.field private final b:I

.field private final c:Z

.field private final d:Ljava/lang/String;

.field private final e:Z

.field private final f:Ljava/lang/Integer;

.field private final g:I


# direct methods
.method synthetic constructor <init>(FIZLjava/lang/String;ZLjava/lang/Integer;ILcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->a:F

    .line 4
    iput p2, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->b:I

    .line 5
    iput-boolean p3, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->c:Z

    .line 6
    iput-object p4, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->d:Ljava/lang/String;

    .line 7
    iput-boolean p5, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->e:Z

    .line 8
    iput-object p6, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->f:Ljava/lang/Integer;

    .line 9
    iput p7, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->g:I

    return-void
.end method


# virtual methods
.method protected a()Ljava/lang/String;
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->a()Ljava/util/Map;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->g:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->a()Ljava/util/Map;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->g:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 4
    :cond_1
    check-cast p1, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;

    .line 5
    iget v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->b:I

    iget v3, p1, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->b:I

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->a:F

    cmpl-float v1, v1, v1

    if-nez v1, :cond_2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->c:Z

    iget-boolean v3, p1, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->c:Z

    if-ne v1, v3, :cond_2

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->d:Ljava/lang/String;

    iget-object v3, p1, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->d:Ljava/lang/String;

    .line 7
    invoke-static {v1, v3}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->e:Z

    iget-boolean v3, p1, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->e:Z

    if-ne v1, v3, :cond_2

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->f:Ljava/lang/Integer;

    iget-object v3, p1, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->f:Ljava/lang/Integer;

    if-ne v1, v3, :cond_2

    iget v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->g:I

    iget p1, p1, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->g:I

    if-ne v1, p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public getClassType()Ljava/lang/Integer;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->f:Ljava/lang/Integer;

    return-object v0
.end method

.method public getLargestNumOfReturns()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->b:I

    return v0
.end method

.method public getMinAcceptablePossibility()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->a:F

    return v0
.end method

.method public getProductSetId()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->d:Ljava/lang/String;

    return-object v0
.end method

.method public getRegion()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->g:I

    return v0
.end method

.method public hashCode()I
    .locals 3

    const/4 v0, 0x7

    new-array v0, v0, [Ljava/lang/Object;

    .line 1
    iget v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->b:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->a:F

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->c:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->d:Ljava/lang/String;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->e:Z

    .line 2
    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x4

    aput-object v1, v0, v2

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->f:Ljava/lang/Integer;

    const/4 v2, 0x5

    aput-object v1, v0, v2

    iget v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->g:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x6

    aput-object v1, v0, v2

    .line 3
    invoke-static {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->hashCode([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public isEnableBorderExtract()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->e:Z

    return v0
.end method

.method public final isEnableFingerprintVerification()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;->c:Z

    return v0
.end method
