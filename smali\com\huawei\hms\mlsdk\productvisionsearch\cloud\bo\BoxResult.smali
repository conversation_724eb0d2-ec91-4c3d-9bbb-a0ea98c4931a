.class public Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;
.super Ljava/lang/Object;
.source "BoxResult.java"


# instance fields
.field private bottomX:I

.field private bottomY:I

.field private topX:I

.field private topY:I


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;->bottomX:I

    return v0
.end method

.method public b()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;->bottomY:I

    return v0
.end method

.method public c()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;->topX:I

    return v0
.end method

.method public d()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/BoxResult;->topY:I

    return v0
.end method
