.class Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter$1;
.super Ljava/lang/Object;
.source "HmsAdapter.java"

# interfaces
.implements Lcom/huawei/hms/adapter/AvailableAdapter$AvailableCallBack;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;


# direct methods
.method constructor <init>(Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;)V
    .locals 0

    .line 81
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter$1;->this$0:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onComplete(I)V
    .locals 2

    .line 85
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter$1;->this$0:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->access$002(Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;Z)Z

    .line 86
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter$1;->this$0:Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;

    if-nez p1, :cond_0

    const/4 v1, 0x1

    :cond_0
    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;->access$102(Lcom/huawei/hms/mlsdk/internal/client/adapter/HmsAdapter;Z)Z

    return-void
.end method
