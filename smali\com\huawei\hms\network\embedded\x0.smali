.class public Lcom/huawei/hms/network/embedded/x0;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final HTTP_METHOD_GET:Ljava/lang/String; = "GET"

.field public static final HTTP_METHOD_POST:Ljava/lang/String; = "POST"

.field public static final KEY_CONTENT_ENCODING:Ljava/lang/String; = "Content-Encoding"

.field public static final KEY_CONTENT_LENGTH:Ljava/lang/String; = "Content-Length"

.field public static final KEY_CONTENT_TYPE:Ljava/lang/String; = "Content-Type"

.field public static final TYPE_CRONET:Ljava/lang/String; = "type_cronet"

.field public static final TYPE_OKHTTP:Ljava/lang/String; = "type_okhttp"

.field public static final TYPE_URLCONNECTION:Ljava/lang/String; = "type_urlconnection"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
