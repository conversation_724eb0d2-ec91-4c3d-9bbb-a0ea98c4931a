.class public Lcom/huawei/hms/network/ai/a;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final A:F = 0.8f

.field public static final B:I = 0x7d0

.field public static final C:I = 0xfa0

.field public static final D:I = 0xfa0

.field public static final E:I = 0x2711

.field public static final F:J = 0x927c0L

.field public static final G:F = 0.4f

.field public static final H:F = 0.8f

.field public static final I:Ljava/lang/String; = "networkkit"

.field public static final a:J = 0x493e0L

.field public static final b:J = 0x493e0L

.field public static final c:I = 0x32

.field public static final d:I = 0x64

.field public static final e:F = 0.7f

.field public static final f:I = 0xa

.field public static final g:I = 0x5

.field public static final h:F = 0.4f

.field public static final i:F = 0.2f

.field public static final j:F = 0.4f

.field public static final k:F = 50.0f

.field public static final l:F = 30.0f

.field public static final m:F = 10.0f

.field public static final n:J = 0xea60L

.field public static final o:I = 0x1

.field public static final p:J = 0x7530L

.field public static final q:I = 0xa

.field public static final r:F = 0.8f

.field public static final s:F = 0.2f

.field public static final t:I = 0x32

.field public static final u:I = 0x927c0

.field public static final v:I = 0x32

.field public static final w:I = 0x2bf20

.field public static final x:I = 0x14

.field public static final y:F = 0.94f

.field public static final z:F = 0.95f


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
