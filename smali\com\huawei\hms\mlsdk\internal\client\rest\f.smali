.class public Lcom/huawei/hms/mlsdk/internal/client/rest/f;
.super Lcom/huawei/hms/mlsdk/internal/client/rest/a;
.source "SimpleRestClientProvider.java"


# direct methods
.method synthetic constructor <init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/internal/client/rest/f$a;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/internal/client/rest/a;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;)V

    return-void
.end method
