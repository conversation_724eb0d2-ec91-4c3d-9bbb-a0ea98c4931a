.class public Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor$Creator;
.super Ljava/lang/Object;
.source "MLMaxSizeFaceTransactor.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Creator"
.end annotation


# instance fields
.field private MLMaxSizeFaceTransactor:Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor;


# direct methods
.method public constructor <init>(Lcom/huawei/hms/mlsdk/common/MLAnalyzer;Lcom/huawei/hms/mlsdk/common/MLResultTrailer;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLAnalyzer<",
            "Lcom/huawei/hms/mlsdk/face/MLFace;",
            ">;",
            "Lcom/huawei/hms/mlsdk/common/MLResultTrailer<",
            "Lcom/huawei/hms/mlsdk/face/MLFace;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor;

    invoke-direct {v0, p1, p2}, Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor;-><init>(Lcom/huawei/hms/mlsdk/common/MLAnalyzer;Lcom/huawei/hms/mlsdk/common/MLResultTrailer;)V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor$Creator;->MLMaxSizeFaceTransactor:Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor;

    return-void
.end method


# virtual methods
.method public create()Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor$Creator;->MLMaxSizeFaceTransactor:Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor;

    return-object v0
.end method

.method public setMaxFrameLostCount(I)Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor$Creator;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor$Creator;->MLMaxSizeFaceTransactor:Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor;

    invoke-static {v0, p1}, Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor;->access$000(Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor;I)V

    return-object p0
.end method
