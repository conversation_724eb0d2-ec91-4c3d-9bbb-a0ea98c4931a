.class public final Lcom/huawei/hms/network/embedded/wb$a;
.super Lcom/huawei/hms/network/embedded/wb;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/network/embedded/wb;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/huawei/hms/network/embedded/wb;-><init>()V

    return-void
.end method


# virtual methods
.method public a(J)Lcom/huawei/hms/network/embedded/wb;
    .locals 0

    return-object p0
.end method

.method public b(JLjava/util/concurrent/TimeUnit;)Lcom/huawei/hms/network/embedded/wb;
    .locals 0

    return-object p0
.end method

.method public e()V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    return-void
.end method
