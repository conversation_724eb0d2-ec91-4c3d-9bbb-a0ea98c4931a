.class public Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;
.super Ljava/lang/Object;
.source "RemoteImageSegmentationDecoder.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder$Holder;
    }
.end annotation


# static fields
.field public static final TAG:Ljava/lang/String; = "RemoteImageSegmentationDecoder"

.field private static volatile lock:Ljava/lang/Object;


# instance fields
.field private initialed:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ljava/lang/Object;

    invoke-direct {v0}, Ljava/lang/Object;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->lock:Ljava/lang/Object;

    return-void
.end method

.method private constructor <init>()V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 3
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->initialed:Z

    return-void
.end method

.method synthetic constructor <init>(Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder$1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;-><init>()V

    return-void
.end method

.method private static convert(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorFrameParcel;
    .locals 12

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->readBitmap()Landroid/graphics/Bitmap;

    move-result-object v0

    if-nez v0, :cond_1

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    const-string v1, "bitmap and byteBuffer can\'t be empty at the same time"

    invoke-static {v0, v1}, Lcom/huawei/hms/common/Preconditions;->checkState(ZLjava/lang/Object;)V

    .line 2
    new-instance v0, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorFrameParcel;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object v1

    if-nez v1, :cond_2

    const/4 v1, 0x0

    goto :goto_2

    :cond_2
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object v1

    invoke-virtual {v1}, Ljava/nio/ByteBuffer;->array()[B

    move-result-object v1

    :goto_2
    move-object v3, v1

    .line 3
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getWidth()I

    move-result v4

    .line 4
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getHeight()I

    move-result v5

    .line 5
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getQuadrant()I

    move-result v6

    .line 6
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getFormatType()I

    move-result v7

    .line 7
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getWidth()I

    move-result v8

    .line 8
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getHeight()I

    move-result v9

    .line 9
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->readBitmap()Landroid/graphics/Bitmap;

    move-result-object v10

    .line 10
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object p0

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getItemIdentity()I

    move-result v11

    move-object v2, v0

    invoke-direct/range {v2 .. v11}, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorFrameParcel;-><init>([BIIIIIILandroid/graphics/Bitmap;I)V

    return-object v0
.end method

.method public static getInstance()Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;
    .locals 1

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder$Holder;->INSTANCE:Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;

    return-object v0
.end method

.method private isAvailable(Landroid/content/Context;)Z
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 2
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/b;->a()Lcom/huawei/hms/mlsdk/mlvision/b;

    move-result-object v1

    .line 3
    invoke-virtual {v0, p1, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->isAvailable(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)Z

    .line 5
    :cond_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    .line 6
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/b;->a()Lcom/huawei/hms/mlsdk/mlvision/b;

    move-result-object v1

    .line 7
    invoke-virtual {v0, p1, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->isAvailable(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)Z

    move-result p1

    return p1
.end method

.method private notifyDownloadIfNeeded(Landroid/content/Context;)V
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    .line 2
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/b;->a()Lcom/huawei/hms/mlsdk/mlvision/b;

    move-result-object v1

    .line 3
    invoke-virtual {v0, p1, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->notifyDownloadIfNeeded(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)V

    return-void
.end method


# virtual methods
.method public declared-synchronized destroy(Landroid/content/Context;)I
    .locals 4

    monitor-enter p0

    .line 1
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/b;->a()Lcom/huawei/hms/mlsdk/mlvision/b;

    move-result-object p1

    .line 2
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    const/4 v0, -0x1

    if-nez p1, :cond_0

    monitor-exit p0

    return v0

    .line 9
    :cond_0
    :try_start_1
    check-cast p1, Lcom/huawei/hms/ml/common/imgseg/IRemoteImageSegmentationDecoderDelegate;

    invoke-interface {p1}, Lcom/huawei/hms/ml/common/imgseg/IRemoteImageSegmentationDecoderDelegate;->destroy()I

    move-result p1
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return p1

    :catchall_0
    move-exception p1

    .line 14
    :try_start_2
    sget-object v1, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "destroy Throwable e: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :catch_0
    move-exception p1

    .line 15
    sget-object v1, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "destroy Exception e: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :goto_0
    monitor-exit p0

    return v0

    :catchall_1
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized detect(Landroid/content/Context;Landroid/os/Bundle;Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;)Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;
    .locals 2

    monitor-enter p0

    .line 1
    :try_start_0
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->isAvailable(Landroid/content/Context;)Z

    move-result p1

    if-nez p1, :cond_0

    .line 2
    new-instance p1, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;

    invoke-direct {p1}, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;-><init>()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object p1

    .line 3
    :cond_0
    :try_start_1
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/b;->a()Lcom/huawei/hms/mlsdk/mlvision/b;

    move-result-object p1

    .line 4
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object v0

    .line 5
    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->initialed:Z

    if-nez v1, :cond_1

    invoke-virtual {p0, p4, p1, v0}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->initial(Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;Landroid/os/IInterface;)I

    move-result p1

    if-ltz p1, :cond_1

    const/4 p1, 0x1

    .line 6
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->initialed:Z

    .line 9
    :cond_1
    iget-boolean p1, p0, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->initialed:Z

    if-nez p1, :cond_2

    .line 10
    new-instance p1, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;

    invoke-direct {p1}, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;-><init>()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-object p1

    :cond_2
    if-nez v0, :cond_3

    .line 14
    :try_start_2
    new-instance p1, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;

    invoke-direct {p1}, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;-><init>()V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    monitor-exit p0

    return-object p1

    .line 18
    :cond_3
    :try_start_3
    invoke-static {p3}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->convert(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorFrameParcel;

    move-result-object p1

    .line 19
    check-cast v0, Lcom/huawei/hms/ml/common/imgseg/IRemoteImageSegmentationDecoderDelegate;

    invoke-interface {v0, p2, p1, p4}, Lcom/huawei/hms/ml/common/imgseg/IRemoteImageSegmentationDecoderDelegate;->detect(Landroid/os/Bundle;Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorFrameParcel;Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;)Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;

    move-result-object p1
    :try_end_3
    .catch Ljava/lang/Exception; {:try_start_3 .. :try_end_3} :catch_0
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    monitor-exit p0

    return-object p1

    :catch_0
    move-exception p1

    .line 21
    :try_start_4
    sget-object p2, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->TAG:Ljava/lang/String;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string p4, "detect Exception  e: "

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 24
    new-instance p1, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;

    invoke-direct {p1}, Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationDetectorParcel;-><init>()V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    monitor-exit p0

    return-object p1

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized initial(Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;Landroid/os/IInterface;)I
    .locals 2

    monitor-enter p0

    const/4 v0, -0x1

    if-nez p3, :cond_0

    monitor-exit p0

    return v0

    .line 1
    :cond_0
    :try_start_0
    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->initialed:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    if-eqz v1, :cond_1

    const/4 p1, 0x0

    monitor-exit p0

    return p1

    .line 6
    :cond_1
    :try_start_1
    invoke-interface {p2}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicContext()Landroid/content/Context;

    move-result-object p2

    .line 7
    check-cast p3, Lcom/huawei/hms/ml/common/imgseg/IRemoteImageSegmentationDecoderDelegate;

    .line 8
    invoke-static {p2}, Lcom/huawei/hms/feature/dynamic/ObjectWrapper;->wrap(Ljava/lang/Object;)Lcom/huawei/hms/feature/dynamic/IObjectWrapper;

    move-result-object p2

    .line 9
    invoke-interface {p3, p2, p1}, Lcom/huawei/hms/ml/common/imgseg/IRemoteImageSegmentationDecoderDelegate;->initial(Lcom/huawei/hms/feature/dynamic/IObjectWrapper;Lcom/huawei/hms/ml/common/imgseg/ImageSegmentationOptionsParcel;)I

    move-result p1

    if-nez p1, :cond_2

    const/4 p2, 0x1

    .line 12
    iput-boolean p2, p0, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->initialed:Z
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :cond_2
    monitor-exit p0

    return p1

    :catchall_0
    move-exception p1

    .line 18
    :try_start_2
    sget-object p2, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->TAG:Ljava/lang/String;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "initial Throwable e: "

    invoke-virtual {p3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :catch_0
    move-exception p1

    .line 19
    sget-object p2, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->TAG:Ljava/lang/String;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "initial Exception e: "

    invoke-virtual {p3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :goto_0
    monitor-exit p0

    return v0

    :catchall_1
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized prepare(Landroid/content/Context;)V
    .locals 1

    monitor-enter p0

    .line 1
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/b;->a()Lcom/huawei/hms/mlsdk/mlvision/b;

    move-result-object v0

    .line 2
    invoke-interface {v0, p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->initial(Landroid/content/Context;)V

    .line 3
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->notifyDownloadIfNeeded(Landroid/content/Context;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized release(Landroid/content/Context;)V
    .locals 1

    monitor-enter p0

    .line 1
    :try_start_0
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->initialed:Z

    if-eqz v0, :cond_0

    .line 2
    invoke-virtual {p0, p1}, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->destroy(Landroid/content/Context;)I

    const/4 v0, 0x0

    .line 3
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/imgseg/client/RemoteImageSegmentationDecoder;->initialed:Z

    .line 6
    :cond_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->release(Landroid/content/Context;)V

    .line 7
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/b;->a()Lcom/huawei/hms/mlsdk/mlvision/b;

    move-result-object p1

    .line 8
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->release()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method
