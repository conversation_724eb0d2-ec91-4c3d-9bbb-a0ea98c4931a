.class public Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;
.super Lcom/huawei/hms/mlsdk/common/MLAnalyzer;
.source "MLObjectAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/huawei/hms/mlsdk/common/MLAnalyzer<",
        "Lcom/huawei/hms/mlsdk/objects/MLObject;",
        ">;"
    }
.end annotation


# static fields
.field private static final TAG:Ljava/lang/String; = "MLObjectAnalyzer"

.field private static appOptionDetectMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lcom/huawei/hms/mlsdk/common/AppSettingHolder<",
            "Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;",
            ">;",
            "Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private app:Lcom/huawei/hms/mlsdk/common/MLApplication;

.field private options:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->appOptionDetectMap:Ljava/util/Map;

    return-void
.end method

.method private constructor <init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/common/MLAnalyzer;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    .line 3
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->options:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    return-void
.end method

.method static synthetic access$000(Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    return-object p0
.end method

.method static synthetic access$100(Ljava/util/List;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->convert(Ljava/util/List;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method private static final convert(Ljava/util/List;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/ml/common/object/ObjectDetectorParcel;",
            ">;",
            "Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;",
            ")",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/objects/MLObject;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    if-nez p0, :cond_0

    return-object v0

    .line 6
    :cond_0
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_3

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/ml/common/object/ObjectDetectorParcel;

    .line 7
    iget-object v2, v1, Lcom/huawei/hms/ml/common/object/ObjectDetectorParcel;->rect:Landroid/graphics/Rect;

    .line 8
    iget v3, p1, Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;->detectorMode:I

    const/4 v4, 0x1

    const/4 v5, 0x0

    if-ne v3, v4, :cond_1

    iget-object v3, v1, Lcom/huawei/hms/ml/common/object/ObjectDetectorParcel;->trackingId:Ljava/lang/Integer;

    goto :goto_1

    :cond_1
    move-object v3, v5

    .line 9
    :goto_1
    iget v4, v1, Lcom/huawei/hms/ml/common/object/ObjectDetectorParcel;->category:I

    if-nez v4, :cond_2

    goto :goto_2

    :cond_2
    iget-object v5, v1, Lcom/huawei/hms/ml/common/object/ObjectDetectorParcel;->confidence:Ljava/lang/Float;

    .line 10
    :goto_2
    iget v1, v1, Lcom/huawei/hms/ml/common/object/ObjectDetectorParcel;->category:I

    invoke-static {v1}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->convertCategoryTo(I)I

    move-result v1

    .line 12
    new-instance v4, Lcom/huawei/hms/mlsdk/objects/MLObject;

    invoke-direct {v4, v2, v3, v5, v1}, Lcom/huawei/hms/mlsdk/objects/MLObject;-><init>(Landroid/graphics/Rect;Ljava/lang/Integer;Ljava/lang/Float;I)V

    .line 13
    invoke-interface {v0, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_3
    return-object v0
.end method

.method private static final convertCategoryTo(I)I
    .locals 1

    const/4 v0, 0x0

    packed-switch p0, :pswitch_data_0

    goto :goto_0

    :pswitch_0
    const/4 v0, 0x6

    goto :goto_0

    :pswitch_1
    const/4 v0, 0x4

    goto :goto_0

    :pswitch_2
    const/4 v0, 0x5

    goto :goto_0

    :pswitch_3
    const/4 v0, 0x2

    goto :goto_0

    :pswitch_4
    const/4 v0, 0x1

    goto :goto_0

    :pswitch_5
    const/4 v0, 0x3

    :goto_0
    :pswitch_6
    return v0

    nop

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static declared-synchronized create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;)Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;
    .locals 6

    const-class v0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;

    monitor-enter v0

    .line 1
    :try_start_0
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getUniqueKey()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, p1}, Lcom/huawei/hms/mlsdk/common/AppSettingHolder;->create(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/common/AppSettingHolder;

    move-result-object v1

    .line 3
    sget-object v2, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->appOptionDetectMap:Ljava/util/Map;

    invoke-interface {v2, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;

    if-nez v2, :cond_0

    .line 4
    new-instance v2, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;

    invoke-direct {v2, p0, p1}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;)V

    .line 5
    sget-object v3, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->appOptionDetectMap:Ljava/util/Map;

    invoke-interface {v3, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    :cond_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->getInstance()Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;

    move-result-object v1

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v3

    invoke-virtual {v1, v3}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->prepare(Landroid/content/Context;)V

    .line 9
    new-instance v1, Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->getAnalyzerType()I

    move-result v3

    if-nez v3, :cond_1

    const/4 v3, 0x2

    goto :goto_0

    :cond_1
    const/4 v3, 0x1

    .line 10
    :goto_0
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isMultipleResultsAllowed()Z

    move-result v4

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isClassificationAllowed()Z

    move-result p1

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->toBundle()Landroid/os/Bundle;

    move-result-object v5

    invoke-direct {v1, v3, v4, p1, v5}, Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;-><init>(IZZLandroid/os/Bundle;)V

    .line 11
    invoke-static {}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->getInstance()Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;

    move-result-object p1

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object p0

    invoke-virtual {p1, p0, v1}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->initial(Landroid/content/Context;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)I
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit v0

    return-object v2

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method


# virtual methods
.method public analyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Landroid/util/SparseArray;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Landroid/util/SparseArray<",
            "Lcom/huawei/hms/mlsdk/objects/MLObject;",
            ">;"
        }
    .end annotation

    if-eqz p1, :cond_2

    .line 1
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->initialize()V

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 2
    invoke-virtual {p1, v0, v1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getFrame(ZZ)Lcom/huawei/hms/mlsdk/common/MLFrame;

    move-result-object p1

    .line 3
    new-instance v2, Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;

    iget-object v3, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->options:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->getAnalyzerType()I

    move-result v3

    if-nez v3, :cond_0

    const/4 v1, 0x2

    :cond_0
    iget-object v3, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->options:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    .line 4
    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isMultipleResultsAllowed()Z

    move-result v3

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->options:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isClassificationAllowed()Z

    move-result v4

    iget-object v5, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/common/MLApplication;->toBundle()Landroid/os/Bundle;

    move-result-object v5

    invoke-direct {v2, v1, v3, v4, v5}, Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;-><init>(IZZLandroid/os/Bundle;)V

    .line 5
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->toBundle()Landroid/os/Bundle;

    move-result-object v1

    .line 6
    invoke-static {}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->getInstance()Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;

    move-result-object v3

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v4

    invoke-virtual {v3, v4, v1, p1, v2}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->detect(Landroid/content/Context;Landroid/os/Bundle;Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)Ljava/util/List;

    move-result-object p1

    invoke-static {p1, v2}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->convert(Ljava/util/List;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)Ljava/util/List;

    move-result-object p1

    .line 7
    new-instance v1, Landroid/util/SparseArray;

    invoke-direct {v1}, Landroid/util/SparseArray;-><init>()V

    .line 9
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/objects/MLObject;

    add-int/lit8 v3, v0, 0x1

    .line 10
    invoke-virtual {v1, v0, v2}, Landroid/util/SparseArray;->put(ILjava/lang/Object;)V

    move v0, v3

    goto :goto_0

    :cond_1
    return-object v1

    .line 11
    :cond_2
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "No frame found."

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public asyncAnalyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Lcom/huawei/hmf/tasks/Task<",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/objects/MLObject;",
            ">;>;"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->initialize()V

    const/4 v0, 0x0

    const/4 v1, 0x1

    .line 3
    invoke-virtual {p1, v0, v1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getFrame(ZZ)Lcom/huawei/hms/mlsdk/common/MLFrame;

    move-result-object p1

    .line 5
    new-instance v0, Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->options:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->getAnalyzerType()I

    move-result v2

    if-nez v2, :cond_0

    const/4 v1, 0x2

    :cond_0
    iget-object v2, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->options:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    .line 6
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isMultipleResultsAllowed()Z

    move-result v2

    iget-object v3, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->options:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzerSetting;->isClassificationAllowed()Z

    move-result v3

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLApplication;->toBundle()Landroid/os/Bundle;

    move-result-object v4

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;-><init>(IZZLandroid/os/Bundle;)V

    .line 7
    new-instance v1, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer$1;

    invoke-direct {v1, p0, p1, v0}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer$1;-><init>(Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)V

    invoke-static {v1}, Lcom/huawei/hmf/tasks/Tasks;->call(Ljava/util/concurrent/Callable;)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    return-object p1
.end method

.method public stop()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->getInstance()Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->release(Landroid/content/Context;)V

    return-void
.end method
