.class public Lcom/huawei/hms/network/embedded/x3;
.super Ljava/io/OutputStream;
.source ""


# instance fields
.field public a:Lcom/huawei/hms/network/embedded/ya;


# direct methods
.method public constructor <init>(Lcom/huawei/hms/network/embedded/ya;)V
    .locals 0

    invoke-direct {p0}, Ljava/io/OutputStream;-><init>()V

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/x3;->a:Lcom/huawei/hms/network/embedded/ya;

    return-void
.end method


# virtual methods
.method public close()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/x3;->a:Lcom/huawei/hms/network/embedded/ya;

    invoke-interface {v0}, Lcom/huawei/hms/network/embedded/ub;->close()V

    return-void
.end method

.method public flush()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/x3;->a:Lcom/huawei/hms/network/embedded/ya;

    invoke-interface {v0}, Lcom/huawei/hms/network/embedded/ya;->flush()V

    return-void
.end method

.method public write(I)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    invoke-static {p1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lcom/huawei/hms/framework/common/StringUtils;->getBytes(Ljava/lang/String;)[B

    move-result-object p1

    invoke-virtual {p0, p1}, Lcom/huawei/hms/network/embedded/x3;->write([B)V

    return-void
.end method

.method public write([B)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    array-length v0, p1

    const/4 v1, 0x0

    invoke-virtual {p0, p1, v1, v0}, Lcom/huawei/hms/network/embedded/x3;->write([BII)V

    return-void
.end method

.method public write([BII)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/x3;->a:Lcom/huawei/hms/network/embedded/ya;

    invoke-interface {v0, p1, p2, p3}, Lcom/huawei/hms/network/embedded/ya;->write([BII)Lcom/huawei/hms/network/embedded/ya;

    return-void
.end method
