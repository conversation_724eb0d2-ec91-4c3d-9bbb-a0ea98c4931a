.class public Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;
.super Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;
.source "AvailableAdapterManager.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager$Holder;
    }
.end annotation


# static fields
.field private static final MIN_HMS_APK_VERSION:I = 0x26262fc


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 14
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AbstractAdapter;-><init>()V

    return-void
.end method

.method synthetic constructor <init>(Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager$1;)V
    .locals 0

    .line 7
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;-><init>()V

    return-void
.end method

.method public static getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;
    .locals 1

    .line 18
    sget-object v0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager$Holder;->INSTANCE:Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    return-object v0
.end method


# virtual methods
.method public getMinHmsApkVersion()I
    .locals 1

    const v0, 0x26262fc

    return v0
.end method
