.class public abstract Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;
.super Ljava/lang/Object;
.source "AbstractInitializer.java"

# interfaces
.implements Lcom/huawei/hms/mlsdk/dynamic/IInitializer;


# static fields
.field public static final POLICY_LOCAL:Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;

.field public static final POLICY_PREFER_HIGHEST_OR_LOCAL:Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;

.field public static final POLICY_PREFER_HIGHEST_OR_REMOTE:Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;

.field public static final POLICY_REMOTE:Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;

.field private static final TAG:Ljava/lang/String; = "AbstractInitializer"


# instance fields
.field private mApplicationContext:Landroid/content/Context;

.field private volatile mDynamicContext:Landroid/content/Context;

.field private volatile mDynamicDelegate:Landroid/os/IInterface;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 26
    new-instance v0, Lcom/huawei/hms/mlsdk/dynamic/policy/LocalPolicy;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/dynamic/policy/LocalPolicy;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->POLICY_LOCAL:Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;

    .line 28
    new-instance v0, Lcom/huawei/hms/mlsdk/dynamic/policy/RemotePolicy;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/dynamic/policy/RemotePolicy;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->POLICY_REMOTE:Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;

    .line 30
    new-instance v0, Lcom/huawei/hms/mlsdk/dynamic/policy/PreferHighestOrLocal;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/dynamic/policy/PreferHighestOrLocal;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->POLICY_PREFER_HIGHEST_OR_LOCAL:Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;

    .line 32
    new-instance v0, Lcom/huawei/hms/mlsdk/dynamic/policy/PreferHighestOrRemote;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/dynamic/policy/PreferHighestOrRemote;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->POLICY_PREFER_HIGHEST_OR_REMOTE:Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 22
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private isCreatorClassRemote(Landroid/content/Context;)Z
    .locals 2

    .line 150
    :try_start_0
    invoke-virtual {p1}, Landroid/content/Context;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object p1

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getCreatorClass()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object p1

    if-eqz p1, :cond_0

    .line 152
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object p1

    const-string v0, "com.huawei.hms"

    invoke-virtual {p1, v0}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result p1
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    return p1

    :catch_0
    move-exception p1

    .line 155
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "isCreatorClassRemote ClassNotFoundException e: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "AbstractInitializer"

    invoke-static {v0, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    :cond_0
    const/4 p1, 0x0

    return p1
.end method

.method private switchDynamicContextIfNeed(Landroid/content/Context;Z)Landroid/content/Context;
    .locals 10

    const/4 v0, 0x2

    const/4 v1, 0x0

    const/4 v2, 0x1

    const-string v3, "AbstractInitializer"

    if-eqz p1, :cond_2

    .line 106
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getApplicationContext()Landroid/content/Context;

    move-result-object v4

    if-ne p1, v4, :cond_0

    goto :goto_0

    .line 111
    :cond_0
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->isCreatorClassRemote(Landroid/content/Context;)Z

    move-result v4

    .line 112
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->isLocalExisted()Z

    move-result v5

    .line 114
    sget-object v6, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const/4 v7, 0x4

    new-array v7, v7, [Ljava/lang/Object;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getModuleName()Ljava/lang/String;

    move-result-object v8

    aput-object v8, v7, v1

    invoke-static {p2}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v8

    aput-object v8, v7, v2

    invoke-static {v4}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v8

    aput-object v8, v7, v0

    const/4 v8, 0x3

    invoke-static {v5}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v9

    aput-object v9, v7, v8

    const-string v8, "%s -> isForce = %b, isCreatorClassRemote = %b, isLocalExisted = %b"

    invoke-static {v6, v8, v7}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v6

    invoke-static {v3, v6}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    if-nez p2, :cond_1

    if-nez v4, :cond_3

    :cond_1
    if-eqz v5, :cond_3

    .line 117
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    .line 118
    sget-object p2, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    new-array v4, v2, [Ljava/lang/Object;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getModuleName()Ljava/lang/String;

    move-result-object v5

    aput-object v5, v4, v1

    const-string v5, "%s -> Force to switch the context of the dynamic module to the context of full sdk"

    invoke-static {p2, v5, v4}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p2

    invoke-static {v3, p2}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_1

    .line 107
    :cond_2
    :goto_0
    sget-object p2, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    new-array v4, v2, [Ljava/lang/Object;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getModuleName()Ljava/lang/String;

    move-result-object v5

    aput-object v5, v4, v1

    const-string v5, "%s -> No need to switch because the context of the dynamic module is null or the context of full sdk"

    invoke-static {p2, v5, v4}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p2

    invoke-static {v3, p2}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    .line 123
    :cond_3
    :goto_1
    sget-object p2, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    new-array v0, v0, [Ljava/lang/Object;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getModuleName()Ljava/lang/String;

    move-result-object v4

    aput-object v4, v0, v1

    if-nez p1, :cond_4

    const-string v1, "NULL"

    goto :goto_2

    :cond_4
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getApplicationContext()Landroid/content/Context;

    move-result-object v1

    if-ne p1, v1, :cond_5

    const-string v1, "LOCAL"

    goto :goto_2

    :cond_5
    const-string v1, "REMOTE"

    :goto_2
    aput-object v1, v0, v2

    const-string v1, "%s -> The context of the dynamic module belongs to %s"

    invoke-static {p2, v1, v0}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p2

    invoke-static {v3, p2}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    .line 125
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicContext:Landroid/content/Context;

    return-object p1
.end method


# virtual methods
.method protected abstract generateDynamicDelegateBridge(Landroid/os/IBinder;)Landroid/os/IInterface;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation
.end method

.method protected getApplicationContext()Landroid/content/Context;
    .locals 2

    .line 43
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mApplicationContext:Landroid/content/Context;

    if-eqz v0, :cond_0

    return-object v0

    .line 44
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "initial must be called first"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method protected getContextPolicy()Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;
    .locals 1

    .line 262
    sget-object v0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->POLICY_PREFER_HIGHEST_OR_REMOTE:Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;

    return-object v0
.end method

.method protected abstract getCreatorClass()Ljava/lang/String;
.end method

.method public getDynamicContext()Landroid/content/Context;
    .locals 3

    .line 92
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicContext:Landroid/content/Context;

    if-eqz v0, :cond_0

    .line 93
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicContext:Landroid/content/Context;

    return-object v0

    .line 95
    :cond_0
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getContextPolicy()Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;

    move-result-object v0

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getApplicationContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getModuleName()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v0, v1, v2}, Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;->getDynamicContext(Landroid/content/Context;Ljava/lang/String;)Landroid/content/Context;

    move-result-object v0

    const/4 v1, 0x0

    .line 97
    invoke-direct {p0, v0, v1}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->switchDynamicContextIfNeed(Landroid/content/Context;Z)Landroid/content/Context;

    .line 99
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicContext:Landroid/content/Context;

    return-object v0
.end method

.method public getDynamicDelegate()Landroid/os/IInterface;
    .locals 4

    .line 62
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicDelegate:Landroid/os/IInterface;

    if-eqz v0, :cond_0

    .line 63
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicDelegate:Landroid/os/IInterface;

    return-object v0

    .line 66
    :cond_0
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getDynamicContext()Landroid/content/Context;

    move-result-object v0

    if-nez v0, :cond_1

    const/4 v0, 0x0

    return-object v0

    .line 72
    :cond_1
    :try_start_0
    invoke-virtual {v0}, Landroid/content/Context;->getClassLoader()Ljava/lang/ClassLoader;

    move-result-object v1

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getCreatorClass()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/ClassLoader;->loadClass(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v1

    .line 73
    invoke-virtual {v1}, Ljava/lang/Class;->newInstance()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/os/IBinder;

    .line 74
    invoke-virtual {p0, v1}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->generateDynamicDelegateBridge(Landroid/os/IBinder;)Landroid/os/IInterface;

    move-result-object v1

    iput-object v1, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicDelegate:Landroid/os/IInterface;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v1

    .line 76
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getDynamicDelegate Exception e: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "AbstractInitializer"

    invoke-static {v2, v1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v1, 0x1

    .line 79
    invoke-direct {p0, v0, v1}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->switchDynamicContextIfNeed(Landroid/content/Context;Z)Landroid/content/Context;

    .line 82
    :goto_0
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicDelegate:Landroid/os/IInterface;

    return-object v0
.end method

.method public declared-synchronized getLoadExceptionBundle()Landroid/os/Bundle;
    .locals 4

    monitor-enter p0

    const/4 v0, 0x0

    .line 191
    :try_start_0
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicContext:Landroid/content/Context;

    if-nez v1, :cond_2

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getContextPolicy()Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;

    move-result-object v1

    sget-object v2, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->POLICY_LOCAL:Lcom/huawei/hms/mlsdk/dynamic/policy/IContextPolicy;

    if-ne v1, v2, :cond_0

    goto :goto_1

    .line 195
    :cond_0
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getDynamicContext()Landroid/content/Context;

    move-result-object v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v1, :cond_1

    .line 197
    monitor-exit p0

    return-object v0

    .line 201
    :cond_1
    :try_start_1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getModuleName()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    invoke-static {v1, v2}, Lcom/huawei/hms/feature/dynamic/DynamicModule;->enable3rdPhone(Ljava/lang/String;Z)V

    .line 202
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getModuleName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, v2}, Lcom/huawei/hms/feature/dynamic/DynamicModule;->enableLowEMUI(Ljava/lang/String;Z)V

    .line 203
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getApplicationContext()Landroid/content/Context;

    move-result-object v1

    sget-object v2, Lcom/huawei/hms/feature/dynamic/DynamicModule;->PREFER_REMOTE:Lcom/huawei/hms/feature/dynamic/DynamicModule$VersionPolicy;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getModuleName()Ljava/lang/String;

    move-result-object v3

    invoke-static {v1, v2, v3}, Lcom/huawei/hms/feature/dynamic/DynamicModule;->load(Landroid/content/Context;Lcom/huawei/hms/feature/dynamic/DynamicModule$VersionPolicy;Ljava/lang/String;)Lcom/huawei/hms/feature/dynamic/DynamicModule;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/feature/dynamic/DynamicModule;->getModuleContext()Landroid/content/Context;
    :try_end_1
    .catch Lcom/huawei/hms/feature/dynamic/DynamicModule$LoadingException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_0

    :catch_0
    move-exception v0

    :try_start_2
    const-string v1, "AbstractInitializer"

    .line 205
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getLoadExceptionBundle DynamicModule.LoadingException e: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    .line 206
    invoke-virtual {v0}, Lcom/huawei/hms/feature/dynamic/DynamicModule$LoadingException;->getBundle()Landroid/os/Bundle;

    move-result-object v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 209
    :goto_0
    monitor-exit p0

    return-object v0

    .line 192
    :cond_2
    :goto_1
    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method public getLocalVersion()I
    .locals 2

    .line 163
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getModuleName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/huawei/hms/feature/dynamic/DynamicModule;->getLocalVersion(Landroid/content/Context;Ljava/lang/String;)I

    move-result v0

    return v0
.end method

.method protected abstract getMinApkVersion()I
.end method

.method protected abstract getModuleName()Ljava/lang/String;
.end method

.method public getRemoteVersion()I
    .locals 3

    .line 169
    :try_start_0
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getModuleName()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/huawei/hms/feature/dynamic/DynamicModule;->getRemoteVersion(Landroid/content/Context;Ljava/lang/String;)I

    move-result v0
    :try_end_0
    .catch Lcom/huawei/hms/feature/dynamic/DynamicModule$LoadingException; {:try_start_0 .. :try_end_0} :catch_0

    return v0

    :catch_0
    move-exception v0

    .line 171
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "getRemoteVersion DynamicModule.LoadingException e: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "AbstractInitializer"

    invoke-static {v1, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v0, -0x1

    return v0
.end method

.method public initial(Landroid/content/Context;)V
    .locals 1

    if-eqz p1, :cond_1

    .line 56
    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    move-object p1, v0

    .line 57
    :goto_0
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mApplicationContext:Landroid/content/Context;

    return-void

    .line 53
    :cond_1
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "applicationContext can\'t be null"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public isLocalExisted()Z
    .locals 1

    .line 179
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getLocalVersion()I

    move-result v0

    if-lez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public isOperational()Z
    .locals 1

    .line 214
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public isRemoteExisted()Z
    .locals 1

    .line 184
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getRemoteVersion()I

    move-result v0

    if-lez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public release()V
    .locals 0

    .line 219
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->releaseDynamicContext()V

    .line 220
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->releaseDynamicDelegate()V

    return-void
.end method

.method protected releaseDynamicContext()V
    .locals 1

    const/4 v0, 0x0

    .line 224
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicContext:Landroid/content/Context;

    return-void
.end method

.method protected releaseDynamicDelegate()V
    .locals 1

    const/4 v0, 0x0

    .line 228
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicDelegate:Landroid/os/IInterface;

    return-void
.end method

.method public switchDynamicContext()Z
    .locals 7

    .line 132
    sget-object v0, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const/4 v1, 0x2

    new-array v2, v1, [Ljava/lang/Object;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getModuleName()Ljava/lang/String;

    move-result-object v3

    const/4 v4, 0x0

    aput-object v3, v2, v4

    iget-object v3, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicContext:Landroid/content/Context;

    const-string v5, "LOCAL"

    if-nez v3, :cond_0

    const-string v3, "NULL"

    goto :goto_0

    :cond_0
    iget-object v3, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicContext:Landroid/content/Context;

    .line 133
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getApplicationContext()Landroid/content/Context;

    move-result-object v6

    if-ne v3, v6, :cond_1

    move-object v3, v5

    goto :goto_0

    :cond_1
    const-string v3, "REMOTE"

    :goto_0
    const/4 v6, 0x1

    aput-object v3, v2, v6

    const-string v3, "%s -> Something wrong, need to switch context, current is %s"

    .line 132
    invoke-static {v0, v3, v2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    const-string v2, "AbstractInitializer"

    invoke-static {v2, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    .line 135
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->isLocalExisted()Z

    move-result v0

    if-eqz v0, :cond_3

    iget-object v0, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicContext:Landroid/content/Context;

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getApplicationContext()Landroid/content/Context;

    move-result-object v3

    if-ne v0, v3, :cond_2

    goto :goto_1

    .line 139
    :cond_2
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicContext:Landroid/content/Context;

    const/4 v0, 0x0

    .line 140
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->mDynamicDelegate:Landroid/os/IInterface;

    .line 142
    sget-object v0, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    new-array v1, v1, [Ljava/lang/Object;

    .line 143
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getModuleName()Ljava/lang/String;

    move-result-object v3

    aput-object v3, v1, v4

    aput-object v5, v1, v6

    const-string v3, "%s -> Switch the context of the dynamic module to %s"

    .line 142
    invoke-static {v0, v3, v1}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    invoke-static {v2, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    return v6

    :cond_3
    :goto_1
    return v4
.end method
