.class public Lcom/huawei/hms/network/ai/c$b;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/network/ai/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "b"
.end annotation


# static fields
.field public static final a:Lcom/huawei/hms/network/ai/c;


# direct methods
.method public static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/huawei/hms/network/ai/c;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/huawei/hms/network/ai/c;-><init>(Lcom/huawei/hms/network/ai/c$a;)V

    sput-object v0, Lcom/huawei/hms/network/ai/c$b;->a:Lcom/huawei/hms/network/ai/c;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
