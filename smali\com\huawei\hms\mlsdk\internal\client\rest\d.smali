.class public Lcom/huawei/hms/mlsdk/internal/client/rest/d;
.super Ljava/lang/Object;
.source "RestClientManager.java"


# static fields
.field private static a:Lcom/huawei/hms/mlsdk/internal/client/rest/d;

.field private static b:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Lcom/huawei/hms/network/restclient/RestClient;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->b:Ljava/util/Map;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static a()Lcom/huawei/hms/mlsdk/internal/client/rest/d;
    .locals 2

    .line 1
    const-class v0, Lcom/huawei/hms/mlsdk/internal/client/rest/d;

    monitor-enter v0

    .line 2
    :try_start_0
    sget-object v1, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->a:Lcom/huawei/hms/mlsdk/internal/client/rest/d;

    if-nez v1, :cond_0

    .line 3
    new-instance v1, Lcom/huawei/hms/mlsdk/internal/client/rest/d;

    invoke-direct {v1}, Lcom/huawei/hms/mlsdk/internal/client/rest/d;-><init>()V

    sput-object v1, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->a:Lcom/huawei/hms/mlsdk/internal/client/rest/d;

    .line 5
    :cond_0
    sget-object v1, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->a:Lcom/huawei/hms/mlsdk/internal/client/rest/d;

    monitor-exit v0

    return-object v1

    :catchall_0
    move-exception v1

    .line 6
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method private a(Ljava/lang/String;Lcom/huawei/hms/network/restclient/RestClient;)V
    .locals 2

    .line 20
    sget-object v0, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->b:Ljava/util/Map;

    monitor-enter v0

    .line 21
    :try_start_0
    sget-object v1, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->b:Ljava/util/Map;

    invoke-interface {v1, p1}, Ljava/util/Map;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    .line 22
    sget-object v1, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->b:Ljava/util/Map;

    invoke-interface {v1, p1, p2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 23
    monitor-exit v0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method


# virtual methods
.method public declared-synchronized a(Ljava/util/List;Ljava/lang/Class;Ljava/lang/String;Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/network/httpclient/Response;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/String;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ")",
            "Lcom/huawei/hms/network/httpclient/Response<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/huawei/hms/mlsdk/common/MLException;
        }
    .end annotation

    monitor-enter p0

    const/4 v0, 0x0

    .line 24
    :try_start_0
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :cond_0
    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 26
    :try_start_1
    const-class v2, Lcom/huawei/hms/mlsdk/cloud/RemoteRequestService;

    if-ne p2, v2, :cond_1

    .line 27
    invoke-virtual {p0, v1}, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->a(Ljava/lang/String;)Lcom/huawei/hms/network/restclient/RestClient;

    move-result-object v1

    invoke-virtual {v1, p2}, Lcom/huawei/hms/network/restclient/RestClient;->create(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/mlsdk/cloud/RemoteRequestService;

    .line 28
    invoke-interface {v1, p3, p4, p5}, Lcom/huawei/hms/mlsdk/cloud/RemoteRequestService;->detect(Ljava/lang/String;Ljava/util/Map;Ljava/lang/String;)Lcom/huawei/hms/network/httpclient/Submit;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/network/httpclient/Submit;->execute()Lcom/huawei/hms/network/httpclient/Response;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 29
    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/Response;->getCode()I

    move-result v1
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    const/16 v2, 0xc8

    if-ne v1, v2, :cond_0

    monitor-exit p0

    return-object v0

    .line 33
    :cond_1
    :try_start_2
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "unknown service type = "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {p6, v1}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    goto :goto_0

    :catch_0
    move-exception v1

    .line 36
    :try_start_3
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "Error===> "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/io/IOException;->getMessage()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {p6, v1}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_2
    if-eqz v0, :cond_6

    .line 39
    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/Response;->getCode()I

    move-result p1

    const/16 p2, 0x191

    if-ne p1, p2, :cond_6

    .line 40
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string p2, "failed message = "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/Response;->getMessage()Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p2, ", code = "

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/Response;->getCode()I

    move-result p2

    invoke-virtual {p1, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p6, p1}, Lcom/huawei/hms/mlkit/common/internal/client/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 41
    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/Response;->getErrorBody()Lcom/huawei/hms/network/httpclient/ResponseBody;

    move-result-object p1
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    if-nez p1, :cond_3

    goto :goto_1

    .line 42
    :cond_3
    :try_start_4
    new-instance p2, Ljava/lang/String;

    invoke-virtual {p1}, Lcom/huawei/hms/network/httpclient/ResponseBody;->bytes()[B

    move-result-object p1

    invoke-direct {p2, p1}, Ljava/lang/String;-><init>([B)V
    :try_end_4
    .catch Ljava/io/IOException; {:try_start_4 .. :try_end_4} :catch_1
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    .line 47
    :try_start_5
    invoke-static {p2}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result p1
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_0

    if-eqz p1, :cond_4

    goto :goto_1

    .line 52
    :cond_4
    :try_start_6
    new-instance p1, Lcom/google/gson/Gson;

    invoke-direct {p1}, Lcom/google/gson/Gson;-><init>()V

    const-class p3, Lcom/huawei/hms/mlsdk/internal/client/rest/RestErrorResponse;

    invoke-virtual {p1, p2, p3}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/huawei/hms/mlsdk/internal/client/rest/RestErrorResponse;
    :try_end_6
    .catch Lcom/google/gson/JsonSyntaxException; {:try_start_6 .. :try_end_6} :catch_1
    .catchall {:try_start_6 .. :try_end_6} :catchall_0

    :try_start_7
    const-string p2, "001001"
    :try_end_7
    .catchall {:try_start_7 .. :try_end_7} :catchall_0

    .line 53
    :try_start_8
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/internal/client/rest/RestErrorResponse;->getRetCode()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1
    :try_end_8
    .catch Lcom/google/gson/JsonSyntaxException; {:try_start_8 .. :try_end_8} :catch_1
    .catchall {:try_start_8 .. :try_end_8} :catchall_0

    goto :goto_2

    :catch_1
    :goto_1
    const/4 p1, 0x0

    :goto_2
    if-eqz p1, :cond_5

    .line 54
    :try_start_9
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string p2, "Token is invalid or expired."

    const/16 p3, 0x13

    invoke-direct {p1, p2, p3}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 56
    :cond_5
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string p2, "Cloud product vision search failed."

    const/16 p3, 0xf

    invoke-direct {p1, p2, p3}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 58
    :cond_6
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string p2, "Cloud product vision search failed."

    const/4 p3, 0x3

    invoke-direct {p1, p2, p3}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1
    :try_end_9
    .catchall {:try_start_9 .. :try_end_9} :catchall_0

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public a(Ljava/lang/String;)Lcom/huawei/hms/network/restclient/RestClient;
    .locals 4

    .line 7
    invoke-static {}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getInstance()Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v0

    .line 8
    new-instance v1, Lcom/huawei/hms/mlsdk/internal/client/rest/f;

    const/4 v2, 0x0

    invoke-direct {v1, v0, v2}, Lcom/huawei/hms/mlsdk/internal/client/rest/f;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/internal/client/rest/f$a;)V

    .line 9
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-class v3, Lcom/huawei/hms/mlsdk/internal/client/rest/f;

    invoke-virtual {v3}, Ljava/lang/Class;->getName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v3, "."

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 10
    sget-object v3, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->b:Ljava/util/Map;

    invoke-interface {v3, v0}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_0

    .line 11
    sget-object p1, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->b:Ljava/util/Map;

    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    move-object v2, p1

    check-cast v2, Lcom/huawei/hms/network/restclient/RestClient;

    goto :goto_0

    .line 15
    :cond_0
    :try_start_0
    invoke-virtual {v1, p1}, Lcom/huawei/hms/mlsdk/internal/client/rest/a;->a(Ljava/lang/String;)Lcom/huawei/hms/network/restclient/RestClient;

    move-result-object p1

    .line 16
    invoke-direct {p0, v0, p1}, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->a(Ljava/lang/String;Lcom/huawei/hms/network/restclient/RestClient;)V
    :try_end_0
    .catch Ljava/lang/RuntimeException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    move-object v2, p1

    :catch_0
    :goto_0
    return-object v2

    :catch_1
    move-exception p1

    .line 19
    throw p1
.end method
