.class final Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager$Holder;
.super Ljava/lang/Object;
.source "AvailableAdapterManager.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "Holder"
.end annotation


# static fields
.field static final INSTANCE:Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 11
    new-instance v0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;-><init>(Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager$1;)V

    sput-object v0, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager$Holder;->INSTANCE:Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 10
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
