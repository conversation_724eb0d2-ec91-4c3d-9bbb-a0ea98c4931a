.class public Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
.super Ljava/lang/Object;
.source "MLFaceAnalyzerSetting.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Factory"
.end annotation


# instance fields
.field private featureType:I

.field private isMaxSizeFaceOnly:Z

.field private isPoseDisabled:Z

.field private isTracingAllowed:Z

.field private keyPointType:I

.field private minFaceProportion:F

.field private performanceType:I

.field private shapeType:I

.field private tracingMode:I


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    .line 2
    iput v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->keyPointType:I

    const/4 v1, 0x2

    .line 4
    iput v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->shapeType:I

    .line 6
    iput v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->featureType:I

    .line 8
    iput v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->performanceType:I

    const/4 v0, 0x0

    .line 10
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->isPoseDisabled:Z

    .line 11
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->isTracingAllowed:Z

    .line 12
    iput v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->tracingMode:I

    const v1, 0x3dcccccd    # 0.1f

    .line 13
    iput v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->minFaceProportion:F

    .line 14
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->isMaxSizeFaceOnly:Z

    return-void
.end method


# virtual methods
.method public allowTracing()Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
    .locals 1

    const/4 v0, 0x2

    .line 1
    invoke-virtual {p0, v0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->allowTracing(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    move-result-object v0

    return-object v0
.end method

.method public allowTracing(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
    .locals 1

    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->isTracingAllowed:Z

    .line 3
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->tracingMode:I

    return-object p0
.end method

.method public create()Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;
    .locals 11

    .line 1
    new-instance v10, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->keyPointType:I

    iget v2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->shapeType:I

    iget v3, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->featureType:I

    iget v4, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->performanceType:I

    iget-boolean v5, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->isPoseDisabled:Z

    iget-boolean v6, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->isTracingAllowed:Z

    iget v7, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->tracingMode:I

    iget-boolean v8, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->isMaxSizeFaceOnly:Z

    iget v9, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->minFaceProportion:F

    move-object v0, v10

    invoke-direct/range {v0 .. v9}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;-><init>(IIIIZZIZF)V

    return-object v10
.end method

.method public setFeatureType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
    .locals 2

    const/4 v0, 0x1

    if-eq p1, v0, :cond_0

    const/4 v1, 0x2

    if-eq p1, v1, :cond_0

    move p1, v0

    .line 1
    :cond_0
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->featureType:I

    return-object p0
.end method

.method public setKeyPointType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->keyPointType:I

    return-object p0
.end method

.method public setMaxSizeFaceOnly(Z)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->isMaxSizeFaceOnly:Z

    return-object p0
.end method

.method public setMinFaceProportion(F)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->minFaceProportion:F

    return-object p0
.end method

.method public setPerformanceType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->performanceType:I

    return-object p0
.end method

.method public setPoseDisabled(Z)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->isPoseDisabled:Z

    return-object p0
.end method

.method public setShapeType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->shapeType:I

    return-object p0
.end method

.method public setTracingAllowed(Z)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
    .locals 1

    const/4 v0, 0x2

    .line 1
    invoke-virtual {p0, p1, v0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->setTracingAllowed(ZI)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    move-result-object p1

    return-object p1
.end method

.method public setTracingAllowed(ZI)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;
    .locals 0

    .line 2
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->isTracingAllowed:Z

    if-eqz p1, :cond_0

    .line 4
    iput p2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->tracingMode:I

    :cond_0
    return-object p0
.end method
