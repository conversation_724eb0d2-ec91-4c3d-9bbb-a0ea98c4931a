.class Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer$1;
.super Ljava/lang/Object;
.source "MLObjectAnalyzer.java"

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->asyncAnalyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Ljava/util/List<",
        "Lcom/huawei/hms/mlsdk/objects/MLObject;",
        ">;>;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;

.field final synthetic val$mlframe:Lcom/huawei/hms/mlsdk/common/MLFrame;

.field final synthetic val$optionsParcel:Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;


# direct methods
.method constructor <init>(Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;

    iput-object p2, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer$1;->val$mlframe:Lcom/huawei/hms/mlsdk/common/MLFrame;

    iput-object p3, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer$1;->val$optionsParcel:Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic call()Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer$1;->call()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public call()Ljava/util/List;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/objects/MLObject;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->access$000(Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->toBundle()Landroid/os/Bundle;

    move-result-object v0

    .line 3
    invoke-static {}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->getInstance()Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;

    move-result-object v1

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer$1;->this$0:Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;

    invoke-static {v2}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->access$000(Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v2

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v2

    iget-object v3, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer$1;->val$mlframe:Lcom/huawei/hms/mlsdk/common/MLFrame;

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer$1;->val$optionsParcel:Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;

    invoke-virtual {v1, v2, v0, v3, v4}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->detect(Landroid/content/Context;Landroid/os/Bundle;Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)Ljava/util/List;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 5
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer$1;->val$optionsParcel:Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/objects/MLObjectAnalyzer;->access$100(Ljava/util/List;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)Ljava/util/List;

    move-result-object v0

    return-object v0

    .line 7
    :cond_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    return-object v0
.end method
