.class public Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;
.super Ljava/lang/Object;
.source "MLRemoteTextSetting.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Factory"
.end annotation


# instance fields
.field private borderType:Ljava/lang/String;

.field private enableFingerprintVerification:Z

.field private handwritingType:Z

.field private languageList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private textType:I


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->languageList:Ljava/util/List;

    const/4 v0, 0x1

    .line 3
    iput v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->textType:I

    const/4 v0, 0x0

    .line 4
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->enableFingerprintVerification:Z

    const-string v1, "NGON"

    .line 5
    iput-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->borderType:Ljava/lang/String;

    .line 6
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->handwritingType:Z

    return-void
.end method


# virtual methods
.method public create()Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;
    .locals 14

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->handwritingType:Z

    if-eqz v0, :cond_0

    .line 2
    new-instance v0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    const-string/jumbo v1, "zh"

    filled-new-array {v1}, [Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->asList([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v2

    iget-boolean v4, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->enableFingerprintVerification:Z

    const/4 v3, 0x1

    const/4 v6, 0x1

    const/4 v7, 0x0

    const-string v5, "NGON"

    move-object v1, v0

    invoke-direct/range {v1 .. v7}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;-><init>(Ljava/util/List;IZLjava/lang/String;ILcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$1;)V

    return-object v0

    .line 4
    :cond_0
    new-instance v0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    iget-object v9, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->languageList:Ljava/util/List;

    iget v10, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->textType:I

    iget-boolean v11, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->enableFingerprintVerification:Z

    iget-object v12, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->borderType:Ljava/lang/String;

    const/4 v13, 0x0

    move-object v8, v0

    invoke-direct/range {v8 .. v13}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;-><init>(Ljava/util/List;IZLjava/lang/String;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$1;)V

    return-object v0
.end method

.method public enableFingerprintVerification()Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;
    .locals 1

    const/4 v0, 0x1

    .line 1
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->enableFingerprintVerification:Z

    return-object p0
.end method

.method public setBorderType(Ljava/lang/String;)Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->borderType:Ljava/lang/String;

    return-object p0
.end method

.method public setLanguageList(Ljava/util/List;)Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;)",
            "Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;"
        }
    .end annotation

    if-eqz p1, :cond_0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->languageList:Ljava/util/List;

    .line 3
    :cond_0
    iget-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->languageList:Ljava/util/List;

    invoke-static {p1}, Ljava/util/Collections;->sort(Ljava/util/List;)V

    return-object p0
.end method

.method public setTextDensityScene(I)Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;
    .locals 2

    const/4 v0, 0x1

    if-eq p1, v0, :cond_0

    const/4 v1, 0x2

    if-eq p1, v1, :cond_0

    .line 1
    iput v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->textType:I

    const-string v0, "MLRemoteTextSetting-RecognizerOptions"

    const-string v1, "ModelType should be either 1 for TYPE_LOOSE or 2 for TYPE_COMPACT, set ModelType TYPE_LOOSE."

    .line 2
    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/base/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 4
    :cond_0
    iput p1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->textType:I

    return-object p0
.end method

.method public setTextFontScene(I)Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;
    .locals 1

    const/4 v0, 0x1

    if-ne p1, v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    .line 1
    :goto_0
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;->handwritingType:Z

    return-object p0
.end method
