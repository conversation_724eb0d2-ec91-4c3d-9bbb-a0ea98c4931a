.class public Lcom/huawei/hms/network/embedded/y6$a;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/huawei/hms/network/embedded/y6;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/network/embedded/y6;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lcom/huawei/hms/network/embedded/i7;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/network/embedded/i7;",
            ")",
            "Ljava/util/List<",
            "Lcom/huawei/hms/network/embedded/x6;",
            ">;"
        }
    .end annotation

    invoke-static {}, Ljava/util/Collections;->emptyList()Ljava/util/List;

    move-result-object p1

    return-object p1
.end method

.method public a(Lcom/huawei/hms/network/embedded/i7;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/network/embedded/i7;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/network/embedded/x6;",
            ">;)V"
        }
    .end annotation

    return-void
.end method
