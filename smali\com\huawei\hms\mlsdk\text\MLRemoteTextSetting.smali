.class public Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;
.super Ljava/lang/Object;
.source "MLRemoteTextSetting.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$Factory;
    }
.end annotation


# static fields
.field public static final ARC:Ljava/lang/String; = "ARC"

.field public static final NGON:Ljava/lang/String; = "NGON"

.field public static final OCR_COMPACT_SCENE:I = 0x2

.field public static final OCR_HANDWRITTENFONT_SCENE:I = 0x1

.field public static final OCR_LOOSE_SCENE:I = 0x1

.field public static final OCR_PRINTFONT_SCENE:I


# instance fields
.field private final borderType:Ljava/lang/String;

.field private final enableFingerprintVerification:Z

.field private final fontType:I

.field private final languageList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private final textDensityScene:I


# direct methods
.method private constructor <init>(Ljava/util/List;IZLjava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;IZ",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    if-nez p1, :cond_0

    .line 5
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->languageList:Ljava/util/List;

    goto :goto_0

    .line 7
    :cond_0
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->languageList:Ljava/util/List;

    .line 9
    :goto_0
    iput p2, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->textDensityScene:I

    .line 10
    iput-boolean p3, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->enableFingerprintVerification:Z

    .line 11
    iput-object p4, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->borderType:Ljava/lang/String;

    const/4 p1, 0x0

    .line 12
    iput p1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->fontType:I

    return-void
.end method

.method private constructor <init>(Ljava/util/List;IZLjava/lang/String;I)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;IZ",
            "Ljava/lang/String;",
            "I)V"
        }
    .end annotation

    .line 13
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    if-nez p1, :cond_0

    .line 15
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->languageList:Ljava/util/List;

    goto :goto_0

    .line 17
    :cond_0
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->languageList:Ljava/util/List;

    .line 19
    :goto_0
    iput p2, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->textDensityScene:I

    .line 20
    iput-boolean p3, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->enableFingerprintVerification:Z

    .line 21
    iput-object p4, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->borderType:Ljava/lang/String;

    const/4 p1, 0x1

    .line 22
    iput p1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->fontType:I

    return-void
.end method

.method synthetic constructor <init>(Ljava/util/List;IZLjava/lang/String;ILcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$1;)V
    .locals 0

    .line 1
    invoke-direct/range {p0 .. p5}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;-><init>(Ljava/util/List;IZLjava/lang/String;I)V

    return-void
.end method

.method synthetic constructor <init>(Ljava/util/List;IZLjava/lang/String;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting$1;)V
    .locals 0

    .line 2
    invoke-direct {p0, p1, p2, p3, p4}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;-><init>(Ljava/util/List;IZLjava/lang/String;)V

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 4
    :cond_1
    check-cast p1, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    .line 5
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->languageList:Ljava/util/List;

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->getLanguageList()Ljava/util/List;

    move-result-object v3

    invoke-interface {v1, v3}, Ljava/util/List;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->borderType:Ljava/lang/String;

    iget-object v3, p1, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->borderType:Ljava/lang/String;

    if-ne v1, v3, :cond_2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->enableFingerprintVerification:Z

    iget-boolean p1, p1, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->enableFingerprintVerification:Z

    if-ne v1, p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public getBorderType()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->borderType:Ljava/lang/String;

    return-object v0
.end method

.method public getFontTypeScene()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->fontType:I

    return v0
.end method

.method public getLanguageList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->languageList:Ljava/util/List;

    return-object v0
.end method

.method public getTextDensityScene()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->textDensityScene:I

    return v0
.end method

.method public hashCode()I
    .locals 3

    const/4 v0, 0x4

    new-array v0, v0, [Ljava/lang/Object;

    .line 1
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->languageList:Ljava/util/List;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->borderType:Ljava/lang/String;

    const/4 v2, 0x1

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->enableFingerprintVerification:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->borderType:Ljava/lang/String;

    const/4 v2, 0x3

    aput-object v1, v0, v2

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->hashCode([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public final isEnableFingerprintVerification()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->enableFingerprintVerification:Z

    return v0
.end method
