.class final Lcom/huawei/hms/mlsdk/mlvision/c$b;
.super Ljava/lang/Object;
.source "RemoteLabelInitializer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/mlvision/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "b"
.end annotation


# static fields
.field static final a:Lcom/huawei/hms/mlsdk/mlvision/c;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/mlvision/c;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/huawei/hms/mlsdk/mlvision/c;-><init>(Lcom/huawei/hms/mlsdk/mlvision/c$a;)V

    sput-object v0, Lcom/huawei/hms/mlsdk/mlvision/c$b;->a:Lcom/huawei/hms/mlsdk/mlvision/c;

    return-void
.end method
