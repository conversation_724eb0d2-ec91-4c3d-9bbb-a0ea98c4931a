.class public Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ImageResult;
.super Ljava/lang/Object;
.source "ImageResult.java"


# instance fields
.field private imageId:Ljava/lang/String;

.field private innerUrl:Ljava/lang/String;

.field private score:F


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ImageResult;->imageId:Ljava/lang/String;

    return-object v0
.end method

.method public b()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ImageResult;->innerUrl:Ljava/lang/String;

    return-object v0
.end method

.method public c()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/ImageResult;->score:F

    return v0
.end method
