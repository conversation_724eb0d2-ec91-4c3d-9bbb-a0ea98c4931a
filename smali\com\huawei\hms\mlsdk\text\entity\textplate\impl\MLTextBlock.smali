.class public Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;
.super Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;
.source "MLTextBlock.java"


# instance fields
.field private textLineList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroid/graphics/Rect;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/TextLanguage;",
            ">;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;",
            ">;[",
            "Landroid/graphics/Point;",
            "F)V"
        }
    .end annotation

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p5

    move v5, p6

    .line 1
    invoke-direct/range {v0 .. v5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;[Landroid/graphics/Point;F)V

    .line 2
    iput-object p4, p0, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;->textLineList:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public getContents()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;->textLineList:Ljava/util/List;

    return-object v0
.end method
