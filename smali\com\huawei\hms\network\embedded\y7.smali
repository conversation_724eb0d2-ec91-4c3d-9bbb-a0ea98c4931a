.class public abstract Lcom/huawei/hms/network/embedded/y7;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static a:Lcom/huawei/hms/network/embedded/y7;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static a()V
    .locals 1

    new-instance v0, Lcom/huawei/hms/network/embedded/m7;

    invoke-direct {v0}, Lcom/huawei/hms/network/embedded/m7;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a(Lcom/huawei/hms/network/embedded/r7$a;)I
.end method

.method public abstract a(Lcom/huawei/hms/network/embedded/m7;Lcom/huawei/hms/network/embedded/p7;)Lcom/huawei/hms/network/embedded/p6;
.end method

.method public abstract a(Lcom/huawei/hms/network/embedded/r7;)Lcom/huawei/hms/network/embedded/r8;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end method

.method public abstract a(Lcom/huawei/hms/network/embedded/v6;)Lcom/huawei/hms/network/embedded/v8;
.end method

.method public abstract a(Lcom/huawei/hms/network/embedded/f7$a;Ljava/lang/String;)V
.end method

.method public abstract a(Lcom/huawei/hms/network/embedded/f7$a;Ljava/lang/String;Ljava/lang/String;)V
.end method

.method public abstract a(Lcom/huawei/hms/network/embedded/r7$a;Lcom/huawei/hms/network/embedded/r8;)V
.end method

.method public abstract a(Lcom/huawei/hms/network/embedded/w6;Ljavax/net/ssl/SSLSocket;Z)V
.end method

.method public abstract a(Lcom/huawei/hms/network/embedded/l6;Lcom/huawei/hms/network/embedded/l6;)Z
.end method
