.class public Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;
.super Ljava/lang/Object;
.source "MLImageSegmentationSetting.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Factory"
.end annotation


# instance fields
.field private analyzerType:I

.field private isExact:Z

.field private scene:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;->isExact:Z

    const/4 v0, 0x0

    .line 3
    iput v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;->analyzerType:I

    .line 4
    iput v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;->scene:I

    return-void
.end method


# virtual methods
.method public create()Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;
    .locals 5

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    iget v1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;->analyzerType:I

    iget-boolean v2, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;->isExact:Z

    iget v3, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;->scene:I

    const/4 v4, 0x0

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;-><init>(IZILcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$1;)V

    return-object v0
.end method

.method public setAnalyzerType(I)Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;->analyzerType:I

    return-object p0
.end method

.method public setExact(Z)Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;
    .locals 0

    .line 1
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;->isExact:Z

    return-object p0
.end method

.method public setScene(I)Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;->scene:I

    return-object p0
.end method
