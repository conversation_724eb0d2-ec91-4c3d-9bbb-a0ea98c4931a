.class public Lcom/huawei/hms/network/ai/v;
.super Ljava/lang/Object;
.source ""


# instance fields
.field public a:I

.field public b:I

.field public c:I

.field public d:I

.field public e:J

.field public f:J

.field public g:I

.field public h:I

.field public i:I

.field public j:Ljava/lang/String;

.field public k:I

.field public l:J


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/v;->d:I

    return v0
.end method

.method public a(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/v;->d:I

    return-void
.end method

.method public a(J)V
    .locals 0

    iput-wide p1, p0, Lcom/huawei/hms/network/ai/v;->f:J

    return-void
.end method

.method public a(<PERSON><PERSON><PERSON>/lang/String;)V
    .locals 0

    iput-object p1, p0, <PERSON><PERSON>/huawei/hms/network/ai/v;->j:Ljava/lang/String;

    return-void
.end method

.method public b()J
    .locals 2

    iget-wide v0, p0, Lcom/huawei/hms/network/ai/v;->f:J

    return-wide v0
.end method

.method public b(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/v;->g:I

    return-void
.end method

.method public b(J)V
    .locals 0

    iput-wide p1, p0, Lcom/huawei/hms/network/ai/v;->e:J

    return-void
.end method

.method public c()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/v;->g:I

    return v0
.end method

.method public c(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/v;->i:I

    return-void
.end method

.method public c(J)V
    .locals 0

    iput-wide p1, p0, Lcom/huawei/hms/network/ai/v;->l:J

    return-void
.end method

.method public d()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/v;->i:I

    return v0
.end method

.method public d(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/v;->h:I

    return-void
.end method

.method public e()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/v;->h:I

    return v0
.end method

.method public e(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/v;->b:I

    return-void
.end method

.method public f()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/v;->b:I

    return v0
.end method

.method public f(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/v;->c:I

    return-void
.end method

.method public g()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/v;->c:I

    return v0
.end method

.method public g(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/v;->k:I

    return-void
.end method

.method public h()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/v;->k:I

    return v0
.end method

.method public h(I)V
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/v;->a:I

    return-void
.end method

.method public i()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/ai/v;->j:Ljava/lang/String;

    return-object v0
.end method

.method public j()J
    .locals 2

    iget-wide v0, p0, Lcom/huawei/hms/network/ai/v;->e:J

    return-wide v0
.end method

.method public k()J
    .locals 2

    iget-wide v0, p0, Lcom/huawei/hms/network/ai/v;->l:J

    return-wide v0
.end method

.method public l()I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/v;->a:I

    return v0
.end method
