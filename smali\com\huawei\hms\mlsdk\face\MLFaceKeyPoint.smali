.class public Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;
.super Ljava/lang/Object;
.source "MLFaceKeyPoint.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint$Type;
    }
.end annotation


# static fields
.field public static final TYPE_BOTTOM_OF_MOUTH:I = 0x1

.field public static final TYPE_LEFT_CHEEK:I = 0x2

.field public static final TYPE_LEFT_EAR:I = 0x3

.field public static final TYPE_LEFT_EYE:I = 0x5

.field public static final TYPE_LEFT_SIDE_OF_MOUTH:I = 0x6

.field public static final TYPE_RIGHT_CHEEK:I = 0x8

.field public static final TYPE_RIGHT_EAR:I = 0x9

.field public static final TYPE_RIGHT_EYE:I = 0xb

.field public static final TYPE_RIGHT_SIDE_OF_MOUTH:I = 0xc

.field public static final TYPE_TIP_OF_LEFT_EAR:I = 0x4

.field public static final TYPE_TIP_OF_NOSE:I = 0x7

.field public static final TYPE_TIP_OF_RIGHT_EAR:I = 0xa


# instance fields
.field private coordinatePoint:Landroid/graphics/PointF;

.field private point:Lcom/huawei/hms/mlsdk/common/MLPosition;

.field private type:I


# direct methods
.method public constructor <init>(Lcom/huawei/hms/mlsdk/common/MLPosition;I)V
    .locals 3

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Landroid/graphics/PointF;

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getX()Ljava/lang/Float;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    move-result v1

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLPosition;->getY()Ljava/lang/Float;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/Float;->floatValue()F

    move-result v2

    invoke-direct {v0, v1, v2}, Landroid/graphics/PointF;-><init>(FF)V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;->coordinatePoint:Landroid/graphics/PointF;

    .line 3
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;->point:Lcom/huawei/hms/mlsdk/common/MLPosition;

    .line 4
    iput p2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;->type:I

    return-void
.end method


# virtual methods
.method public getCoordinatePoint()Landroid/graphics/PointF;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;->coordinatePoint:Landroid/graphics/PointF;

    return-object v0
.end method

.method public getPoint()Lcom/huawei/hms/mlsdk/common/MLPosition;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;->point:Lcom/huawei/hms/mlsdk/common/MLPosition;

    return-object v0
.end method

.method public getType()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;->type:I

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 1
    invoke-static {p0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->toStringHelper(Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;->type:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "type"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;->point:Lcom/huawei/hms/mlsdk/common/MLPosition;

    const-string v2, "position"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
