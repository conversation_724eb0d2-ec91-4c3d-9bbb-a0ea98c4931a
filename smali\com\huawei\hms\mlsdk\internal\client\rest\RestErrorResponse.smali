.class public Lcom/huawei/hms/mlsdk/internal/client/rest/RestErrorResponse;
.super Ljava/lang/Object;
.source "RestErrorResponse.java"


# instance fields
.field private retCode:Ljava/lang/String;

.field private retMsg:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getRetCode()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/RestErrorResponse;->retCode:Ljava/lang/String;

    return-object v0
.end method

.method public getRetMsg()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/RestErrorResponse;->retMsg:Ljava/lang/String;

    return-object v0
.end method

.method public setRetCode(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/RestErrorResponse;->retCode:Ljava/lang/String;

    return-void
.end method

.method public setRetMsg(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/RestErrorResponse;->retMsg:Ljava/lang/String;

    return-void
.end method
