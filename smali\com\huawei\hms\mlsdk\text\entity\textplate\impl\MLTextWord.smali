.class public Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;
.super Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;
.source "MLTextWord.java"


# direct methods
.method public constructor <init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;[Landroid/graphics/Point;F)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroid/graphics/Rect;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/TextLanguage;",
            ">;[",
            "Landroid/graphics/Point;",
            "F)V"
        }
    .end annotation

    .line 1
    invoke-direct/range {p0 .. p5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;[Landroid/graphics/Point;F)V

    return-void
.end method


# virtual methods
.method public getContents()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "+",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/IText;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x0

    return-object v0
.end method
