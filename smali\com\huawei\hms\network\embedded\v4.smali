.class public abstract Lcom/huawei/hms/network/embedded/v4;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a()I
.end method

.method public abstract b()Lcom/huawei/hms/network/embedded/s4;
.end method

.method public abstract c()Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;
.end method

.method public abstract d()I
.end method

.method public abstract e()Lcom/huawei/hms/network/embedded/y4;
.end method

.method public abstract f()Lcom/huawei/hms/network/netdiag/info/SignalInfoMetrics;
.end method

.method public abstract g()Lcom/huawei/hms/network/embedded/c5;
.end method

.method public abstract h()I
.end method
