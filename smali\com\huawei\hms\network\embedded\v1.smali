.class public Lcom/huawei/hms/network/embedded/v1;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/network/embedded/v1$b;,
        Lcom/huawei/hms/network/embedded/v1$a;
    }
.end annotation


# static fields
.field public static final a:Ljava/lang/String; = "Secure"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static decryptBody([B)[B
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    if-eqz p0, :cond_2

    array-length v0, p0

    if-eqz v0, :cond_2

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x13

    const/4 v2, 0x0

    if-lt v0, v1, :cond_1

    :try_start_0
    invoke-static {v2}, Lcom/huawei/hms/network/embedded/t1;->a(Z)[B

    move-result-object v0

    invoke-static {p0, v0}, Lcom/huawei/secure/android/common/encrypt/aes/AesGcm;->decrypt([B[B)[B

    move-result-object p0
    :try_end_0
    .catch Ljava/lang/IndexOutOfBoundsException; {:try_start_0 .. :try_end_0} :catch_0

    if-eqz p0, :cond_0

    array-length v0, p0

    if-eqz v0, :cond_0

    return-object p0

    :cond_0
    new-instance p0, Lcom/huawei/hms/network/embedded/v1$a;

    const-string v0, "Decrypt body failed"

    invoke-direct {p0, v0}, Lcom/huawei/hms/network/embedded/v1$a;-><init>(Ljava/lang/String;)V

    throw p0

    :catch_0
    new-instance p0, Lcom/huawei/hms/network/embedded/v1$a;

    const-string v0, "The cached file is tampered, throw a indexOutOfBoundsException"

    invoke-direct {p0, v0}, Lcom/huawei/hms/network/embedded/v1$a;-><init>(Ljava/lang/String;)V

    throw p0

    :cond_1
    const-string p0, "Secure"

    const-string v0, "android sdk version less than 19"

    invoke-static {p0, v0}, Lcom/huawei/hms/framework/common/Logger;->d(Ljava/lang/String;Ljava/lang/Object;)V

    new-array p0, v2, [B

    return-object p0

    :cond_2
    new-instance p0, Lcom/huawei/hms/network/embedded/v1$a;

    const-string v0, "Decrypt body is empty"

    invoke-direct {p0, v0}, Lcom/huawei/hms/network/embedded/v1$a;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method public static encryptBody([B)[B
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x13

    if-lt v0, v1, :cond_1

    const/4 v0, 0x1

    invoke-static {v0}, Lcom/huawei/hms/network/embedded/t1;->a(Z)[B

    move-result-object v0

    invoke-static {p0, v0}, Lcom/huawei/secure/android/common/encrypt/aes/AesGcm;->encrypt([B[B)[B

    move-result-object p0

    if-eqz p0, :cond_0

    array-length v0, p0

    if-eqz v0, :cond_0

    return-object p0

    :cond_0
    new-instance p0, Lcom/huawei/hms/network/embedded/v1$a;

    const-string v0, "Encrypt body failed"

    invoke-direct {p0, v0}, Lcom/huawei/hms/network/embedded/v1$a;-><init>(Ljava/lang/String;)V

    throw p0

    :cond_1
    const-string p0, "Secure"

    const-string v0, "android sdk version less than 19"

    invoke-static {p0, v0}, Lcom/huawei/hms/framework/common/Logger;->d(Ljava/lang/String;Ljava/lang/Object;)V

    const/4 p0, 0x0

    new-array p0, p0, [B

    return-object p0
.end method
