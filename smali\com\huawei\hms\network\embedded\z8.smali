.class public final Lcom/huawei/hms/network/embedded/z8;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/network/embedded/z8$b;
    }
.end annotation


# static fields
.field public static final synthetic q:Z = true


# instance fields
.field public final a:Lcom/huawei/hms/network/embedded/m7;

.field public final b:Lcom/huawei/hms/network/embedded/v8;

.field public final c:Lcom/huawei/hms/network/embedded/p6;

.field public final d:Lcom/huawei/hms/network/embedded/c7;

.field public final e:Lcom/huawei/hms/network/embedded/va;

.field public f:Ljava/lang/Object;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field public g:Lcom/huawei/hms/network/embedded/p7;

.field public h:Lcom/huawei/hms/network/embedded/s8;

.field public i:Lcom/huawei/hms/network/embedded/u8;

.field public j:Lcom/huawei/hms/network/embedded/r8;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field public k:Z

.field public l:Z

.field public m:Z

.field public n:Z

.field public o:Z

.field public final p:I


# direct methods
.method public static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Lcom/huawei/hms/network/embedded/m7;Lcom/huawei/hms/network/embedded/p6;)V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/huawei/hms/network/embedded/z8$a;

    invoke-direct {v0, p0}, Lcom/huawei/hms/network/embedded/z8$a;-><init>(Lcom/huawei/hms/network/embedded/z8;)V

    iput-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->e:Lcom/huawei/hms/network/embedded/va;

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/z8;->a:Lcom/huawei/hms/network/embedded/m7;

    sget-object v1, Lcom/huawei/hms/network/embedded/y7;->a:Lcom/huawei/hms/network/embedded/y7;

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/m7;->g()Lcom/huawei/hms/network/embedded/v6;

    move-result-object v2

    invoke-virtual {v1, v2}, Lcom/huawei/hms/network/embedded/y7;->a(Lcom/huawei/hms/network/embedded/v6;)Lcom/huawei/hms/network/embedded/v8;

    move-result-object v1

    iput-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    iput-object p2, p0, Lcom/huawei/hms/network/embedded/z8;->c:Lcom/huawei/hms/network/embedded/p6;

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/m7;->l()Lcom/huawei/hms/network/embedded/c7$b;

    move-result-object v1

    invoke-interface {v1, p2}, Lcom/huawei/hms/network/embedded/c7$b;->create(Lcom/huawei/hms/network/embedded/p6;)Lcom/huawei/hms/network/embedded/c7;

    move-result-object p2

    iput-object p2, p0, Lcom/huawei/hms/network/embedded/z8;->d:Lcom/huawei/hms/network/embedded/c7;

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/m7;->c()I

    move-result p2

    int-to-long v1, p2

    sget-object p2, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-virtual {v0, v1, v2, p2}, Lcom/huawei/hms/network/embedded/wb;->b(JLjava/util/concurrent/TimeUnit;)Lcom/huawei/hms/network/embedded/wb;

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/m7;->f()I

    move-result p1

    iput p1, p0, Lcom/huawei/hms/network/embedded/z8;->p:I

    return-void
.end method

.method private createAddress(Lcom/huawei/hms/network/embedded/i7;)Lcom/huawei/hms/network/embedded/l6;
    .locals 17

    move-object/from16 v0, p0

    invoke-virtual/range {p1 .. p1}, Lcom/huawei/hms/network/embedded/i7;->i()Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    iget-object v1, v0, Lcom/huawei/hms/network/embedded/z8;->a:Lcom/huawei/hms/network/embedded/m7;

    invoke-virtual {v1}, Lcom/huawei/hms/network/embedded/m7;->C()Ljavax/net/ssl/SSLSocketFactory;

    move-result-object v2

    iget-object v1, v0, Lcom/huawei/hms/network/embedded/z8;->a:Lcom/huawei/hms/network/embedded/m7;

    invoke-virtual {v1}, Lcom/huawei/hms/network/embedded/m7;->p()Ljavax/net/ssl/HostnameVerifier;

    move-result-object v1

    iget-object v3, v0, Lcom/huawei/hms/network/embedded/z8;->a:Lcom/huawei/hms/network/embedded/m7;

    invoke-virtual {v3}, Lcom/huawei/hms/network/embedded/m7;->d()Lcom/huawei/hms/network/embedded/r6;

    move-result-object v3

    move-object v10, v1

    move-object v9, v2

    move-object v11, v3

    goto :goto_0

    :cond_0
    move-object v9, v2

    move-object v10, v9

    move-object v11, v10

    :goto_0
    new-instance v1, Lcom/huawei/hms/network/embedded/l6;

    invoke-virtual/range {p1 .. p1}, Lcom/huawei/hms/network/embedded/i7;->h()Ljava/lang/String;

    move-result-object v5

    invoke-virtual/range {p1 .. p1}, Lcom/huawei/hms/network/embedded/i7;->n()I

    move-result v6

    iget-object v2, v0, Lcom/huawei/hms/network/embedded/z8;->a:Lcom/huawei/hms/network/embedded/m7;

    invoke-virtual {v2}, Lcom/huawei/hms/network/embedded/m7;->k()Lcom/huawei/hms/network/embedded/b7;

    move-result-object v7

    iget-object v2, v0, Lcom/huawei/hms/network/embedded/z8;->a:Lcom/huawei/hms/network/embedded/m7;

    invoke-virtual {v2}, Lcom/huawei/hms/network/embedded/m7;->B()Ljavax/net/SocketFactory;

    move-result-object v8

    iget-object v2, v0, Lcom/huawei/hms/network/embedded/z8;->a:Lcom/huawei/hms/network/embedded/m7;

    invoke-virtual {v2}, Lcom/huawei/hms/network/embedded/m7;->x()Lcom/huawei/hms/network/embedded/m6;

    move-result-object v12

    iget-object v2, v0, Lcom/huawei/hms/network/embedded/z8;->a:Lcom/huawei/hms/network/embedded/m7;

    invoke-virtual {v2}, Lcom/huawei/hms/network/embedded/m7;->w()Ljava/net/Proxy;

    move-result-object v13

    iget-object v2, v0, Lcom/huawei/hms/network/embedded/z8;->a:Lcom/huawei/hms/network/embedded/m7;

    invoke-virtual {v2}, Lcom/huawei/hms/network/embedded/m7;->v()Ljava/util/List;

    move-result-object v14

    iget-object v2, v0, Lcom/huawei/hms/network/embedded/z8;->a:Lcom/huawei/hms/network/embedded/m7;

    invoke-virtual {v2}, Lcom/huawei/hms/network/embedded/m7;->h()Ljava/util/List;

    move-result-object v15

    iget-object v2, v0, Lcom/huawei/hms/network/embedded/z8;->a:Lcom/huawei/hms/network/embedded/m7;

    invoke-virtual {v2}, Lcom/huawei/hms/network/embedded/m7;->y()Ljava/net/ProxySelector;

    move-result-object v16

    move-object v4, v1

    invoke-direct/range {v4 .. v16}, Lcom/huawei/hms/network/embedded/l6;-><init>(Ljava/lang/String;ILcom/huawei/hms/network/embedded/b7;Ljavax/net/SocketFactory;Ljavax/net/ssl/SSLSocketFactory;Ljavax/net/ssl/HostnameVerifier;Lcom/huawei/hms/network/embedded/r6;Lcom/huawei/hms/network/embedded/m6;Ljava/net/Proxy;Ljava/util/List;Ljava/util/List;Ljava/net/ProxySelector;)V

    return-object v1
.end method

.method private maybeReleaseConnection(Ljava/io/IOException;Z)Ljava/io/IOException;
    .locals 5
    .param p1    # Ljava/io/IOException;
        .annotation runtime Ljavax/annotation/Nullable;
        .end annotation
    .end param
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    monitor-enter v0

    if-eqz p2, :cond_1

    :try_start_0
    iget-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->j:Lcom/huawei/hms/network/embedded/r8;

    if-nez v1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "cannot release connection while it is in use"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    :goto_0
    iget-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->i:Lcom/huawei/hms/network/embedded/u8;

    const/4 v2, 0x0

    if-eqz v1, :cond_3

    iget-object v3, p0, Lcom/huawei/hms/network/embedded/z8;->j:Lcom/huawei/hms/network/embedded/r8;

    if-nez v3, :cond_3

    if-nez p2, :cond_2

    iget-boolean p2, p0, Lcom/huawei/hms/network/embedded/z8;->o:Z

    if-eqz p2, :cond_3

    :cond_2
    invoke-virtual {p0}, Lcom/huawei/hms/network/embedded/z8;->releaseConnectionNoEvents()Ljava/net/Socket;

    move-result-object p2

    goto :goto_1

    :cond_3
    move-object p2, v2

    :goto_1
    iget-object v3, p0, Lcom/huawei/hms/network/embedded/z8;->i:Lcom/huawei/hms/network/embedded/u8;

    if-eqz v3, :cond_4

    move-object v1, v2

    :cond_4
    iget-boolean v2, p0, Lcom/huawei/hms/network/embedded/z8;->o:Z

    const/4 v3, 0x1

    const/4 v4, 0x0

    if-eqz v2, :cond_5

    iget-object v2, p0, Lcom/huawei/hms/network/embedded/z8;->j:Lcom/huawei/hms/network/embedded/r8;

    if-nez v2, :cond_5

    move v2, v3

    goto :goto_2

    :cond_5
    move v2, v4

    :goto_2
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    invoke-static {p2}, Lcom/huawei/hms/network/embedded/b8;->a(Ljava/net/Socket;)V

    if-eqz v1, :cond_6

    iget-object p2, p0, Lcom/huawei/hms/network/embedded/z8;->d:Lcom/huawei/hms/network/embedded/c7;

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->c:Lcom/huawei/hms/network/embedded/p6;

    invoke-virtual {p2, v0, v1}, Lcom/huawei/hms/network/embedded/c7;->connectionReleased(Lcom/huawei/hms/network/embedded/p6;Lcom/huawei/hms/network/embedded/u6;)V

    :cond_6
    if-eqz v2, :cond_9

    if-eqz p1, :cond_7

    goto :goto_3

    :cond_7
    move v3, v4

    :goto_3
    invoke-direct {p0, p1}, Lcom/huawei/hms/network/embedded/z8;->timeoutExit(Ljava/io/IOException;)Ljava/io/IOException;

    move-result-object p1

    iget-object p2, p0, Lcom/huawei/hms/network/embedded/z8;->d:Lcom/huawei/hms/network/embedded/c7;

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->c:Lcom/huawei/hms/network/embedded/p6;

    if-eqz v3, :cond_8

    invoke-virtual {p2, v0, p1}, Lcom/huawei/hms/network/embedded/c7;->callFailed(Lcom/huawei/hms/network/embedded/p6;Ljava/io/IOException;)V

    goto :goto_4

    :cond_8
    invoke-virtual {p2, v0}, Lcom/huawei/hms/network/embedded/c7;->callEnd(Lcom/huawei/hms/network/embedded/p6;)V

    :cond_9
    :goto_4
    return-object p1

    :catchall_0
    move-exception p1

    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method private timeoutExit(Ljava/io/IOException;)Ljava/io/IOException;
    .locals 2
    .param p1    # Ljava/io/IOException;
        .annotation runtime Ljavax/annotation/Nullable;
        .end annotation
    .end param
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation

    iget-boolean v0, p0, Lcom/huawei/hms/network/embedded/z8;->n:Z

    if-eqz v0, :cond_0

    return-object p1

    :cond_0
    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->e:Lcom/huawei/hms/network/embedded/va;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/va;->h()Z

    move-result v0

    if-nez v0, :cond_1

    return-object p1

    :cond_1
    new-instance v0, Ljava/io/InterruptedIOException;

    const-string v1, "timeout"

    invoke-direct {v0, v1}, Ljava/io/InterruptedIOException;-><init>(Ljava/lang/String;)V

    if-eqz p1, :cond_2

    invoke-virtual {v0, p1}, Ljava/io/InterruptedIOException;->initCause(Ljava/lang/Throwable;)Ljava/lang/Throwable;

    :cond_2
    return-object v0
.end method


# virtual methods
.method public acquireConnectionNoEvents(Lcom/huawei/hms/network/embedded/u8;)V
    .locals 2

    sget-boolean v0, Lcom/huawei/hms/network/embedded/z8;->q:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    invoke-static {v0}, Ljava/lang/Thread;->holdsLock(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->i:Lcom/huawei/hms/network/embedded/u8;

    if-nez v0, :cond_2

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/z8;->i:Lcom/huawei/hms/network/embedded/u8;

    iget-object p1, p1, Lcom/huawei/hms/network/embedded/u8;->p:Ljava/util/List;

    new-instance v0, Lcom/huawei/hms/network/embedded/z8$b;

    iget-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->f:Ljava/lang/Object;

    invoke-direct {v0, p0, v1}, Lcom/huawei/hms/network/embedded/z8$b;-><init>(Lcom/huawei/hms/network/embedded/z8;Ljava/lang/Object;)V

    invoke-interface {p1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-void

    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1
.end method

.method public callStart()V
    .locals 2

    invoke-static {}, Lcom/huawei/hms/network/embedded/ia;->f()Lcom/huawei/hms/network/embedded/ia;

    move-result-object v0

    const-string v1, "response.body().close()"

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/embedded/ia;->a(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    iput-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->f:Ljava/lang/Object;

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->d:Lcom/huawei/hms/network/embedded/c7;

    iget-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->c:Lcom/huawei/hms/network/embedded/p6;

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/embedded/c7;->callStart(Lcom/huawei/hms/network/embedded/p6;)V

    return-void
.end method

.method public canRetry()Z
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->h:Lcom/huawei/hms/network/embedded/s8;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/s8;->d()Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->h:Lcom/huawei/hms/network/embedded/s8;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/s8;->c()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public cancel()V
    .locals 3

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    monitor-enter v0

    const/4 v1, 0x1

    :try_start_0
    iput-boolean v1, p0, Lcom/huawei/hms/network/embedded/z8;->m:Z

    iget-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->j:Lcom/huawei/hms/network/embedded/r8;

    iget-object v2, p0, Lcom/huawei/hms/network/embedded/z8;->h:Lcom/huawei/hms/network/embedded/s8;

    if-eqz v2, :cond_0

    invoke-virtual {v2}, Lcom/huawei/hms/network/embedded/s8;->a()Lcom/huawei/hms/network/embedded/u8;

    move-result-object v2

    if-eqz v2, :cond_0

    iget-object v2, p0, Lcom/huawei/hms/network/embedded/z8;->h:Lcom/huawei/hms/network/embedded/s8;

    invoke-virtual {v2}, Lcom/huawei/hms/network/embedded/s8;->a()Lcom/huawei/hms/network/embedded/u8;

    move-result-object v2

    goto :goto_0

    :cond_0
    iget-object v2, p0, Lcom/huawei/hms/network/embedded/z8;->i:Lcom/huawei/hms/network/embedded/u8;

    :goto_0
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v1, :cond_1

    invoke-virtual {v1}, Lcom/huawei/hms/network/embedded/r8;->a()V

    goto :goto_1

    :cond_1
    if-eqz v2, :cond_2

    invoke-virtual {v2}, Lcom/huawei/hms/network/embedded/u8;->e()V

    :cond_2
    :goto_1
    return-void

    :catchall_0
    move-exception v1

    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v1
.end method

.method public exchangeDoneDueToException()V
    .locals 2

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    monitor-enter v0

    :try_start_0
    iget-boolean v1, p0, Lcom/huawei/hms/network/embedded/z8;->o:Z

    if-nez v1, :cond_0

    const/4 v1, 0x0

    iput-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->j:Lcom/huawei/hms/network/embedded/r8;

    monitor-exit v0

    return-void

    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    invoke-direct {v1}, Ljava/lang/IllegalStateException;-><init>()V

    throw v1

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method public exchangeMessageDone(Lcom/huawei/hms/network/embedded/r8;ZZLjava/io/IOException;)Ljava/io/IOException;
    .locals 3
    .param p4    # Ljava/io/IOException;
        .annotation runtime Ljavax/annotation/Nullable;
        .end annotation
    .end param
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->j:Lcom/huawei/hms/network/embedded/r8;

    if-eq p1, v1, :cond_0

    monitor-exit v0

    return-object p4

    :cond_0
    const/4 p1, 0x0

    const/4 v2, 0x1

    if-eqz p2, :cond_1

    iget-boolean p2, p0, Lcom/huawei/hms/network/embedded/z8;->k:Z

    xor-int/2addr p2, v2

    iput-boolean v2, p0, Lcom/huawei/hms/network/embedded/z8;->k:Z

    goto :goto_0

    :cond_1
    move p2, p1

    :goto_0
    if-eqz p3, :cond_3

    iget-boolean p3, p0, Lcom/huawei/hms/network/embedded/z8;->l:Z

    if-nez p3, :cond_2

    move p2, v2

    :cond_2
    iput-boolean v2, p0, Lcom/huawei/hms/network/embedded/z8;->l:Z

    :cond_3
    iget-boolean p3, p0, Lcom/huawei/hms/network/embedded/z8;->k:Z

    if-eqz p3, :cond_4

    iget-boolean p3, p0, Lcom/huawei/hms/network/embedded/z8;->l:Z

    if-eqz p3, :cond_4

    if-eqz p2, :cond_4

    invoke-virtual {v1}, Lcom/huawei/hms/network/embedded/r8;->b()Lcom/huawei/hms/network/embedded/u8;

    move-result-object p2

    iget p3, p2, Lcom/huawei/hms/network/embedded/u8;->m:I

    add-int/2addr p3, v2

    iput p3, p2, Lcom/huawei/hms/network/embedded/u8;->m:I

    const/4 p2, 0x0

    iput-object p2, p0, Lcom/huawei/hms/network/embedded/z8;->j:Lcom/huawei/hms/network/embedded/r8;

    goto :goto_1

    :cond_4
    move v2, p1

    :goto_1
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v2, :cond_5

    invoke-direct {p0, p4, p1}, Lcom/huawei/hms/network/embedded/z8;->maybeReleaseConnection(Ljava/io/IOException;Z)Ljava/io/IOException;

    move-result-object p4

    :cond_5
    return-object p4

    :catchall_0
    move-exception p1

    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public getExchangeFinder()Lcom/huawei/hms/network/embedded/s8;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->h:Lcom/huawei/hms/network/embedded/s8;

    return-object v0
.end method

.method public getRequest()Lcom/huawei/hms/network/embedded/p7;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->g:Lcom/huawei/hms/network/embedded/p7;

    return-object v0
.end method

.method public getSelection()Lcom/huawei/hms/network/embedded/y8$a;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->h:Lcom/huawei/hms/network/embedded/s8;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/s8;->b()Lcom/huawei/hms/network/embedded/y8$a;

    move-result-object v0

    return-object v0
.end method

.method public hasExchange()Z
    .locals 2

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    monitor-enter v0

    :try_start_0
    iget-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->j:Lcom/huawei/hms/network/embedded/r8;

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    monitor-exit v0

    return v1

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method public isCanceled()Z
    .locals 2

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    monitor-enter v0

    :try_start_0
    iget-boolean v1, p0, Lcom/huawei/hms/network/embedded/z8;->m:Z

    monitor-exit v0

    return v1

    :catchall_0
    move-exception v1

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw v1
.end method

.method public newExchange(Lcom/huawei/hms/network/embedded/j7$a;Z)Lcom/huawei/hms/network/embedded/r8;
    .locals 8

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    monitor-enter v0

    :try_start_0
    iget-boolean v1, p0, Lcom/huawei/hms/network/embedded/z8;->o:Z

    if-nez v1, :cond_1

    iget-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->j:Lcom/huawei/hms/network/embedded/r8;

    if-nez v1, :cond_0

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->h:Lcom/huawei/hms/network/embedded/s8;

    iget-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->a:Lcom/huawei/hms/network/embedded/m7;

    invoke-virtual {v0, v1, p1, p2}, Lcom/huawei/hms/network/embedded/s8;->a(Lcom/huawei/hms/network/embedded/m7;Lcom/huawei/hms/network/embedded/j7$a;Z)Lcom/huawei/hms/network/embedded/c9;

    move-result-object v7

    new-instance p1, Lcom/huawei/hms/network/embedded/r8;

    iget-object v4, p0, Lcom/huawei/hms/network/embedded/z8;->c:Lcom/huawei/hms/network/embedded/p6;

    iget-object v5, p0, Lcom/huawei/hms/network/embedded/z8;->d:Lcom/huawei/hms/network/embedded/c7;

    iget-object v6, p0, Lcom/huawei/hms/network/embedded/z8;->h:Lcom/huawei/hms/network/embedded/s8;

    move-object v2, p1

    move-object v3, p0

    invoke-direct/range {v2 .. v7}, Lcom/huawei/hms/network/embedded/r8;-><init>(Lcom/huawei/hms/network/embedded/z8;Lcom/huawei/hms/network/embedded/p6;Lcom/huawei/hms/network/embedded/c7;Lcom/huawei/hms/network/embedded/s8;Lcom/huawei/hms/network/embedded/c9;)V

    iget-object p2, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    monitor-enter p2

    :try_start_1
    iput-object p1, p0, Lcom/huawei/hms/network/embedded/z8;->j:Lcom/huawei/hms/network/embedded/r8;

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/huawei/hms/network/embedded/z8;->k:Z

    iput-boolean v0, p0, Lcom/huawei/hms/network/embedded/z8;->l:Z

    monitor-exit p2

    return-object p1

    :catchall_0
    move-exception p1

    monitor-exit p2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1

    :cond_0
    :try_start_2
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "cannot make a new request because the previous response is still open: please call response.close()"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "released"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :catchall_1
    move-exception p1

    monitor-exit v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    throw p1
.end method

.method public noMoreExchanges(Ljava/io/IOException;)Ljava/io/IOException;
    .locals 2
    .param p1    # Ljava/io/IOException;
        .annotation runtime Ljavax/annotation/Nullable;
        .end annotation
    .end param
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    monitor-enter v0

    const/4 v1, 0x1

    :try_start_0
    iput-boolean v1, p0, Lcom/huawei/hms/network/embedded/z8;->o:Z

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const/4 v0, 0x0

    invoke-direct {p0, p1, v0}, Lcom/huawei/hms/network/embedded/z8;->maybeReleaseConnection(Ljava/io/IOException;Z)Ljava/io/IOException;

    move-result-object p1

    return-object p1

    :catchall_0
    move-exception p1

    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public prepareToConnect(Lcom/huawei/hms/network/embedded/p7;)V
    .locals 8

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->g:Lcom/huawei/hms/network/embedded/p7;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/p7;->k()Lcom/huawei/hms/network/embedded/i7;

    move-result-object v0

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/p7;->k()Lcom/huawei/hms/network/embedded/i7;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/huawei/hms/network/embedded/b8;->a(Lcom/huawei/hms/network/embedded/i7;Lcom/huawei/hms/network/embedded/i7;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->h:Lcom/huawei/hms/network/embedded/s8;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/s8;->c()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->j:Lcom/huawei/hms/network/embedded/r8;

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->h:Lcom/huawei/hms/network/embedded/s8;

    if-eqz v0, :cond_2

    const/4 v0, 0x1

    const/4 v1, 0x0

    invoke-direct {p0, v1, v0}, Lcom/huawei/hms/network/embedded/z8;->maybeReleaseConnection(Ljava/io/IOException;Z)Ljava/io/IOException;

    iput-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->h:Lcom/huawei/hms/network/embedded/s8;

    goto :goto_0

    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    invoke-direct {p1}, Ljava/lang/IllegalStateException;-><init>()V

    throw p1

    :cond_2
    :goto_0
    iput-object p1, p0, Lcom/huawei/hms/network/embedded/z8;->g:Lcom/huawei/hms/network/embedded/p7;

    new-instance v7, Lcom/huawei/hms/network/embedded/s8;

    iget-object v2, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/p7;->k()Lcom/huawei/hms/network/embedded/i7;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/huawei/hms/network/embedded/z8;->createAddress(Lcom/huawei/hms/network/embedded/i7;)Lcom/huawei/hms/network/embedded/l6;

    move-result-object v3

    iget-object v4, p0, Lcom/huawei/hms/network/embedded/z8;->c:Lcom/huawei/hms/network/embedded/p6;

    iget-object v5, p0, Lcom/huawei/hms/network/embedded/z8;->d:Lcom/huawei/hms/network/embedded/c7;

    iget v6, p0, Lcom/huawei/hms/network/embedded/z8;->p:I

    move-object v0, v7

    move-object v1, p0

    invoke-direct/range {v0 .. v6}, Lcom/huawei/hms/network/embedded/s8;-><init>(Lcom/huawei/hms/network/embedded/z8;Lcom/huawei/hms/network/embedded/v8;Lcom/huawei/hms/network/embedded/l6;Lcom/huawei/hms/network/embedded/p6;Lcom/huawei/hms/network/embedded/c7;I)V

    iput-object v7, p0, Lcom/huawei/hms/network/embedded/z8;->h:Lcom/huawei/hms/network/embedded/s8;

    iget-object v0, v7, Lcom/huawei/hms/network/embedded/s8;->b:Lcom/huawei/hms/network/embedded/l6;

    const-string v1, "host"

    invoke-virtual {p1, v1}, Lcom/huawei/hms/network/embedded/p7;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/embedded/l6;->a(Ljava/lang/String;)V

    return-void
.end method

.method public releaseConnectionNoEvents()Ljava/net/Socket;
    .locals 4
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation

    sget-boolean v0, Lcom/huawei/hms/network/embedded/z8;->q:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    invoke-static {v0}, Ljava/lang/Thread;->holdsLock(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    :cond_1
    :goto_0
    const/4 v0, 0x0

    iget-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->i:Lcom/huawei/hms/network/embedded/u8;

    iget-object v1, v1, Lcom/huawei/hms/network/embedded/u8;->p:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    :goto_1
    const/4 v2, -0x1

    if-ge v0, v1, :cond_3

    iget-object v3, p0, Lcom/huawei/hms/network/embedded/z8;->i:Lcom/huawei/hms/network/embedded/u8;

    iget-object v3, v3, Lcom/huawei/hms/network/embedded/u8;->p:Ljava/util/List;

    invoke-interface {v3, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/ref/Reference;

    invoke-virtual {v3}, Ljava/lang/ref/Reference;->get()Ljava/lang/Object;

    move-result-object v3

    if-ne v3, p0, :cond_2

    goto :goto_2

    :cond_2
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    :cond_3
    move v0, v2

    :goto_2
    if-eq v0, v2, :cond_5

    iget-object v1, p0, Lcom/huawei/hms/network/embedded/z8;->i:Lcom/huawei/hms/network/embedded/u8;

    iget-object v2, v1, Lcom/huawei/hms/network/embedded/u8;->p:Ljava/util/List;

    invoke-interface {v2, v0}, Ljava/util/List;->remove(I)Ljava/lang/Object;

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->i:Lcom/huawei/hms/network/embedded/u8;

    iget-object v2, v1, Lcom/huawei/hms/network/embedded/u8;->p:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_4

    invoke-static {}, Ljava/lang/System;->nanoTime()J

    move-result-wide v2

    iput-wide v2, v1, Lcom/huawei/hms/network/embedded/u8;->q:J

    iget-object v2, p0, Lcom/huawei/hms/network/embedded/z8;->b:Lcom/huawei/hms/network/embedded/v8;

    invoke-virtual {v2, v1}, Lcom/huawei/hms/network/embedded/v8;->b(Lcom/huawei/hms/network/embedded/u8;)Z

    move-result v2

    if-eqz v2, :cond_4

    invoke-virtual {v1}, Lcom/huawei/hms/network/embedded/u8;->c()Ljava/net/Socket;

    move-result-object v0

    :cond_4
    return-object v0

    :cond_5
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method

.method public timeout()Lcom/huawei/hms/network/embedded/wb;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->e:Lcom/huawei/hms/network/embedded/va;

    return-object v0
.end method

.method public timeoutEarlyExit()V
    .locals 1

    iget-boolean v0, p0, Lcom/huawei/hms/network/embedded/z8;->n:Z

    if-nez v0, :cond_0

    const/4 v0, 0x1

    iput-boolean v0, p0, Lcom/huawei/hms/network/embedded/z8;->n:Z

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->e:Lcom/huawei/hms/network/embedded/va;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/va;->h()Z

    return-void

    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-direct {v0}, Ljava/lang/IllegalStateException;-><init>()V

    throw v0
.end method

.method public timeoutEnter()V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8;->e:Lcom/huawei/hms/network/embedded/va;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/va;->g()V

    return-void
.end method
