.class public final Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;
.super Ljava/lang/Object;
.source "MLRemoteProductVisionSearchAnalyzerSetting.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Factory"
.end annotation


# static fields
.field private static final h:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Integer;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private a:F

.field private b:I

.field private c:Z

.field private d:Ljava/lang/String;

.field private e:Z

.field private f:Ljava/lang/Integer;

.field private g:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->h:Ljava/util/Map;

    return-void
.end method

.method public constructor <init>()V
    .locals 3

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0x14

    .line 2
    iput v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->b:I

    const/4 v0, 0x1

    .line 8
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->e:Z

    const/16 v0, 0x3e9

    .line 12
    iput v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->g:I

    .line 16
    sget-object v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->h:Ljava/util/Map;

    const/16 v1, 0x3ea

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "CN"

    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x3eb

    .line 17
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "SG"

    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x3ef

    .line 18
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x3ec

    .line 19
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "DE"

    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x3ee

    .line 20
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/16 v1, 0x3ed

    .line 21
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "RU"

    invoke-interface {v0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method

.method static synthetic a()Ljava/util/Map;
    .locals 1

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->h:Ljava/util/Map;

    return-object v0
.end method


# virtual methods
.method public create()Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;
    .locals 10

    .line 1
    new-instance v9, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;

    iget v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->a:F

    iget v2, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->b:I

    iget-boolean v3, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->c:Z

    iget-object v4, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->d:Ljava/lang/String;

    iget-boolean v5, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->e:Z

    iget-object v6, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->f:Ljava/lang/Integer;

    iget v7, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->g:I

    const/4 v8, 0x0

    move-object v0, v9

    invoke-direct/range {v0 .. v8}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting;-><init>(FIZLjava/lang/String;ZLjava/lang/Integer;ILcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$a;)V

    return-object v9
.end method

.method public enableFingerprintVerification()Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;
    .locals 1

    const/4 v0, 0x1

    .line 1
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->c:Z

    return-object p0
.end method

.method public setBorderExtract(Z)Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->e:Z

    return-object p0
.end method

.method public setClassType(Ljava/lang/Integer;)Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->f:Ljava/lang/Integer;

    return-object p0
.end method

.method public setLargestNumOfReturns(I)Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;
    .locals 6

    const/16 v0, 0x64

    const/4 v1, 0x1

    if-lt p1, v1, :cond_0

    if-gt p1, v0, :cond_0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->b:I

    return-object p0

    .line 2
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    sget-object v2, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const/4 v3, 0x2

    new-array v3, v3, [Ljava/lang/Object;

    .line 3
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v4

    const/4 v5, 0x0

    aput-object v4, v3, v5

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    aput-object v0, v3, v1

    const-string v0, "maxResults must be between %d and %d"

    .line 4
    invoke-static {v2, v0, v3}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public setMinAcceptablePossibility(F)Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->a:F

    return-object p0
.end method

.method public setProductSetId(Ljava/lang/String;)Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->d:Ljava/lang/String;

    return-object p0
.end method

.method public setRegion(I)Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;
    .locals 2

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->h:Ljava/util/Map;

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 5
    iput p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzerSetting$Factory;->g:I

    return-object p0

    .line 6
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "region is not available"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
