.class public Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;
.super Ljava/lang/Object;
.source "RemoteFaceDetector.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector$Holder;
    }
.end annotation


# static fields
.field private static final DEFAULT_ERROE_CODE:I = -0x1

.field private static final TAG:Ljava/lang/String; = "RemoteFaceDetector"

.field private static volatile lock:Ljava/lang/Object;


# instance fields
.field private isInitialed:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ljava/lang/Object;

    invoke-direct {v0}, Ljava/lang/Object;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->lock:Ljava/lang/Object;

    return-void
.end method

.method private constructor <init>()V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 3
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->isInitialed:Z

    return-void
.end method

.method synthetic constructor <init>(Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector$1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;-><init>()V

    return-void
.end method

.method private expectedFace(Lcom/huawei/hms/ml/common/face/FaceParcel;Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)Z
    .locals 3

    .line 1
    invoke-virtual {p3}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getKeyPointType()I

    move-result v0

    if-nez v0, :cond_0

    .line 2
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p1, Lcom/huawei/hms/ml/common/face/FaceParcel;->landmarks:Ljava/util/List;

    .line 4
    :cond_0
    invoke-virtual {p3}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getShapeType()I

    move-result v0

    const/4 v1, 0x3

    if-ne v0, v1, :cond_1

    .line 5
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p1, Lcom/huawei/hms/ml/common/face/FaceParcel;->contours:Ljava/util/List;

    .line 7
    :cond_1
    invoke-virtual {p3}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getMinFaceProportion()F

    move-result p3

    const/4 v0, 0x0

    cmpl-float v1, p3, v0

    if-ltz v1, :cond_4

    const/high16 v1, 0x3f800000    # 1.0f

    cmpg-float v1, p3, v1

    if-gtz v1, :cond_4

    .line 9
    invoke-virtual {p2}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getQuadrant()I

    move-result v1

    if-eqz v1, :cond_4

    invoke-virtual {p2}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getQuadrant()I

    move-result v1

    const/4 v2, 0x2

    if-eq v1, v2, :cond_4

    .line 10
    invoke-virtual {p2}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object p2

    invoke-virtual {p2}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getHeight()I

    move-result p2

    int-to-float p2, p2

    .line 11
    iget p1, p1, Lcom/huawei/hms/ml/common/face/FaceParcel;->width:F

    cmpl-float v1, p1, v0

    const/4 v2, 0x0

    if-eqz v1, :cond_3

    cmpl-float v0, p2, v0

    if-nez v0, :cond_2

    goto :goto_0

    :cond_2
    div-float/2addr p1, p2

    cmpg-float p1, p1, p3

    if-gtz p1, :cond_4

    :cond_3
    :goto_0
    return v2

    :cond_4
    const/4 p1, 0x1

    return p1
.end method

.method public static getInstance()Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;
    .locals 1

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector$Holder;->INSTANCE:Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;

    return-object v0
.end method

.method private notifyDownloadIfNeeded(Landroid/content/Context;)V
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    .line 2
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/a;->a()Lcom/huawei/hms/mlsdk/mlvision/a;

    move-result-object v1

    .line 3
    invoke-virtual {v0, p1, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->notifyDownloadIfNeeded(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)V

    return-void
.end method


# virtual methods
.method public convertSetting(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;
    .locals 13

    .line 1
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getFeatureType()I

    move-result v0

    const/4 v1, 0x1

    const/4 v2, 0x2

    if-ne v0, v1, :cond_0

    move v6, v2

    goto :goto_0

    :cond_0
    if-ne v0, v2, :cond_1

    move v6, v1

    goto :goto_0

    :cond_1
    move v6, v0

    .line 8
    :goto_0
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getPerformanceType()I

    move-result v0

    if-ne v0, v2, :cond_2

    move v7, v1

    goto :goto_1

    :cond_2
    move v7, v2

    .line 10
    :goto_1
    new-instance v0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getKeyPointType()I

    move-result v4

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getShapeType()I

    move-result v5

    .line 11
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isPoseDisabled()Z

    move-result v8

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isTracingAllowed()Z

    move-result v9

    .line 12
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getTracingMode()I

    move-result v10

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isMaxSizeFaceOnly()Z

    move-result v11

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getMinFaceProportion()F

    move-result v12

    move-object v3, v0

    invoke-direct/range {v3 .. v12}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;-><init>(IIIIZZIZF)V

    return-object v0
.end method

.method public declared-synchronized destroy(Landroid/content/Context;)V
    .locals 3

    monitor-enter p0

    .line 1
    :try_start_0
    sget-object p1, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    const-string v0, "Destroy"

    invoke-static {p1, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/a;->a()Lcom/huawei/hms/mlsdk/mlvision/a;

    move-result-object v0

    .line 3
    invoke-interface {v0}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object v0

    if-nez v0, :cond_0

    const-string v0, "Destroy delegate is null"

    .line 6
    invoke-static {p1, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    .line 11
    :cond_0
    :try_start_1
    check-cast v0, Lcom/huawei/hms/ml/common/face/IRemoteFaceDetectorDelegate;

    invoke-interface {v0}, Lcom/huawei/hms/ml/common/face/IRemoteFaceDetectorDelegate;->destroy()I
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 13
    :try_start_2
    sget-object v0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Destroy Exception e: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :goto_0
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized detect(Landroid/content/Context;Landroid/os/Bundle;Lcom/huawei/hms/ml/common/face/FaceFrameParcel;Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            "Landroid/os/Bundle;",
            "Lcom/huawei/hms/ml/common/face/FaceFrameParcel;",
            "Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;",
            ")",
            "Ljava/util/List<",
            "Lcom/huawei/hms/ml/common/face/FaceParcel;",
            ">;"
        }
    .end annotation

    monitor-enter p0

    .line 1
    :try_start_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 2
    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->isInitialed:Z

    if-nez v1, :cond_0

    invoke-virtual {p0, p1, p4}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->initialize(Landroid/content/Context;Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;)I

    move-result p1

    if-ltz p1, :cond_0

    const/4 p1, 0x1

    .line 3
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->isInitialed:Z

    .line 5
    :cond_0
    iget-boolean p1, p0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->isInitialed:Z

    if-nez p1, :cond_1

    .line 6
    sget-object p1, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    const-string p2, "Detect Initialed is false"

    invoke-static {p1, p2}, Lcom/huawei/hms/ml/common/utils/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object v0

    .line 7
    :cond_1
    :try_start_1
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/a;->a()Lcom/huawei/hms/mlsdk/mlvision/a;

    move-result-object p1

    .line 8
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object p1

    if-nez p1, :cond_2

    .line 11
    sget-object p1, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    const-string p2, "Detect delegate is null"

    invoke-static {p1, p2}, Lcom/huawei/hms/ml/common/utils/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-object v0

    .line 15
    :cond_2
    :try_start_2
    check-cast p1, Lcom/huawei/hms/ml/common/face/IRemoteFaceDetectorDelegate;

    invoke-interface {p1, p2, p3, p4}, Lcom/huawei/hms/ml/common/face/IRemoteFaceDetectorDelegate;->detect(Landroid/os/Bundle;Lcom/huawei/hms/ml/common/face/FaceFrameParcel;Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;)Ljava/util/List;

    move-result-object p1
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    monitor-exit p0

    return-object p1

    :catch_0
    move-exception p1

    .line 17
    :try_start_3
    sget-object p2, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string p4, "Detect Exception e: "

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public detect3DFromRemote(Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;Lcom/huawei/hms/mlsdk/common/MLApplication;Ljava/lang/String;)Ljava/util/List;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            "Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;",
            "Lcom/huawei/hms/mlsdk/common/MLApplication;",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;",
            ">;"
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    const-string v1, "enter detect3DFromRemote"

    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 3
    new-instance v2, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;

    invoke-direct {v2}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;-><init>()V

    .line 7
    :try_start_0
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object v3

    if-eqz v3, :cond_0

    .line 8
    invoke-virtual {v3}, Ljava/nio/ByteBuffer;->remaining()I

    move-result v4

    if-eqz v4, :cond_0

    .line 9
    invoke-virtual {v3}, Ljava/nio/ByteBuffer;->remaining()I

    move-result v4

    new-array v5, v4, [B

    const/4 v6, 0x0

    .line 10
    invoke-virtual {v3, v5, v6, v4}, Ljava/nio/ByteBuffer;->get([BII)Ljava/nio/ByteBuffer;

    .line 11
    invoke-virtual {v2, v5}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setBytes([B)V

    const/16 v3, 0x11

    goto :goto_0

    .line 13
    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->readBitmap()Landroid/graphics/Bitmap;

    move-result-object v3

    .line 14
    invoke-virtual {v2, v3}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setBitmap(Landroid/graphics/Bitmap;)V

    const/16 v3, 0x75

    .line 17
    :goto_0
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v4

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getTimestamp()J

    move-result-wide v4

    invoke-virtual {v2, v4, v5}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setTimestampMillis(J)V

    .line 18
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v4

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getQuadrant()I

    move-result v4

    invoke-virtual {v2, v4}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setRotation(I)V

    .line 19
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v4

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getWidth()I

    move-result v4

    invoke-virtual {v2, v4}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setWidth(I)V

    .line 20
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v4

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getHeight()I

    move-result v4

    invoke-virtual {v2, v4}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setHeight(I)V

    .line 21
    invoke-virtual {v2, v3}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setFormat(I)V

    .line 22
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getItemIdentity()I

    move-result p1

    invoke-virtual {v2, p1}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setFrameId(I)V

    .line 23
    invoke-static {p2}, Lcom/huawei/hms/mlsdk/face/internal/client/FaceHelper;->create(Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;)Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;

    move-result-object p1

    .line 24
    invoke-virtual {p3}, Lcom/huawei/hms/mlsdk/common/MLApplication;->toBundle()Landroid/os/Bundle;

    move-result-object p2

    .line 27
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v3

    .line 28
    invoke-virtual {p3}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object p3

    invoke-virtual {p0, p3, p2, v2, p1}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->detect(Landroid/content/Context;Landroid/os/Bundle;Lcom/huawei/hms/ml/common/face/FaceFrameParcel;Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;)Ljava/util/List;

    move-result-object p1

    .line 30
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide p2

    .line 31
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "Total duration 06==="

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sub-long/2addr p2, v3

    invoke-virtual {v2, p2, p3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-static {v0, p2}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    if-nez p1, :cond_1

    const-string p1, "detect fail, :  faceParcelList is null"

    .line 33
    invoke-static {v0, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    return-object v1

    .line 36
    :cond_1
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_1
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result p2

    if-eqz p2, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p2

    check-cast p2, Lcom/huawei/hms/ml/common/face/FaceParcel;

    .line 37
    invoke-static {p2, p4}, Lcom/huawei/hms/mlsdk/face/internal/client/FaceHelper;->toML3DFace(Lcom/huawei/hms/ml/common/face/FaceParcel;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;

    move-result-object p2

    invoke-virtual {v1, p2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z
    :try_end_0
    .catch Ljava/lang/RuntimeException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception p1

    .line 42
    sget-object p2, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string p4, "detect exception e:  "

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_2

    :catch_1
    move-exception p1

    .line 43
    sget-object p2, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string p4, "detect exception e: "

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/RuntimeException;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    :cond_2
    :goto_2
    return-object v1
.end method

.method public detectFromRemote(Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;Lcom/huawei/hms/mlsdk/common/MLApplication;)Ljava/util/List;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            "Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;",
            "Lcom/huawei/hms/mlsdk/common/MLApplication;",
            ")",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/face/MLFace;",
            ">;"
        }
    .end annotation

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    const-string v1, "enter detect2DFromRemote"

    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 3
    new-instance v2, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;

    invoke-direct {v2}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;-><init>()V

    .line 7
    :try_start_0
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object v3

    if-eqz v3, :cond_0

    .line 8
    invoke-virtual {v3}, Ljava/nio/ByteBuffer;->remaining()I

    move-result v4

    if-eqz v4, :cond_0

    .line 9
    invoke-virtual {v3}, Ljava/nio/ByteBuffer;->remaining()I

    move-result v4

    new-array v5, v4, [B

    const/4 v6, 0x0

    .line 10
    invoke-virtual {v3, v5, v6, v4}, Ljava/nio/ByteBuffer;->get([BII)Ljava/nio/ByteBuffer;

    .line 11
    invoke-virtual {v2, v5}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setBytes([B)V

    const/16 v3, 0x11

    goto :goto_0

    .line 13
    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->readBitmap()Landroid/graphics/Bitmap;

    move-result-object v3

    .line 14
    invoke-virtual {v2, v3}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setBitmap(Landroid/graphics/Bitmap;)V

    const/16 v3, 0x75

    .line 17
    :goto_0
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v4

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getTimestamp()J

    move-result-wide v4

    invoke-virtual {v2, v4, v5}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setTimestampMillis(J)V

    .line 18
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v4

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getQuadrant()I

    move-result v4

    invoke-virtual {v2, v4}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setRotation(I)V

    .line 19
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v4

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getWidth()I

    move-result v4

    invoke-virtual {v2, v4}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setWidth(I)V

    .line 20
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v4

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getHeight()I

    move-result v4

    invoke-virtual {v2, v4}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setHeight(I)V

    .line 21
    invoke-virtual {v2, v3}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setFormat(I)V

    .line 22
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object v3

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getItemIdentity()I

    move-result v3

    invoke-virtual {v2, v3}, Lcom/huawei/hms/ml/common/face/FaceFrameParcel;->setFrameId(I)V

    .line 23
    invoke-virtual {p0, p2}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->convertSetting(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    move-result-object v3

    invoke-static {v3}, Lcom/huawei/hms/mlsdk/face/internal/client/FaceHelper;->create(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;

    move-result-object v3

    .line 24
    invoke-virtual {p3}, Lcom/huawei/hms/mlsdk/common/MLApplication;->toBundle()Landroid/os/Bundle;

    move-result-object v4

    .line 25
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v5

    .line 26
    invoke-virtual {p3}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object p3

    invoke-virtual {p0, p3, v4, v2, v3}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->detect(Landroid/content/Context;Landroid/os/Bundle;Lcom/huawei/hms/ml/common/face/FaceFrameParcel;Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;)Ljava/util/List;

    move-result-object p3

    .line 28
    invoke-static {}, Landroid/os/SystemClock;->elapsedRealtime()J

    move-result-wide v2

    .line 29
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "Total duration 06==="

    invoke-virtual {v4, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sub-long/2addr v2, v5

    invoke-virtual {v4, v2, v3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v0, v2}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    if-nez p3, :cond_1

    const-string p1, "detect fail, :  faceParcelList is null"

    .line 31
    invoke-static {v0, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    return-object v1

    .line 34
    :cond_1
    invoke-interface {p3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p3

    :cond_2
    :goto_1
    invoke-interface {p3}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_3

    invoke-interface {p3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/huawei/hms/ml/common/face/FaceParcel;

    .line 35
    invoke-direct {p0, v0, p1, p2}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->expectedFace(Lcom/huawei/hms/ml/common/face/FaceParcel;Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)Z

    move-result v2

    if-eqz v2, :cond_2

    .line 36
    invoke-static {v0}, Lcom/huawei/hms/mlsdk/face/internal/client/FaceHelper;->toMLFace(Lcom/huawei/hms/ml/common/face/FaceParcel;)Lcom/huawei/hms/mlsdk/face/MLFace;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_1

    .line 40
    :cond_3
    invoke-virtual {p2}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isMaxSizeFaceOnly()Z

    move-result p1

    if-eqz p1, :cond_6

    invoke-virtual {v1}, Ljava/util/ArrayList;->isEmpty()Z

    move-result p1

    if-nez p1, :cond_6

    const/4 p1, 0x0

    .line 42
    invoke-virtual {v1}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object p2

    :goto_2
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    move-result p3

    if-eqz p3, :cond_5

    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object p3

    check-cast p3, Lcom/huawei/hms/mlsdk/face/MLFace;

    if-eqz p1, :cond_4

    .line 43
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/face/MLFace;->getWidth()F

    move-result v0

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/face/MLFace;->getHeight()F

    move-result v2

    mul-float/2addr v0, v2

    .line 44
    invoke-virtual {p3}, Lcom/huawei/hms/mlsdk/face/MLFace;->getWidth()F

    move-result v2

    invoke-virtual {p3}, Lcom/huawei/hms/mlsdk/face/MLFace;->getHeight()F

    move-result v3

    mul-float/2addr v2, v3

    cmpl-float v0, v0, v2

    if-lez v0, :cond_4

    goto :goto_2

    :cond_4
    move-object p1, p3

    goto :goto_2

    .line 49
    :cond_5
    new-instance p2, Ljava/util/ArrayList;

    invoke-direct {p2}, Ljava/util/ArrayList;-><init>()V
    :try_end_0
    .catch Ljava/lang/RuntimeException; {:try_start_0 .. :try_end_0} :catch_3
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_2

    .line 50
    :try_start_1
    invoke-virtual {p2, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z
    :try_end_1
    .catch Ljava/lang/RuntimeException; {:try_start_1 .. :try_end_1} :catch_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    move-object v1, p2

    goto :goto_5

    :catch_0
    move-exception p1

    move-object v1, p2

    goto :goto_3

    :catch_1
    move-exception p1

    move-object v1, p2

    goto :goto_4

    :catch_2
    move-exception p1

    .line 55
    :goto_3
    sget-object p2, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "detect exception e:  "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_5

    :catch_3
    move-exception p1

    .line 56
    :goto_4
    sget-object p2, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "detect exception e: "

    invoke-virtual {p3, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/RuntimeException;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    :cond_6
    :goto_5
    return-object v1
.end method

.method public declared-synchronized initialize(Landroid/content/Context;Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;)I
    .locals 3

    monitor-enter p0

    .line 1
    :try_start_0
    sget-object p1, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    const-string v0, "enter initialize"

    invoke-static {p1, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->isInitialed:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    const/4 p1, 0x0

    monitor-exit p0

    return p1

    .line 3
    :cond_0
    :try_start_1
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/a;->a()Lcom/huawei/hms/mlsdk/mlvision/a;

    move-result-object v0

    .line 4
    invoke-interface {v0}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object v1

    const/4 v2, -0x1

    if-nez v1, :cond_1

    const-string p2, "initialize delegate is null"

    .line 7
    invoke-static {p1, p2}, Lcom/huawei/hms/ml/common/utils/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return v2

    .line 11
    :cond_1
    :try_start_2
    invoke-interface {v0}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicContext()Landroid/content/Context;

    move-result-object v0

    .line 12
    check-cast v1, Lcom/huawei/hms/ml/common/face/IRemoteFaceDetectorDelegate;

    .line 13
    invoke-static {v0}, Lcom/huawei/hms/feature/dynamic/ObjectWrapper;->wrap(Ljava/lang/Object;)Lcom/huawei/hms/feature/dynamic/IObjectWrapper;

    move-result-object v0

    .line 14
    invoke-interface {v1, v0, p2}, Lcom/huawei/hms/ml/common/face/IRemoteFaceDetectorDelegate;->initialize(Lcom/huawei/hms/feature/dynamic/IObjectWrapper;Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;)I

    move-result p2

    if-ltz p2, :cond_2

    const/4 v0, 0x1

    .line 17
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->isInitialed:Z

    const-string v0, "initialize success"

    .line 18
    invoke-static {p1, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_2
    const-string v0, "initialize failure"

    .line 20
    invoke-static {p1, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :goto_0
    monitor-exit p0

    return p2

    :catch_0
    move-exception p1

    .line 24
    :try_start_3
    sget-object p2, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Initialize Exception e: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    monitor-exit p0

    return v2

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public isAvailable(Landroid/content/Context;)Z
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    .line 2
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/a;->a()Lcom/huawei/hms/mlsdk/mlvision/a;

    move-result-object v1

    .line 3
    invoke-virtual {v0, p1, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->isAvailable(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)Z

    move-result p1

    return p1
.end method

.method public declared-synchronized prepare(Landroid/content/Context;)V
    .locals 1

    monitor-enter p0

    .line 1
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/a;->a()Lcom/huawei/hms/mlsdk/mlvision/a;

    move-result-object v0

    .line 2
    invoke-interface {v0, p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->initial(Landroid/content/Context;)V

    .line 3
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->notifyDownloadIfNeeded(Landroid/content/Context;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized release(Landroid/content/Context;)V
    .locals 2

    monitor-enter p0

    .line 1
    :try_start_0
    sget-object v0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->TAG:Ljava/lang/String;

    const-string v1, "release"

    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->i(Ljava/lang/String;Ljava/lang/String;)V

    .line 2
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->isInitialed:Z

    if-eqz v0, :cond_0

    .line 3
    invoke-virtual {p0, p1}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->destroy(Landroid/content/Context;)V

    const/4 v0, 0x0

    .line 4
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->isInitialed:Z

    .line 6
    :cond_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->release(Landroid/content/Context;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public setFocus(I)Z
    .locals 0

    const/4 p1, 0x1

    return p1
.end method
