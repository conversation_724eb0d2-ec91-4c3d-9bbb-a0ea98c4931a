.class public interface abstract Lcom/huawei/hms/network/embedded/v7$a;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/network/embedded/v7;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(Lcom/huawei/hms/network/embedded/p7;Lcom/huawei/hms/network/embedded/w7;)Lcom/huawei/hms/network/embedded/v7;
.end method
