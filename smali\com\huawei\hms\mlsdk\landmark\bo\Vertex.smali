.class public Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;
.super Ljava/lang/Object;
.source "Vertex.java"


# instance fields
.field private x:I

.field private y:I


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getX()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;->x:I

    return v0
.end method

.method public getY()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;->y:I

    return v0
.end method

.method public setX(I)V
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;->x:I

    return-void
.end method

.method public setY(I)V
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;->y:I

    return-void
.end method
