.class final Lcom/huawei/hms/mlsdk/mlvision/e$b;
.super Ljava/lang/Object;
.source "RemoteTextInitializer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/mlvision/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "b"
.end annotation


# static fields
.field static a:Lcom/huawei/hms/mlsdk/mlvision/e;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/mlvision/e;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/huawei/hms/mlsdk/mlvision/e;-><init>(Lcom/huawei/hms/mlsdk/mlvision/e$a;)V

    sput-object v0, Lcom/huawei/hms/mlsdk/mlvision/e$b;->a:Lcom/huawei/hms/mlsdk/mlvision/e;

    return-void
.end method
