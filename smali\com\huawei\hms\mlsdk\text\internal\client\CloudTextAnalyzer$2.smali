.class Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$2;
.super Lcom/google/gson/reflect/TypeToken;
.source "CloudTextAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->packageParams(Ljava/lang/String;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;)Ljava/lang/String;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/google/gson/reflect/TypeToken<",
        "Ljava/util/ArrayList<",
        "Ljava/lang/String;",
        ">;>;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;


# direct methods
.method constructor <init>(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$2;->this$0:Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    invoke-direct {p0}, Lcom/google/gson/reflect/TypeToken;-><init>()V

    return-void
.end method
