.class public Lcom/huawei/hms/mlsdk/internal/client/rest/e;
.super Ljava/lang/Object;
.source "RestClientRequestHeaders.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;
    }
.end annotation


# instance fields
.field private a:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method synthetic constructor <init>(Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;Lcom/huawei/hms/mlsdk/internal/client/rest/e$a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 p2, 0x0

    .line 2
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/e;->a:Ljava/util/Map;

    .line 9
    invoke-static {p1}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;)Ljava/util/Map;

    move-result-object p1

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/e;->a:Ljava/util/Map;

    return-void
.end method


# virtual methods
.method public a()Ljava/util/Map;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/e;->a:Ljava/util/Map;

    return-object v0
.end method
