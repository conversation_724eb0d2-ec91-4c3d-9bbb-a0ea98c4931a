.class public Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;
.super Ljava/lang/Object;
.source "MLLocalTextSetting.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;
    }
.end annotation


# static fields
.field public static final OCR_DETECT_MODE:I = 0x1

.field public static final OCR_TRACKING_MODE:I = 0x2


# instance fields
.field private final language:Ljava/lang/String;

.field private final ocrMode:I

.field private final plateEnable:Z


# direct methods
.method private constructor <init>(Ljava/lang/String;IZ)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->language:Ljava/lang/String;

    .line 4
    iput p2, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->ocrMode:I

    .line 5
    iput-boolean p3, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->plateEnable:Z

    return-void
.end method

.method synthetic constructor <init>(Ljava/lang/String;IZLcom/huawei/hms/mlsdk/text/MLLocalTextSetting$1;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;-><init>(Ljava/lang/String;IZ)V

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p1, p0, :cond_0

    return v0

    .line 1
    :cond_0
    instance-of v1, p1, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    .line 4
    :cond_1
    check-cast p1, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    .line 5
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->language:Ljava/lang/String;

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->getLanguage()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget v1, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->ocrMode:I

    iget v3, p1, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->ocrMode:I

    if-ne v1, v3, :cond_2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->plateEnable:Z

    iget-boolean p1, p1, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->plateEnable:Z

    if-ne v1, p1, :cond_2

    goto :goto_0

    :cond_2
    move v0, v2

    :goto_0
    return v0
.end method

.method public getLanguage()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->language:Ljava/lang/String;

    return-object v0
.end method

.method public getOCRMode()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->ocrMode:I

    return v0
.end method

.method public getPlateEnable()I
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->plateEnable:Z

    return v0
.end method

.method public hashCode()I
    .locals 3

    const/4 v0, 0x3

    new-array v0, v0, [Ljava/lang/Object;

    .line 1
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->language:Ljava/lang/String;

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget v1, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->ocrMode:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->plateEnable:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    invoke-static {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->hashCode([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method
