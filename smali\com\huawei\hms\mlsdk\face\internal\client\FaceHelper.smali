.class public Lcom/huawei/hms/mlsdk/face/internal/client/FaceHelper;
.super Ljava/lang/Object;
.source "FaceHelper.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static create(Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;)Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;
    .locals 2

    .line 1
    new-instance v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;

    invoke-direct {v0}, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;-><init>()V

    if-eqz p0, :cond_0

    .line 3
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getKeyPointType()I

    move-result v1

    iput v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->landmarkMode:I

    .line 4
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getShapeType()I

    move-result v1

    iput v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->contourMode:I

    .line 5
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getFeatureType()I

    move-result v1

    iput v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->classificationMode:I

    .line 6
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getPerformanceType()I

    move-result v1

    iput v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->performanceMode:I

    const/4 v1, 0x0

    .line 7
    iput-boolean v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->face3dEnabled:Z

    .line 8
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isPoseDisabled()Z

    move-result v1

    iput-boolean v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->poseDisabled:Z

    .line 9
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isTracingAllowed()Z

    move-result v1

    iput-boolean v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->trackingEnabled:Z

    .line 10
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getTracingMode()I

    move-result v1

    iput v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->trackingMode:I

    .line 11
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->isMaxSizeFaceOnly()Z

    move-result v1

    iput-boolean v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->bProminentFaceOnly:Z

    .line 12
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;->getMinFaceProportion()F

    move-result p0

    iput p0, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->minFaceSize:F

    :cond_0
    return-object v0
.end method

.method public static create(Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;)Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;
    .locals 3

    .line 13
    new-instance v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;

    invoke-direct {v0}, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;-><init>()V

    if-eqz p0, :cond_0

    const/4 v1, 0x0

    .line 15
    iput v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->landmarkMode:I

    const/4 v1, 0x3

    .line 16
    iput v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->contourMode:I

    const/4 v1, 0x1

    .line 17
    iput v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->classificationMode:I

    .line 18
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->getPerformanceType()I

    move-result v2

    iput v2, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->performanceMode:I

    .line 19
    iput-boolean v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->face3dEnabled:Z

    .line 20
    iput-boolean v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->poseDisabled:Z

    .line 21
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFaceAnalyzerSetting;->isTracingAllowed()Z

    move-result p0

    iput-boolean p0, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->trackingEnabled:Z

    .line 22
    iput v1, v0, Lcom/huawei/hms/ml/common/face/FaceDetectorOptionsParcel;->trackingMode:I

    :cond_0
    return-object v0
.end method

.method public static toML3DFace(Lcom/huawei/hms/ml/common/face/FaceParcel;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;
    .locals 13

    .line 1
    new-instance v8, Ljava/util/ArrayList;

    invoke-direct {v8}, Ljava/util/ArrayList;-><init>()V

    .line 2
    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    .line 3
    iget-object v0, p0, Lcom/huawei/hms/ml/common/face/FaceParcel;->face3DParams:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    mul-int/lit8 v0, v0, 0x2

    new-array v10, v0, [F

    .line 5
    iget-object v0, p0, Lcom/huawei/hms/ml/common/face/FaceParcel;->face3DVertices:Ljava/util/List;

    if-eqz v0, :cond_2

    .line 6
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/ml/common/face/Point3dF;

    .line 7
    new-instance v2, Lcom/huawei/hms/mlsdk/common/MLPosition;

    iget v3, v1, Lcom/huawei/hms/ml/common/face/Point3dF;->x:F

    invoke-static {v3}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v3

    iget v4, v1, Lcom/huawei/hms/ml/common/face/Point3dF;->y:F

    invoke-static {v4}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v4

    iget v1, v1, Lcom/huawei/hms/ml/common/face/Point3dF;->z:F

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    invoke-direct {v2, v3, v4, v1}, Lcom/huawei/hms/mlsdk/common/MLPosition;-><init>(Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/Float;)V

    invoke-interface {v8, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 9
    :cond_0
    iget-object v0, p0, Lcom/huawei/hms/ml/common/face/FaceParcel;->face3DParams:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v1, 0x0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/graphics/PointF;

    add-int/lit8 v3, v1, 0x1

    .line 10
    iget v4, v2, Landroid/graphics/PointF;->x:F

    aput v4, v10, v1

    add-int/lit8 v1, v3, 0x1

    .line 11
    iget v2, v2, Landroid/graphics/PointF;->y:F

    aput v2, v10, v3

    goto :goto_1

    .line 13
    :cond_1
    iget-object v0, p0, Lcom/huawei/hms/ml/common/face/FaceParcel;->face3DKeypoints:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/ml/common/face/Point3dF;

    .line 14
    new-instance v2, Lcom/huawei/hms/mlsdk/common/MLPosition;

    iget v3, v1, Lcom/huawei/hms/ml/common/face/Point3dF;->x:F

    invoke-static {v3}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v3

    iget v4, v1, Lcom/huawei/hms/ml/common/face/Point3dF;->y:F

    invoke-static {v4}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v4

    iget v1, v1, Lcom/huawei/hms/ml/common/face/Point3dF;->z:F

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    invoke-direct {v2, v3, v4, v1}, Lcom/huawei/hms/mlsdk/common/MLPosition;-><init>(Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/Float;)V

    invoke-interface {v9, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 18
    :cond_2
    new-instance v12, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;

    iget v1, p0, Lcom/huawei/hms/ml/common/face/FaceParcel;->mId:I

    iget-object v2, p0, Lcom/huawei/hms/ml/common/face/FaceParcel;->position:Landroid/graphics/PointF;

    iget v3, p0, Lcom/huawei/hms/ml/common/face/FaceParcel;->width:F

    iget v4, p0, Lcom/huawei/hms/ml/common/face/FaceParcel;->height:F

    iget v5, p0, Lcom/huawei/hms/ml/common/face/FaceParcel;->face3DEulerAngleX:F

    iget v6, p0, Lcom/huawei/hms/ml/common/face/FaceParcel;->face3DEulerAngleY:F

    iget v7, p0, Lcom/huawei/hms/ml/common/face/FaceParcel;->face3DEulerAngleZ:F

    move-object v0, v12

    move-object v11, p1

    invoke-direct/range {v0 .. v11}, Lcom/huawei/hms/mlsdk/face/face3d/ML3DFace;-><init>(ILandroid/graphics/PointF;FFFFFLjava/util/List;Ljava/util/List;[FLjava/lang/String;)V

    return-object v12
.end method

.method public static toMLFace(Lcom/huawei/hms/ml/common/face/FaceParcel;)Lcom/huawei/hms/mlsdk/face/MLFace;
    .locals 24

    move-object/from16 v1, p0

    .line 1
    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    .line 2
    iget-object v0, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->landmarks:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/ml/common/face/LandmarkParcel;

    if-eqz v2, :cond_0

    .line 4
    invoke-static {v2}, Lcom/huawei/hms/mlsdk/face/internal/client/FaceHelper;->toMLFaceKeyPoint(Lcom/huawei/hms/ml/common/face/LandmarkParcel;)Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;

    move-result-object v2

    invoke-interface {v9, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 7
    :cond_1
    new-instance v10, Ljava/util/ArrayList;

    invoke-direct {v10}, Ljava/util/ArrayList;-><init>()V

    .line 9
    :try_start_0
    iget-object v0, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->contours:Ljava/util/List;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/ml/common/face/FaceContourParcel;

    .line 10
    invoke-static {v2}, Lcom/huawei/hms/mlsdk/face/internal/client/FaceHelper;->toMLFaceShape(Lcom/huawei/hms/ml/common/face/FaceContourParcel;)Lcom/huawei/hms/mlsdk/face/MLFaceShape;

    move-result-object v2

    invoke-interface {v10, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z
    :try_end_0
    .catch Ljava/lang/RuntimeException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    .line 15
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "failed to translate Parcel to MLFaceShape:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v2, "FaceHelper"

    invoke-static {v2, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    .line 18
    :cond_2
    new-instance v13, Ljava/util/ArrayList;

    invoke-direct {v13}, Ljava/util/ArrayList;-><init>()V

    .line 19
    iget-object v0, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->allPoints:Ljava/util/List;

    if-eqz v0, :cond_3

    .line 20
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Landroid/graphics/PointF;

    .line 21
    new-instance v3, Lcom/huawei/hms/mlsdk/common/MLPosition;

    iget v4, v2, Landroid/graphics/PointF;->x:F

    invoke-static {v4}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v4

    iget v2, v2, Landroid/graphics/PointF;->y:F

    invoke-static {v2}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v2

    invoke-direct {v3, v4, v2}, Lcom/huawei/hms/mlsdk/common/MLPosition;-><init>(Ljava/lang/Float;Ljava/lang/Float;)V

    invoke-interface {v13, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_2

    .line 25
    :cond_3
    new-instance v11, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;

    iget v15, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->smilingProbability:F

    iget v0, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->neutralProbability:F

    iget v2, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->angryProbability:F

    iget v3, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->disgustProbability:F

    iget v4, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->fearProbability:F

    iget v5, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->sadProbability:F

    iget v6, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->surpriseProbability:F

    move-object v14, v11

    move/from16 v16, v0

    move/from16 v17, v2

    move/from16 v18, v3

    move/from16 v19, v4

    move/from16 v20, v5

    move/from16 v21, v6

    invoke-direct/range {v14 .. v21}, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;-><init>(FFFFFFF)V

    .line 30
    new-instance v12, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;

    iget v0, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->leftEyeOpenProbability:F

    iget v2, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->rightEyeOpenProbability:F

    iget v3, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->sunGlassProbability:F

    iget v4, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->sexProbability:F

    iget v5, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->hatProbability:F

    iget v6, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->moustacheProbability:F

    iget v7, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->age:I

    move-object/from16 v16, v12

    move/from16 v17, v0

    move/from16 v18, v2

    move/from16 v19, v3

    move/from16 v20, v4

    move/from16 v21, v5

    move/from16 v22, v6

    move/from16 v23, v7

    invoke-direct/range {v16 .. v23}, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;-><init>(FFFFFFI)V

    .line 35
    new-instance v0, Lcom/huawei/hms/mlsdk/face/MLFace;

    iget v2, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->mId:I

    iget-object v3, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->position:Landroid/graphics/PointF;

    iget v4, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->width:F

    iget v5, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->height:F

    iget v6, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->headEulerAngleX:F

    iget v7, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->headEulerAngleY:F

    iget v8, v1, Lcom/huawei/hms/ml/common/face/FaceParcel;->headEulerAngleZ:F

    move-object v1, v0

    invoke-direct/range {v1 .. v13}, Lcom/huawei/hms/mlsdk/face/MLFace;-><init>(ILandroid/graphics/PointF;FFFFFLjava/util/List;Ljava/util/List;Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;Lcom/huawei/hms/mlsdk/face/MLFaceFeature;Ljava/util/List;)V

    return-object v0

    :catch_1
    move-exception v0

    .line 36
    throw v0
.end method

.method static toMLFaceKeyPoint(Lcom/huawei/hms/ml/common/face/LandmarkParcel;)Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;
    .locals 2

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;

    iget-object v1, p0, Lcom/huawei/hms/ml/common/face/LandmarkParcel;->point:Lcom/huawei/hms/ml/common/face/MLVisionPointParcel;

    invoke-static {v1}, Lcom/huawei/hms/mlsdk/face/internal/client/FaceHelper;->toMLPoint(Lcom/huawei/hms/ml/common/face/MLVisionPointParcel;)Lcom/huawei/hms/mlsdk/common/MLPosition;

    move-result-object v1

    iget p0, p0, Lcom/huawei/hms/ml/common/face/LandmarkParcel;->landmarkType:I

    invoke-direct {v0, v1, p0}, Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;-><init>(Lcom/huawei/hms/mlsdk/common/MLPosition;I)V

    return-object v0
.end method

.method static toMLFaceShape(Lcom/huawei/hms/ml/common/face/FaceContourParcel;)Lcom/huawei/hms/mlsdk/face/MLFaceShape;
    .locals 3

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 2
    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/face/FaceContourParcel;->getPoints()Ljava/util/List;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/ml/common/face/MLVisionPointParcel;

    .line 3
    invoke-static {v2}, Lcom/huawei/hms/mlsdk/face/internal/client/FaceHelper;->toMLPoint(Lcom/huawei/hms/ml/common/face/MLVisionPointParcel;)Lcom/huawei/hms/mlsdk/common/MLPosition;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    .line 5
    :cond_0
    new-instance v1, Lcom/huawei/hms/mlsdk/face/MLFaceShape;

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/face/FaceContourParcel;->getFaceContourType()I

    move-result p0

    invoke-direct {v1, p0, v0}, Lcom/huawei/hms/mlsdk/face/MLFaceShape;-><init>(ILjava/util/List;)V

    return-object v1
.end method

.method static toMLPoint(Lcom/huawei/hms/ml/common/face/MLVisionPointParcel;)Lcom/huawei/hms/mlsdk/common/MLPosition;
    .locals 3

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/common/MLPosition;

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/face/MLVisionPointParcel;->getX()Ljava/lang/Float;

    move-result-object v1

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/face/MLVisionPointParcel;->getY()Ljava/lang/Float;

    move-result-object v2

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/face/MLVisionPointParcel;->getY()Ljava/lang/Float;

    move-result-object p0

    invoke-direct {v0, v1, v2, p0}, Lcom/huawei/hms/mlsdk/common/MLPosition;-><init>(Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/Float;)V

    return-object v0
.end method
