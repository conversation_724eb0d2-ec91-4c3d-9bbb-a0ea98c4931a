.class public interface abstract Lcom/huawei/hms/mlsdk/text/entity/textplate/IText;
.super Ljava/lang/Object;
.source "IText.java"

# interfaces
.implements Lcom/huawei/hms/mlsdk/text/IMLPlate;


# virtual methods
.method public abstract getContents()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "+",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/IText;",
            ">;"
        }
    .end annotation
.end method

.method public abstract getLanguage()Ljava/lang/String;
.end method

.method public abstract getLanguageList()Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/TextLanguage;",
            ">;"
        }
    .end annotation
.end method

.method public abstract getStringValue()Ljava/lang/String;
.end method
