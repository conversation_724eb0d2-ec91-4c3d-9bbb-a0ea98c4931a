.class public final Lcom/huawei/hms/network/embedded/w9$a;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/network/embedded/w9;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:[Lcom/huawei/hms/network/embedded/w9$a;

.field public final b:I

.field public final c:I


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0x100

    new-array v0, v0, [Lcom/huawei/hms/network/embedded/w9$a;

    iput-object v0, p0, Lcom/huawei/hms/network/embedded/w9$a;->a:[Lcom/huawei/hms/network/embedded/w9$a;

    const/4 v0, 0x0

    iput v0, p0, Lcom/huawei/hms/network/embedded/w9$a;->b:I

    iput v0, p0, Lcom/huawei/hms/network/embedded/w9$a;->c:I

    return-void
.end method

.method public constructor <init>(II)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/huawei/hms/network/embedded/w9$a;->a:[Lcom/huawei/hms/network/embedded/w9$a;

    iput p1, p0, Lcom/huawei/hms/network/embedded/w9$a;->b:I

    and-int/lit8 p1, p2, 0x7

    if-nez p1, :cond_0

    const/16 p1, 0x8

    :cond_0
    iput p1, p0, Lcom/huawei/hms/network/embedded/w9$a;->c:I

    return-void
.end method
