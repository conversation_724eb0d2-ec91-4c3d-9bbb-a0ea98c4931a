.class public Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;
.super Ljava/lang/Object;
.source "MLRemoteLandmarkAnalyzer.java"

# interfaces
.implements Ljava/io/Closeable;


# static fields
.field private static final TAG:Ljava/lang/String;

.field private static appSettingAnalyzerMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lcom/huawei/hms/mlsdk/common/AppSettingHolder<",
            "Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;",
            ">;",
            "Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private app:Lcom/huawei/hms/mlsdk/common/MLApplication;

.field private currentCall:Lcom/huawei/hms/network/httpclient/Submit;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/huawei/hms/network/httpclient/Submit<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field private setting:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->appSettingAnalyzerMap:Ljava/util/Map;

    const-string v0, "MLRemoteLandmarkAnalyzer"

    .line 3
    sput-object v0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->TAG:Ljava/lang/String;

    return-void
.end method

.method private constructor <init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    .line 3
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->setting:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

    return-void
.end method

.method static synthetic access$000(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;)Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->setting:Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

    return-object p0
.end method

.method static synthetic access$100(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    return-object p0
.end method

.method static synthetic access$200()Ljava/lang/String;
    .locals 1

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->TAG:Ljava/lang/String;

    return-object v0
.end method

.method static synthetic access$300(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Ljava/util/Map;)Z
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->isHeaderInvalidate(Ljava/util/Map;)Z

    move-result p0

    return p0
.end method

.method static synthetic access$400(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Landroid/graphics/Bitmap;)D
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->calculateScale(Landroid/graphics/Bitmap;)D

    move-result-wide p0

    return-wide p0
.end method

.method static synthetic access$500(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Landroid/graphics/Bitmap;D)Landroid/graphics/Bitmap;
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->resizeImage(Landroid/graphics/Bitmap;D)Landroid/graphics/Bitmap;

    move-result-object p0

    return-object p0
.end method

.method static synthetic access$600(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Landroid/graphics/Bitmap;)Ljava/lang/String;
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->base64Img(Landroid/graphics/Bitmap;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method static synthetic access$700(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Ljava/lang/String;I)Ljava/lang/String;
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->packageParamJson(Ljava/lang/String;I)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method static synthetic access$800(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Lcom/huawei/hms/network/httpclient/Response;D)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2, p3}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->handleResult(Lcom/huawei/hms/network/httpclient/Response;D)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method

.method private base64Img(Landroid/graphics/Bitmap;)Ljava/lang/String;
    .locals 1

    const/16 v0, 0x64

    .line 1
    invoke-static {p1, v0}, Lcom/huawei/hms/ml/common/utils/ImageConvertUtils;->bitmap2Jpeg(Landroid/graphics/Bitmap;I)[B

    move-result-object p1

    const/4 v0, 0x2

    .line 2
    invoke-static {p1, v0}, Landroid/util/Base64;->encodeToString([BI)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method private calculateScale(Landroid/graphics/Bitmap;)D
    .locals 2

    .line 1
    invoke-virtual {p1}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v0

    .line 2
    invoke-virtual {p1}, Landroid/graphics/Bitmap;->getHeight()I

    move-result p1

    .line 3
    invoke-static {v0, p1}, Ljava/lang/Math;->min(II)I

    move-result p1

    int-to-float p1, p1

    const/high16 v0, 0x3f800000    # 1.0f

    mul-float/2addr p1, v0

    const/high16 v1, 0x44200000    # 640.0f

    div-float/2addr p1, v1

    cmpg-float v1, p1, v0

    if-gez v1, :cond_0

    goto :goto_0

    :cond_0
    move v0, p1

    :goto_0
    float-to-double v0, v0

    return-wide v0
.end method

.method public static declared-synchronized create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;)Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;
    .locals 3

    const-class v0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    monitor-enter v0

    .line 1
    :try_start_0
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getUniqueKey()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, p1}, Lcom/huawei/hms/mlsdk/common/AppSettingHolder;->create(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/common/AppSettingHolder;

    move-result-object v1

    .line 3
    sget-object v2, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->appSettingAnalyzerMap:Ljava/util/Map;

    invoke-interface {v2, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    if-nez v2, :cond_0

    .line 4
    new-instance v2, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;

    invoke-direct {v2, p0, p1}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;)V

    .line 5
    sget-object p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->appSettingAnalyzerMap:Ljava/util/Map;

    invoke-interface {p0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_0
    monitor-exit v0

    return-object v2

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method

.method private handleResult(Lcom/huawei/hms/network/httpclient/Response;D)Ljava/util/List;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/network/httpclient/Response<",
            "Ljava/lang/String;",
            ">;D)",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 3
    invoke-virtual {p1}, Lcom/huawei/hms/network/httpclient/Response;->isSuccessful()Z

    move-result v1

    const/4 v2, 0x2

    if-eqz v1, :cond_d

    .line 7
    invoke-virtual {p1}, Lcom/huawei/hms/network/httpclient/Response;->getBody()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    .line 10
    new-instance v1, Lcom/google/gson/Gson;

    invoke-direct {v1}, Lcom/google/gson/Gson;-><init>()V

    const-class v3, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;

    invoke-virtual {v1, p1, v3}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;

    if-eqz p1, :cond_c

    .line 18
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->getRetCode()Ljava/lang/String;

    move-result-object v1

    const-string v3, "0"

    invoke-virtual {v3, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_5

    .line 19
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->getLandMarkResults()Ljava/util/List;

    move-result-object p1

    .line 41
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;

    .line 42
    new-instance v2, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;

    invoke-direct {v2}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;-><init>()V

    const-string v3, ""

    .line 44
    invoke-virtual {v2, v3}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->setLandmarkId(Ljava/lang/String;)V

    .line 46
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->getDescription()Ljava/lang/String;

    move-result-object v3

    if-nez v3, :cond_0

    const-string v3, "Unknown"

    goto :goto_1

    :cond_0
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->getDescription()Ljava/lang/String;

    move-result-object v3

    .line 47
    :goto_1
    invoke-virtual {v2, v3}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->setLandmark(Ljava/lang/String;)V

    .line 49
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->getScore()D

    move-result-wide v3

    invoke-static {v3, v4}, Ljava/lang/String;->valueOf(D)Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Ljava/lang/Float;->parseFloat(Ljava/lang/String;)F

    move-result v3

    invoke-virtual {v2, v3}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->setPossibility(F)V

    .line 52
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->getVertices()Ljava/util/List;

    move-result-object v3

    const/4 v4, 0x0

    if-eqz v3, :cond_1

    .line 55
    invoke-interface {v3, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;

    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;->getX()I

    move-result v5

    int-to-double v5, v5

    mul-double/2addr v5, p2

    double-to-int v5, v5

    .line 56
    invoke-interface {v3, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;->getY()I

    move-result v4

    int-to-double v6, v4

    mul-double/2addr v6, p2

    double-to-int v4, v6

    const/4 v6, 0x1

    .line 57
    invoke-interface {v3, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;

    invoke-virtual {v7}, Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;->getX()I

    move-result v7

    int-to-double v7, v7

    mul-double/2addr v7, p2

    double-to-int v7, v7

    .line 58
    invoke-interface {v3, v6}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/landmark/bo/Vertex;->getY()I

    move-result v3

    int-to-double v8, v3

    mul-double/2addr v8, p2

    double-to-int v3, v8

    .line 60
    new-instance v6, Landroid/graphics/Rect;

    invoke-direct {v6, v5, v4, v7, v3}, Landroid/graphics/Rect;-><init>(IIII)V

    invoke-virtual {v2, v6}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->setBorder(Landroid/graphics/Rect;)V

    goto :goto_2

    .line 62
    :cond_1
    new-instance v3, Landroid/graphics/Rect;

    invoke-direct {v3, v4, v4, v4, v4}, Landroid/graphics/Rect;-><init>(IIII)V

    invoke-virtual {v2, v3}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->setBorder(Landroid/graphics/Rect;)V

    .line 66
    :goto_2
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;->getLocations()Ljava/util/List;

    move-result-object v1

    .line 67
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    if-eqz v1, :cond_2

    .line 69
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_3
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/huawei/hms/mlsdk/landmark/bo/Location;

    .line 70
    new-instance v5, Lcom/huawei/hms/mlsdk/common/MLCoordinate;

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/landmark/bo/Location;->getLatitude()D

    move-result-wide v6

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/landmark/bo/Location;->getLongitude()D

    move-result-wide v8

    invoke-direct {v5, v6, v7, v8, v9}, Lcom/huawei/hms/mlsdk/common/MLCoordinate;-><init>(DD)V

    invoke-interface {v3, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_3

    .line 73
    :cond_2
    new-instance v1, Lcom/huawei/hms/mlsdk/common/MLCoordinate;

    const-wide/16 v4, 0x0

    invoke-direct {v1, v4, v5, v4, v5}, Lcom/huawei/hms/mlsdk/common/MLCoordinate;-><init>(DD)V

    invoke-interface {v3, v1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 75
    :cond_3
    invoke-virtual {v2, v3}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;->setPositionInfos(Ljava/util/List;)V

    .line 77
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :cond_4
    return-object v0

    .line 78
    :cond_5
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->getRetCode()Ljava/lang/String;

    move-result-object p2

    const-string p3, "2001"

    invoke-virtual {p3, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_b

    .line 80
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->getRetCode()Ljava/lang/String;

    move-result-object p2

    const-string p3, "2002"

    invoke-virtual {p3, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_a

    .line 83
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->getRetCode()Ljava/lang/String;

    move-result-object p2

    const-string p3, "2005"

    invoke-virtual {p3, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_9

    .line 85
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->getRetCode()Ljava/lang/String;

    move-result-object p2

    const-string p3, "4005"

    invoke-virtual {p3, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    const/16 p3, 0x9

    if-nez p2, :cond_8

    .line 88
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->getRetCode()Ljava/lang/String;

    move-result-object p2

    const-string v0, "4006"

    invoke-virtual {v0, p2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_7

    .line 91
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->getRetCode()Ljava/lang/String;

    move-result-object p1

    const-string p2, "4007"

    invoke-virtual {p2, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_6

    .line 92
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string p2, "The free quota has been used up,please upgrade package on https://developer.huawei.com."

    invoke-direct {p1, p2, p3}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 96
    :cond_6
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string p2, "Internal error."

    invoke-direct {p1, p2, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 97
    :cond_7
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string p2, "Please subscribe package first on https://developer.huawei.com."

    invoke-direct {p1, p2, p3}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 98
    :cond_8
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string p2, "The project has been out of credit and grace period is over."

    invoke-direct {p1, p2, p3}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 99
    :cond_9
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string p2, "Picture is not recognized."

    invoke-direct {p1, p2, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 100
    :cond_a
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const/4 p2, 0x5

    const-string p3, "Incorrect parameter. This exception is irrelevant to services."

    invoke-direct {p1, p3, p2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 101
    :cond_b
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const/16 p2, 0xf

    const-string p3, "Identity authentication required."

    invoke-direct {p1, p3, p2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 102
    :cond_c
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string p2, "Cloud service return the empty result."

    invoke-direct {p1, p2, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 103
    :cond_d
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string p2, "Get cloud response failed."

    invoke-direct {p1, p2, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1
.end method

.method private isHeaderInvalidate(Ljava/util/Map;)Z
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)Z"
        }
    .end annotation

    const-string v0, "appId"

    .line 1
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    const/4 v1, 0x1

    if-eqz v0, :cond_4

    .line 2
    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_1

    :cond_0
    const-string v0, "Authorization"

    .line 7
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    const-string v2, "Bearer "

    const-string v3, ""

    invoke-virtual {v0, v2, v3}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v0

    .line 8
    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 9
    sget-object p1, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->TAG:Ljava/lang/String;

    const-string v0, "header file api_key is empty"

    invoke-static {p1, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    return v1

    :cond_1
    const-string v0, "X-Package-Name"

    .line 13
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    if-eqz p1, :cond_3

    .line 14
    invoke-virtual {p1}, Ljava/lang/String;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    return p1

    .line 15
    :cond_3
    :goto_0
    sget-object p1, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->TAG:Ljava/lang/String;

    const-string v0, "header file package_name is empty"

    invoke-static {p1, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    return v1

    .line 16
    :cond_4
    :goto_1
    sget-object p1, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->TAG:Ljava/lang/String;

    const-string v0, "header app_id is empty"

    invoke-static {p1, v0}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    return v1
.end method

.method private packageParamJson(Ljava/lang/String;I)Ljava/lang/String;
    .locals 3

    .line 1
    sget-object v0, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const/4 v1, 0x2

    new-array v1, v1, [Ljava/lang/Object;

    const/4 v2, 0x0

    aput-object p1, v1, v2

    invoke-static {p2}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object p1

    const/4 p2, 0x1

    aput-object p1, v1, p2

    const-string/jumbo p1, "{\"imgBase64\":\"%s\",\"topNum\":\"%s\"}"

    invoke-static {v0, p1, v1}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method private resizeImage(Landroid/graphics/Bitmap;D)Landroid/graphics/Bitmap;
    .locals 3

    .line 1
    invoke-virtual {p1}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v0

    int-to-double v0, v0

    div-double/2addr v0, p2

    double-to-int v0, v0

    .line 2
    invoke-virtual {p1}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v1

    int-to-double v1, v1

    div-double/2addr v1, p2

    double-to-int p2, v1

    const/4 p3, 0x1

    .line 3
    invoke-static {p1, v0, p2, p3}, Landroid/graphics/Bitmap;->createScaledBitmap(Landroid/graphics/Bitmap;IIZ)Landroid/graphics/Bitmap;

    move-result-object p1

    return-object p1
.end method


# virtual methods
.method public asyncAnalyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Lcom/huawei/hmf/tasks/Task<",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmark;",
            ">;>;"
        }
    .end annotation

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;

    invoke-direct {v0, p0, p1}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer$1;-><init>(Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;)V

    invoke-static {v0}, Lcom/huawei/hmf/tasks/Tasks;->callInBackground(Ljava/util/concurrent/Callable;)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    return-object p1
.end method

.method public close()V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->stop()V

    return-void
.end method

.method public stop()V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->currentCall:Lcom/huawei/hms/network/httpclient/Submit;

    if-eqz v0, :cond_0

    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/Submit;->isCanceled()Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->currentCall:Lcom/huawei/hms/network/httpclient/Submit;

    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/Submit;->isExecuted()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->currentCall:Lcom/huawei/hms/network/httpclient/Submit;

    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/Submit;->cancel()V

    const/4 v0, 0x0

    .line 3
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzer;->currentCall:Lcom/huawei/hms/network/httpclient/Submit;

    :cond_0
    return-void
.end method
