.class final Lcom/huawei/hms/mlsdk/mlvision/a$b;
.super Ljava/lang/Object;
.source "RemoteFaceInitializer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/mlvision/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "b"
.end annotation


# static fields
.field static final a:Lcom/huawei/hms/mlsdk/mlvision/a;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/mlvision/a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/huawei/hms/mlsdk/mlvision/a;-><init>(Lcom/huawei/hms/mlsdk/mlvision/a$a;)V

    sput-object v0, Lcom/huawei/hms/mlsdk/mlvision/a$b;->a:Lcom/huawei/hms/mlsdk/mlvision/a;

    return-void
.end method
