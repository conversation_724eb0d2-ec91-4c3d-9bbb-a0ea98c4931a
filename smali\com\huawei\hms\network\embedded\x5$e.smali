.class public interface abstract Lcom/huawei/hms/network/embedded/x5$e;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/network/embedded/x5;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "e"
.end annotation


# static fields
.field public static final a:Ljava/lang/String; = "detectserivce/checkConnectivity"

.field public static final b:I = 0x1388

.field public static final c:I = 0x194

.field public static final d:I = 0xcc
