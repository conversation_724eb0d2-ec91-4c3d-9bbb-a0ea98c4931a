.class public final Lcom/huawei/hms/network/embedded/w;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/network/embedded/w$a;,
        Lcom/huawei/hms/network/embedded/w$c;,
        Lcom/huawei/hms/network/embedded/w$b;
    }
.end annotation


# static fields
.field public static final a:I = 0x927c0

.field public static final b:I = 0xea60

.field public static final c:I = 0x5265c00

.field public static final d:I = 0x80

.field public static final e:I = 0x5

.field public static f:I = 0x2

.field public static g:I = 0x0

.field public static h:J = 0x927c0L

.field public static final i:Ljava/lang/String; = "Default"

.field public static final j:Ljava/lang/String; = "LocalDns"

.field public static final k:Ljava/lang/String; = "DNKeeper"

.field public static final l:Ljava/lang/String; = "HttpDns"


# direct methods
.method public static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static a()J
    .locals 2

    sget-wide v0, Lcom/huawei/hms/network/embedded/w;->h:J

    return-wide v0
.end method

.method public static a(I)V
    .locals 0

    sput p0, Lcom/huawei/hms/network/embedded/w;->g:I

    return-void
.end method

.method public static a(J)V
    .locals 0

    sput-wide p0, Lcom/huawei/hms/network/embedded/w;->h:J

    return-void
.end method

.method public static b()I
    .locals 1

    sget v0, Lcom/huawei/hms/network/embedded/w;->g:I

    return v0
.end method

.method public static b(I)V
    .locals 0

    sput p0, Lcom/huawei/hms/network/embedded/w;->f:I

    return-void
.end method

.method public static c()I
    .locals 1

    sget v0, Lcom/huawei/hms/network/embedded/w;->f:I

    return v0
.end method
