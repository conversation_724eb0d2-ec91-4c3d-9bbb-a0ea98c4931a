.class public interface abstract Lcom/huawei/hms/network/embedded/x9;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final a:Lcom/huawei/hms/network/embedded/x9;


# direct methods
.method public static constructor <clinit>()V
    .locals 1

    new-instance v0, Lcom/huawei/hms/network/embedded/x9$a;

    invoke-direct {v0}, Lcom/huawei/hms/network/embedded/x9$a;-><init>()V

    sput-object v0, Lcom/huawei/hms/network/embedded/x9;->a:Lcom/huawei/hms/network/embedded/x9;

    return-void
.end method


# virtual methods
.method public abstract a(ILcom/huawei/hms/network/embedded/n9;)V
.end method

.method public abstract a(ILcom/huawei/hms/network/embedded/za;IZ)Z
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation
.end method

.method public abstract a(ILjava/util/List;)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lcom/huawei/hms/network/embedded/o9;",
            ">;)Z"
        }
    .end annotation
.end method

.method public abstract a(ILjava/util/List;Z)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/List<",
            "Lcom/huawei/hms/network/embedded/o9;",
            ">;Z)Z"
        }
    .end annotation
.end method
