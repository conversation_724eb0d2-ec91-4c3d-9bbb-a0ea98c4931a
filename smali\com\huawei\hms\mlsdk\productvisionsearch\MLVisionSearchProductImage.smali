.class public Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;
.super Ljava/lang/Object;
.source "MLVisionSearchProductImage.java"


# instance fields
.field private a:Ljava/lang/String;

.field private b:Ljava/lang/String;

.field private c:F

.field private d:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;F)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->a:Ljava/lang/String;

    .line 4
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->b:Ljava/lang/String;

    .line 5
    iput p3, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->c:F

    return-void
.end method


# virtual methods
.method public getImageId()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->b:Ljava/lang/String;

    return-object v0
.end method

.method public getInnerUrl()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->d:Ljava/lang/String;

    return-object v0
.end method

.method public getPossibility()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->c:F

    return v0
.end method

.method public getProductId()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->a:Ljava/lang/String;

    return-object v0
.end method

.method public setImageId(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->b:Ljava/lang/String;

    return-void
.end method

.method public setInnerUrl(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->d:Ljava/lang/String;

    return-void
.end method

.method public setPossibility(F)V
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->c:F

    return-void
.end method

.method public setProductId(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->a:Ljava/lang/String;

    return-void
.end method
