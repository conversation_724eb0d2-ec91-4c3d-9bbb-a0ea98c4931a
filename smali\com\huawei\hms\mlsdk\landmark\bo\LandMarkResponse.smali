.class public Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;
.super Ljava/lang/Object;
.source "LandMarkResponse.java"


# instance fields
.field private landMarkResults:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;",
            ">;"
        }
    .end annotation
.end field

.field private retCode:Ljava/lang/String;

.field private retMsg:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getLandMarkResults()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->landMarkResults:Ljava/util/List;

    return-object v0
.end method

.method public getRetCode()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->retCode:Ljava/lang/String;

    return-object v0
.end method

.method public getRetMsg()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->retMsg:Ljava/lang/String;

    return-object v0
.end method

.method public setLandMarkResults(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResult;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->landMarkResults:Ljava/util/List;

    return-void
.end method

.method public setRetCode(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->retCode:Ljava/lang/String;

    return-void
.end method

.method public setRetMsg(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/landmark/bo/LandMarkResponse;->retMsg:Ljava/lang/String;

    return-void
.end method
