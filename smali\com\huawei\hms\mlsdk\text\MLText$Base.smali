.class public Lcom/huawei/hms/mlsdk/text/MLText$Base;
.super Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;
.source "MLText.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/text/MLText;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Base"
.end annotation


# direct methods
.method public constructor <init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;[Landroid/graphics/Point;F)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroid/graphics/Rect;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/TextLanguage;",
            ">;[",
            "Landroid/graphics/Point;",
            "F)V"
        }
    .end annotation

    .line 1
    invoke-direct/range {p0 .. p5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;[Landroid/graphics/Point;F)V

    return-void
.end method


# virtual methods
.method public getContents()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "+",
            "Lcom/huawei/hms/mlsdk/text/MLText$Base;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x0

    return-object v0
.end method

.method public getPossibility()Ljava/lang/Float;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getConfidence()Ljava/lang/Float;

    move-result-object v0

    return-object v0
.end method
