.class public Lcom/huawei/hms/network/ai/n0;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ljava/io/Serializable;


# direct methods
.method public constructor <init>(Ljava/io/InputStream;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lcom/huawei/hms/network/ai/m0;)[F
    .locals 0

    const/4 p1, 0x0

    new-array p1, p1, [F

    return-object p1
.end method
