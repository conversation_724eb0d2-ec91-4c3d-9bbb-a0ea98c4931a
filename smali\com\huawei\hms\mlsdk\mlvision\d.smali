.class public Lcom/huawei/hms/mlsdk/mlvision/d;
.super Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;
.source "RemoteObjectInitializer.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/mlvision/d$b;
    }
.end annotation


# direct methods
.method synthetic constructor <init>(Lcom/huawei/hms/mlsdk/mlvision/d$a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;-><init>()V

    return-void
.end method

.method public static a()Lcom/huawei/hms/mlsdk/mlvision/d;
    .locals 1

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/mlvision/d$b;->a:Lcom/huawei/hms/mlsdk/mlvision/d;

    return-object v0
.end method


# virtual methods
.method protected generateDynamicDelegateBridge(Landroid/os/IBinder;)Landroid/os/IInterface;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    invoke-static {p1}, Lcom/huawei/hms/ml/common/object/IRemoteObjectCreator$Stub;->asInterface(Landroid/os/IBinder;)Lcom/huawei/hms/ml/common/object/IRemoteObjectCreator;

    move-result-object p1

    invoke-interface {p1}, Lcom/huawei/hms/ml/common/object/IRemoteObjectCreator;->newRemoteObjectDecoderDelegate()Lcom/huawei/hms/ml/common/object/IRemoteObjectDecoderDelegate;

    move-result-object p1

    return-object p1
.end method

.method protected getCreatorClass()Ljava/lang/String;
    .locals 1

    const-string v0, "com.huawei.hms.mlkit.object.Creator"

    return-object v0
.end method

.method public declared-synchronized getDynamicDelegate()Landroid/os/IInterface;
    .locals 1

    monitor-enter p0

    .line 1
    :try_start_0
    invoke-super {p0}, Lcom/huawei/hms/mlsdk/dynamic/AbstractInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method protected getMinApkVersion()I
    .locals 1

    const v0, 0x9897ac

    return v0
.end method

.method protected getModuleName()Ljava/lang/String;
    .locals 1

    const-string v0, "huawei_module_mlkit_object"

    return-object v0
.end method

.method public release()V
    .locals 0

    return-void
.end method
