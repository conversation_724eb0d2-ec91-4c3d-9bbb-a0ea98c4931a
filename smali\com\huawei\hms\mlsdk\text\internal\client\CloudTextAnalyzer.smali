.class public Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;
.super Ljava/lang/Object;
.source "CloudTextAnalyzer.java"

# interfaces
.implements Ljava/io/Closeable;


# static fields
.field private static final TAG:Ljava/lang/String; = "CloudTextAnalyzer"

.field private static appOptionDetectMap:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Lcom/huawei/hms/mlsdk/common/AppSettingHolder<",
            "Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;",
            ">;",
            "Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private app:Lcom/huawei/hms/mlsdk/common/MLApplication;

.field private final options:Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->appOptionDetectMap:Ljava/util/Map;

    return-void
.end method

.method private constructor <init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    .line 3
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->options:Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    return-void
.end method

.method static synthetic access$000(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;)Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->options:Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    return-object p0
.end method

.method static synthetic access$100(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;)Lcom/huawei/hms/mlsdk/common/MLApplication;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->app:Lcom/huawei/hms/mlsdk/common/MLApplication;

    return-object p0
.end method

.method static synthetic access$200(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Ljava/util/Map;)Z
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->isHeaderInvalidate(Ljava/util/Map;)Z

    move-result p0

    return p0
.end method

.method static synthetic access$300(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Landroid/graphics/Bitmap;)F
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->calculateScale(Landroid/graphics/Bitmap;)F

    move-result p0

    return p0
.end method

.method static synthetic access$400(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Landroid/graphics/Bitmap;F)Landroid/graphics/Bitmap;
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->resizeImage(Landroid/graphics/Bitmap;F)Landroid/graphics/Bitmap;

    move-result-object p0

    return-object p0
.end method

.method static synthetic access$500(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Ljava/lang/String;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;)Ljava/lang/String;
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->packageParams(Ljava/lang/String;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method static synthetic access$600(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Ljava/util/List;Ljava/util/Map;Ljava/lang/String;)Lcom/huawei/hms/network/httpclient/Response;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/huawei/hms/mlsdk/common/MLException;
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2, p3}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->traverseCloudAnalyzerApi(Ljava/util/List;Ljava/util/Map;Ljava/lang/String;)Lcom/huawei/hms/network/httpclient/Response;

    move-result-object p0

    return-object p0
.end method

.method static synthetic access$700(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Lcom/huawei/hms/network/httpclient/Response;F)Lcom/huawei/hms/mlsdk/text/MLText;
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->handleResult(Lcom/huawei/hms/network/httpclient/Response;F)Lcom/huawei/hms/mlsdk/text/MLText;

    move-result-object p0

    return-object p0
.end method

.method private calculateScale(Landroid/graphics/Bitmap;)F
    .locals 2

    .line 1
    invoke-virtual {p1}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v0

    .line 2
    invoke-virtual {p1}, Landroid/graphics/Bitmap;->getHeight()I

    move-result p1

    .line 3
    invoke-static {v0, p1}, Ljava/lang/Math;->min(II)I

    move-result p1

    int-to-float p1, p1

    const/high16 v0, 0x3f800000    # 1.0f

    mul-float/2addr p1, v0

    const/high16 v1, 0x44870000    # 1080.0f

    div-float/2addr p1, v1

    cmpg-float v1, p1, v0

    if-gez v1, :cond_0

    goto :goto_0

    :cond_0
    move v0, p1

    :goto_0
    return v0
.end method

.method private cloudBlockToHmsBlock(Lcom/huawei/hms/mlsdk/document/internal/client/bo/Region;F)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;
    .locals 8

    .line 1
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 2
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 3
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 4
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Region;->getSections()Ljava/util/List;

    move-result-object v1

    if-eqz v1, :cond_3

    .line 6
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Section;

    .line 7
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Section;->getLines()Ljava/util/List;

    move-result-object v2

    if-eqz v2, :cond_0

    .line 9
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_1
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_0

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Line;

    .line 10
    invoke-direct {p0, v5, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->cloudLineToLine(Lcom/huawei/hms/mlsdk/document/internal/client/bo/Line;F)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;

    move-result-object v5

    .line 11
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->length()I

    move-result v6

    if-eqz v6, :cond_2

    .line 12
    invoke-static {}, Ljava/lang/System;->lineSeparator()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 14
    :cond_2
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getStringValue()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 15
    invoke-virtual {v4, v5}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 16
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getLanguageList()Ljava/util/List;

    move-result-object v6

    if-eqz v6, :cond_1

    .line 17
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getLanguageList()Ljava/util/List;

    move-result-object v6

    invoke-virtual {v3, v6}, Ljava/util/ArrayList;->removeAll(Ljava/util/Collection;)Z

    .line 18
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getLanguageList()Ljava/util/List;

    move-result-object v5

    invoke-virtual {v3, v5}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    .line 24
    :cond_3
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Region;->getCoords()Ljava/util/List;

    move-result-object v1

    invoke-virtual {p0, v1, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->boundingPloyToPoints(Ljava/util/List;F)[Landroid/graphics/Point;

    move-result-object v5

    .line 25
    new-instance v7, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;

    .line 26
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Region;->getCoords()Ljava/util/List;

    move-result-object v0

    invoke-static {v0, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->createBoundingBox(Ljava/util/List;F)Landroid/graphics/Rect;

    move-result-object v2

    .line 27
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Region;->getConfidence()D

    move-result-wide p1

    invoke-static {p1, p2}, Ljava/lang/String;->valueOf(D)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Ljava/lang/Float;->parseFloat(Ljava/lang/String;)F

    move-result v6

    move-object v0, v7

    invoke-direct/range {v0 .. v6}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V

    return-object v7
.end method

.method private cloudLineToLine(Lcom/huawei/hms/mlsdk/document/internal/client/bo/Line;F)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;
    .locals 8

    .line 1
    new-instance v4, Ljava/util/ArrayList;

    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 2
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 3
    new-instance v3, Ljava/util/ArrayList;

    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 5
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Line;->getTerms()Ljava/util/List;

    move-result-object v1

    if-eqz v1, :cond_2

    .line 7
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Term;

    .line 8
    invoke-direct {p0, v2, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->cloudWordToElement(Lcom/huawei/hms/mlsdk/document/internal/client/bo/Term;F)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;

    move-result-object v5

    .line 9
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getStringValue()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v0, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 10
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Term;->getChars()Ljava/util/List;

    move-result-object v6

    if-eqz v6, :cond_1

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Term;->getChars()Ljava/util/List;

    move-result-object v6

    invoke-interface {v6}, Ljava/util/List;->size()I

    move-result v6

    if-lez v6, :cond_1

    .line 11
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Term;->getChars()Ljava/util/List;

    move-result-object v6

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Term;->getChars()Ljava/util/List;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    add-int/lit8 v2, v2, -0x1

    invoke-interface {v6, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Char;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Char;->getBreakType()Ljava/lang/String;

    move-result-object v2

    const-string v6, "SPACE"

    .line 12
    invoke-virtual {v6, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v2

    if-eqz v2, :cond_1

    const-string v2, " "

    .line 13
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 16
    :cond_1
    invoke-virtual {v4, v5}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 17
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getLanguageList()Ljava/util/List;

    move-result-object v2

    if-eqz v2, :cond_0

    .line 18
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getLanguageList()Ljava/util/List;

    move-result-object v2

    invoke-virtual {v3, v2}, Ljava/util/ArrayList;->removeAll(Ljava/util/Collection;)Z

    .line 19
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getLanguageList()Ljava/util/List;

    move-result-object v2

    invoke-virtual {v3, v2}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    .line 23
    :cond_2
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Line;->getCoords()Ljava/util/List;

    move-result-object v1

    invoke-virtual {p0, v1, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->boundingPloyToPoints(Ljava/util/List;F)[Landroid/graphics/Point;

    move-result-object v5

    .line 25
    new-instance v7, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Line;->getCoords()Ljava/util/List;

    move-result-object v0

    invoke-static {v0, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->createBoundingBox(Ljava/util/List;F)Landroid/graphics/Rect;

    move-result-object v2

    .line 26
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Line;->getConfidence()D

    move-result-wide p1

    invoke-static {p1, p2}, Ljava/lang/String;->valueOf(D)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Ljava/lang/Float;->parseFloat(Ljava/lang/String;)F

    move-result v6

    move-object v0, v7

    invoke-direct/range {v0 .. v6}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V

    return-object v7
.end method

.method private cloudWordToElement(Lcom/huawei/hms/mlsdk/document/internal/client/bo/Term;F)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;
    .locals 8

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 3
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Term;->getLanguages()Ljava/util/List;

    move-result-object v1

    .line 4
    invoke-static {v1}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->propertyToLanguages(Ljava/util/List;)Ljava/util/List;

    move-result-object v5

    .line 6
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Term;->getChars()Ljava/util/List;

    move-result-object v1

    if-eqz v1, :cond_0

    .line 8
    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Char;

    .line 9
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Char;->getValue()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 12
    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Term;->getCoords()Ljava/util/List;

    move-result-object v1

    invoke-virtual {p0, v1, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->boundingPloyToPoints(Ljava/util/List;F)[Landroid/graphics/Point;

    move-result-object v6

    .line 13
    new-instance v1, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Term;->getCoords()Ljava/util/List;

    move-result-object v0

    invoke-static {v0, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->createBoundingBox(Ljava/util/List;F)Landroid/graphics/Rect;

    move-result-object v4

    .line 14
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Term;->getConfidence()D

    move-result-wide p1

    invoke-static {p1, p2}, Ljava/lang/String;->valueOf(D)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Ljava/lang/Float;->parseFloat(Ljava/lang/String;)F

    move-result v7

    move-object v2, v1

    invoke-direct/range {v2 .. v7}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;[Landroid/graphics/Point;F)V

    return-object v1
.end method

.method public static declared-synchronized create(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;)Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;
    .locals 3

    const-class v0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    monitor-enter v0

    if-eqz p0, :cond_1

    .line 1
    :try_start_0
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getUniqueKey()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1, p1}, Lcom/huawei/hms/mlsdk/common/AppSettingHolder;->create(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/common/AppSettingHolder;

    move-result-object v1

    .line 3
    sget-object v2, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->appOptionDetectMap:Ljava/util/Map;

    invoke-interface {v2, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    if-nez v2, :cond_0

    .line 4
    new-instance v2, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;

    invoke-direct {v2, p0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;-><init>(Lcom/huawei/hms/mlsdk/common/MLApplication;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;)V

    .line 5
    sget-object p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->appOptionDetectMap:Ljava/util/Map;

    invoke-interface {p0, v1, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_0
    monitor-exit v0

    return-object v2

    .line 6
    :cond_1
    :try_start_1
    new-instance p0, Ljava/lang/NullPointerException;

    invoke-direct {p0}, Ljava/lang/NullPointerException;-><init>()V

    throw p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :catchall_0
    move-exception p0

    monitor-exit v0

    throw p0
.end method

.method public static createBoundingBox(Ljava/util/List;F)Landroid/graphics/Rect;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/cloud/Coord;",
            ">;F)",
            "Landroid/graphics/Rect;"
        }
    .end annotation

    if-eqz p0, :cond_1

    .line 1
    invoke-interface {p0}, Ljava/util/List;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_1

    const/4 v0, 0x0

    const/high16 v1, -0x80000000

    const v2, 0x7fffffff

    move v3, v2

    move v4, v3

    move v2, v1

    .line 7
    :goto_0
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v5

    if-ge v0, v5, :cond_0

    .line 8
    invoke-interface {p0, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/huawei/hms/mlsdk/cloud/Coord;

    .line 9
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/cloud/Coord;->getX()I

    move-result v6

    invoke-static {v6, v3}, Ljava/lang/Math;->min(II)I

    move-result v3

    .line 10
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/cloud/Coord;->getY()I

    move-result v6

    invoke-static {v6, v4}, Ljava/lang/Math;->min(II)I

    move-result v4

    .line 11
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/cloud/Coord;->getX()I

    move-result v6

    invoke-static {v6, v1}, Ljava/lang/Math;->max(II)I

    move-result v1

    .line 12
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/cloud/Coord;->getY()I

    move-result v5

    invoke-static {v5, v2}, Ljava/lang/Math;->max(II)I

    move-result v2

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 14
    :cond_0
    new-instance p0, Landroid/graphics/Rect;

    int-to-float v0, v3

    mul-float/2addr v0, p1

    invoke-static {v0}, Ljava/lang/Math;->round(F)I

    move-result v0

    int-to-float v3, v4

    mul-float/2addr v3, p1

    invoke-static {v3}, Ljava/lang/Math;->round(F)I

    move-result v3

    int-to-float v1, v1

    mul-float/2addr v1, p1

    .line 15
    invoke-static {v1}, Ljava/lang/Math;->round(F)I

    move-result v1

    int-to-float v2, v2

    mul-float/2addr v2, p1

    invoke-static {v2}, Ljava/lang/Math;->round(F)I

    move-result p1

    invoke-direct {p0, v0, v3, v1, p1}, Landroid/graphics/Rect;-><init>(IIII)V

    return-object p0

    :cond_1
    const/4 p0, 0x0

    return-object p0
.end method

.method public static createBoundingBoxFromCoords(Ljava/util/List;F)Landroid/graphics/Rect;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/cloud/Coord;",
            ">;F)",
            "Landroid/graphics/Rect;"
        }
    .end annotation

    if-eqz p0, :cond_1

    .line 1
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_1

    const/4 v0, 0x0

    const/high16 v1, -0x80000000

    const v2, 0x7fffffff

    move v3, v2

    move v4, v3

    move v2, v1

    .line 7
    :goto_0
    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v5

    if-ge v0, v5, :cond_0

    .line 8
    invoke-interface {p0, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Lcom/huawei/hms/mlsdk/cloud/Coord;

    .line 9
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/cloud/Coord;->getX()I

    move-result v6

    invoke-static {v6, v3}, Ljava/lang/Math;->min(II)I

    move-result v3

    .line 10
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/cloud/Coord;->getY()I

    move-result v6

    invoke-static {v6, v4}, Ljava/lang/Math;->min(II)I

    move-result v4

    .line 11
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/cloud/Coord;->getX()I

    move-result v6

    invoke-static {v6, v1}, Ljava/lang/Math;->max(II)I

    move-result v1

    .line 12
    invoke-virtual {v5}, Lcom/huawei/hms/mlsdk/cloud/Coord;->getY()I

    move-result v5

    invoke-static {v5, v2}, Ljava/lang/Math;->max(II)I

    move-result v2

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 14
    :cond_0
    new-instance p0, Landroid/graphics/Rect;

    int-to-float v0, v3

    mul-float/2addr v0, p1

    invoke-static {v0}, Ljava/lang/Math;->round(F)I

    move-result v0

    int-to-float v3, v4

    mul-float/2addr v3, p1

    invoke-static {v3}, Ljava/lang/Math;->round(F)I

    move-result v3

    int-to-float v1, v1

    mul-float/2addr v1, p1

    .line 15
    invoke-static {v1}, Ljava/lang/Math;->round(F)I

    move-result v1

    int-to-float v2, v2

    mul-float/2addr v2, p1

    invoke-static {v2}, Ljava/lang/Math;->round(F)I

    move-result p1

    invoke-direct {p0, v0, v3, v1, p1}, Landroid/graphics/Rect;-><init>(IIII)V

    return-object p0

    :cond_1
    const/4 p0, 0x0

    return-object p0
.end method

.method private createText(Lcom/huawei/hms/mlsdk/document/internal/client/bo/RemoteOcrDocumentResponse;F)Lcom/huawei/hms/mlsdk/text/MLText;
    .locals 3

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 2
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/RemoteOcrDocumentResponse;->getDocs()Ljava/util/List;

    move-result-object p1

    if-eqz p1, :cond_3

    .line 3
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    if-nez v1, :cond_0

    goto :goto_2

    .line 7
    :cond_0
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 8
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_1

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Document;

    .line 9
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Document;->getRegions()Ljava/util/List;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    .line 11
    :goto_1
    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v2

    if-ge p1, v2, :cond_2

    .line 12
    invoke-interface {v1, p1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Region;

    invoke-direct {p0, v2, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->cloudBlockToHmsBlock(Lcom/huawei/hms/mlsdk/document/internal/client/bo/Region;F)Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;

    move-result-object v2

    .line 13
    invoke-interface {v0, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    add-int/lit8 p1, p1, 0x1

    goto :goto_1

    .line 16
    :cond_2
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    .line 17
    new-instance p2, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;

    invoke-direct {p2, v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;-><init>(Ljava/util/List;)V

    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 18
    new-instance p2, Lcom/huawei/hms/mlsdk/text/MLText;

    invoke-direct {p2, p1}, Lcom/huawei/hms/mlsdk/text/MLText;-><init>(Ljava/util/List;)V

    return-object p2

    .line 19
    :cond_3
    :goto_2
    new-instance p1, Lcom/huawei/hms/mlsdk/text/MLText;

    invoke-direct {p1}, Lcom/huawei/hms/mlsdk/text/MLText;-><init>()V

    return-object p1
.end method

.method private createVisionText(Ljava/util/List;D)Lcom/huawei/hms/mlsdk/text/MLText;
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;",
            ">;D)",
            "Lcom/huawei/hms/mlsdk/text/MLText;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 2
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v2, 0x1

    .line 3
    :goto_0
    invoke-interface/range {p1 .. p1}, Ljava/util/List;->size()I

    move-result v3

    if-ge v2, v3, :cond_1

    move-object/from16 v3, p1

    .line 4
    invoke-interface {v3, v2}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;

    .line 5
    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->getText()Ljava/lang/String;

    move-result-object v12

    .line 6
    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->getConfidence()D

    move-result-wide v5

    double-to-float v13, v5

    .line 7
    new-instance v5, Lcom/huawei/hms/mlsdk/text/TextLanguage;

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->getLanguage()Ljava/lang/String;

    move-result-object v6

    invoke-direct {v5, v6}, Lcom/huawei/hms/mlsdk/text/TextLanguage;-><init>(Ljava/lang/String;)V

    .line 8
    new-instance v14, Ljava/util/ArrayList;

    invoke-direct {v14}, Ljava/util/ArrayList;-><init>()V

    .line 9
    invoke-interface {v14, v5}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 11
    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->getCoords()Ljava/util/List;

    move-result-object v5

    move-wide/from16 v10, p2

    double-to-float v6, v10

    invoke-static {v5, v6}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->createBoundingBoxFromCoords(Ljava/util/List;F)Landroid/graphics/Rect;

    move-result-object v15

    .line 12
    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteText;->getCoords()Ljava/util/List;

    move-result-object v4

    move-object/from16 v9, p0

    invoke-virtual {v9, v4, v6}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->boundingPloyToPointsFromCoords(Ljava/util/List;F)[Landroid/graphics/Point;

    move-result-object v4

    .line 14
    new-instance v8, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;

    move-object v5, v8

    move-object v6, v12

    move-object v7, v15

    move-object v3, v8

    move-object v8, v14

    move-object v9, v4

    move v10, v13

    invoke-direct/range {v5 .. v10}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextWord;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;[Landroid/graphics/Point;F)V

    .line 15
    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    .line 16
    invoke-interface {v9, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 17
    new-instance v3, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;

    move-object v5, v3

    move-object v10, v4

    move v11, v13

    invoke-direct/range {v5 .. v11}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V

    .line 18
    new-instance v9, Ljava/util/ArrayList;

    invoke-direct {v9}, Ljava/util/ArrayList;-><init>()V

    .line 19
    invoke-interface {v9, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 20
    new-instance v3, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;

    move-object v5, v3

    invoke-direct/range {v5 .. v11}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V

    .line 21
    invoke-interface {v0, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 22
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->length()I

    move-result v3

    if-lez v3, :cond_0

    .line 23
    invoke-static {}, Ljava/lang/System;->lineSeparator()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 25
    :cond_0
    invoke-virtual {v1, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 27
    :cond_1
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 28
    new-instance v2, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;

    invoke-direct {v2, v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;-><init>(Ljava/util/List;)V

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 29
    new-instance v0, Lcom/huawei/hms/mlsdk/text/MLText;

    invoke-direct {v0, v1}, Lcom/huawei/hms/mlsdk/text/MLText;-><init>(Ljava/util/List;)V

    return-object v0
.end method

.method private getErrorMessageByResponseCode(Ljava/lang/String;)V
    .locals 4
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/huawei/hms/mlsdk/common/MLException;
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    move-result v0

    const/4 v1, 0x5

    const/4 v2, 0x2

    const/4 v3, -0x1

    sparse-switch v0, :sswitch_data_0

    goto :goto_0

    :sswitch_0
    const-string v0, "4007"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    move v3, v1

    goto :goto_0

    :sswitch_1
    const-string v0, "4006"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_1

    goto :goto_0

    :cond_1
    const/4 v3, 0x4

    goto :goto_0

    :sswitch_2
    const-string v0, "4005"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v3, 0x3

    goto :goto_0

    :sswitch_3
    const-string v0, "2005"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_3

    goto :goto_0

    :cond_3
    move v3, v2

    goto :goto_0

    :sswitch_4
    const-string v0, "2002"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_4

    goto :goto_0

    :cond_4
    const/4 v3, 0x1

    goto :goto_0

    :sswitch_5
    const-string v0, "2001"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_5

    goto :goto_0

    :cond_5
    const/4 v3, 0x0

    :goto_0
    const/16 p1, 0x9

    packed-switch v3, :pswitch_data_0

    .line 20
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v0, "Internal error."

    invoke-direct {p1, v0, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 21
    :pswitch_0
    new-instance v0, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v1, "The free quota has been used up,please upgrade package on https://developer.huawei.com."

    invoke-direct {v0, v1, p1}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v0

    .line 22
    :pswitch_1
    new-instance v0, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v1, "Please subscribe package first on https://developer.huawei.com."

    invoke-direct {v0, v1, p1}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v0

    .line 23
    :pswitch_2
    new-instance v0, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v1, "The project has been out of credit and grace period is over."

    invoke-direct {v0, v1, p1}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw v0

    .line 24
    :pswitch_3
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v0, "Picture is not recognized."

    invoke-direct {p1, v0, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 25
    :pswitch_4
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string v0, "Incorrect parameter. This exception is irrelevant to services."

    invoke-direct {p1, v0, v1}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 26
    :pswitch_5
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const/16 v0, 0xf

    const-string v1, "Identity authentication required."

    invoke-direct {p1, v1, v0}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    :sswitch_data_0
    .sparse-switch
        0x1774bf -> :sswitch_5
        0x1774c0 -> :sswitch_4
        0x1774c3 -> :sswitch_3
        0x185d81 -> :sswitch_2
        0x185d82 -> :sswitch_1
        0x185d83 -> :sswitch_0
    .end sparse-switch

    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method private handleResult(Lcom/huawei/hms/network/httpclient/Response;F)Lcom/huawei/hms/mlsdk/text/MLText;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/network/httpclient/Response<",
            "Ljava/lang/String;",
            ">;F)",
            "Lcom/huawei/hms/mlsdk/text/MLText;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/text/MLText;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/text/MLText;-><init>()V

    .line 2
    invoke-virtual {p1}, Lcom/huawei/hms/network/httpclient/Response;->isSuccessful()Z

    move-result v1

    const/4 v2, 0x2

    if-eqz v1, :cond_7

    .line 5
    invoke-virtual {p1}, Lcom/huawei/hms/network/httpclient/Response;->getBody()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    .line 7
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->options:Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->getTextDensityScene()I

    move-result v1

    if-ne v1, v2, :cond_0

    .line 8
    new-instance v1, Lcom/google/gson/Gson;

    invoke-direct {v1}, Lcom/google/gson/Gson;-><init>()V

    const-class v3, Lcom/huawei/hms/mlsdk/document/internal/client/bo/RemoteOcrDocumentResponse;

    invoke-virtual {v1, p1, v3}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    goto :goto_0

    .line 10
    :cond_0
    new-instance v1, Lcom/google/gson/Gson;

    invoke-direct {v1}, Lcom/google/gson/Gson;-><init>()V

    const-class v3, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;

    invoke-virtual {v1, p1, v3}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    :goto_0
    const-string v1, "Cloud service return the empty result."

    if-eqz p1, :cond_6

    .line 16
    iget-object v3, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->options:Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->getTextDensityScene()I

    move-result v3

    const/4 v4, 0x1

    const-string v5, "0"

    if-ne v3, v4, :cond_3

    .line 17
    check-cast p1, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;

    const/4 v0, 0x0

    .line 19
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;->getRetCode()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v5, v3}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    .line 20
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;->getResponses()Ljava/util/List;

    move-result-object v0

    goto :goto_1

    .line 22
    :cond_1
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/text/internal/client/bo/RemoteOcrTextResponse;->getRetCode()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->getErrorMessageByResponseCode(Ljava/lang/String;)V

    :goto_1
    if-eqz v0, :cond_2

    .line 24
    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result p1

    if-eqz p1, :cond_2

    float-to-double p1, p2

    .line 28
    invoke-direct {p0, v0, p1, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->createVisionText(Ljava/util/List;D)Lcom/huawei/hms/mlsdk/text/MLText;

    move-result-object v0

    goto :goto_2

    .line 29
    :cond_2
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    invoke-direct {p1, v1, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 33
    :cond_3
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->options:Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->getTextDensityScene()I

    move-result v1

    if-ne v1, v2, :cond_5

    .line 34
    check-cast p1, Lcom/huawei/hms/mlsdk/document/internal/client/bo/RemoteOcrDocumentResponse;

    .line 35
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/RemoteOcrDocumentResponse;->getRetCode()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v5, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_4

    .line 36
    invoke-direct {p0, p1, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->createText(Lcom/huawei/hms/mlsdk/document/internal/client/bo/RemoteOcrDocumentResponse;F)Lcom/huawei/hms/mlsdk/text/MLText;

    move-result-object v0

    goto :goto_2

    .line 38
    :cond_4
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/RemoteOcrDocumentResponse;->getRetCode()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->getErrorMessageByResponseCode(Ljava/lang/String;)V

    :cond_5
    :goto_2
    return-object v0

    .line 39
    :cond_6
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    invoke-direct {p1, v1, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1

    .line 40
    :cond_7
    new-instance p1, Lcom/huawei/hms/mlsdk/common/MLException;

    const-string p2, "Get cloud response failed."

    invoke-direct {p1, p2, v2}, Lcom/huawei/hms/mlsdk/common/MLException;-><init>(Ljava/lang/String;I)V

    throw p1
.end method

.method private isHeaderInvalidate(Ljava/util/Map;)Z
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)Z"
        }
    .end annotation

    const-string v0, "appId"

    .line 1
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    const/4 v1, 0x1

    const-string v2, "CloudTextAnalyzer"

    if-eqz v0, :cond_4

    .line 2
    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_1

    :cond_0
    const-string v0, "Authorization"

    .line 7
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/String;

    const-string v3, "Bearer "

    const-string v4, ""

    invoke-virtual {v0, v3, v4}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object v0

    .line 8
    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-eqz v0, :cond_1

    const-string p1, "header file api_key is empty"

    .line 9
    invoke-static {v2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    return v1

    :cond_1
    const-string v0, "X-Package-Name"

    .line 13
    invoke-interface {p1, v0}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    if-eqz p1, :cond_3

    .line 14
    invoke-virtual {p1}, Ljava/lang/String;->isEmpty()Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    return p1

    :cond_3
    :goto_0
    const-string p1, "header file package_name is empty"

    .line 15
    invoke-static {v2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    return v1

    :cond_4
    :goto_1
    const-string p1, "header app_id is empty"

    .line 16
    invoke-static {v2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->e(Ljava/lang/String;Ljava/lang/String;)V

    return v1
.end method

.method private packageParams(Ljava/lang/String;Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;)Ljava/lang/String;
    .locals 4

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$2;

    invoke-direct {v0, p0}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$2;-><init>(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;)V

    .line 2
    invoke-virtual {v0}, Lcom/google/gson/reflect/TypeToken;->getType()Ljava/lang/reflect/Type;

    move-result-object v0

    .line 3
    sget-object v1, Ljava/util/Locale;->ENGLISH:Ljava/util/Locale;

    const/4 v2, 0x3

    new-array v2, v2, [Ljava/lang/Object;

    const/4 v3, 0x0

    aput-object p1, v2, v3

    invoke-virtual {p2}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->getBorderType()Ljava/lang/String;

    move-result-object p1

    const/4 v3, 0x1

    aput-object p1, v2, v3

    new-instance p1, Lcom/google/gson/Gson;

    invoke-direct {p1}, Lcom/google/gson/Gson;-><init>()V

    .line 4
    invoke-virtual {p2}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->getLanguageList()Ljava/util/List;

    move-result-object p2

    invoke-virtual {p1, p2, v0}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;Ljava/lang/reflect/Type;)Ljava/lang/String;

    move-result-object p1

    const/4 p2, 0x2

    aput-object p1, v2, p2

    const-string/jumbo p1, "{\"imgBase64\":\"%s\",\"coordsType\":\"%s\",\"languages\":%s}"

    .line 5
    invoke-static {v1, p1, v2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public static propertyToLanguages(Ljava/util/List;)Ljava/util/List;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/document/internal/client/bo/Language;",
            ">;)",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/TextLanguage;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    if-eqz p0, :cond_1

    .line 2
    invoke-interface {p0}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_1

    .line 3
    invoke-interface {p0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Language;

    .line 4
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/document/internal/client/bo/Language;->getLanguage()Ljava/lang/String;

    move-result-object v1

    .line 5
    invoke-virtual {v1}, Ljava/lang/String;->isEmpty()Z

    move-result v2

    if-nez v2, :cond_0

    new-instance v2, Lcom/huawei/hms/mlsdk/text/TextLanguage;

    invoke-direct {v2, v1}, Lcom/huawei/hms/mlsdk/text/TextLanguage;-><init>(Ljava/lang/String;)V

    goto :goto_1

    :cond_0
    const/4 v2, 0x0

    .line 6
    :goto_1
    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_1
    return-object v0
.end method

.method private resizeImage(Landroid/graphics/Bitmap;F)Landroid/graphics/Bitmap;
    .locals 2

    .line 1
    invoke-virtual {p1}, Landroid/graphics/Bitmap;->getWidth()I

    move-result v0

    int-to-float v0, v0

    div-float/2addr v0, p2

    float-to-int v0, v0

    .line 2
    invoke-virtual {p1}, Landroid/graphics/Bitmap;->getHeight()I

    move-result v1

    int-to-float v1, v1

    div-float/2addr v1, p2

    float-to-int p2, v1

    const/4 v1, 0x1

    .line 3
    invoke-static {p1, v0, p2, v1}, Landroid/graphics/Bitmap;->createScaledBitmap(Landroid/graphics/Bitmap;IIZ)Landroid/graphics/Bitmap;

    move-result-object p1

    return-object p1
.end method

.method private traverseCloudAnalyzerApi(Ljava/util/List;Ljava/util/Map;Ljava/lang/String;)Lcom/huawei/hms/network/httpclient/Response;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;",
            "Ljava/lang/String;",
            ")",
            "Lcom/huawei/hms/network/httpclient/Response<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .annotation system Ldalvik/annotation/Throws;
        value = {
            Lcom/huawei/hms/mlsdk/common/MLException;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->options:Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->getFontTypeScene()I

    move-result v0

    if-nez v0, :cond_1

    .line 2
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;->options:Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/MLRemoteTextSetting;->getTextDensityScene()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    const-string v0, "v1/image/recognition/ocr/text"

    goto :goto_0

    :cond_0
    const-string v0, "v1/image/recognition/ocr/document"

    goto :goto_0

    :cond_1
    const-string v0, "v1/image/recognition/ocr/handwriting"

    :goto_0
    move-object v4, v0

    .line 13
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->a()Lcom/huawei/hms/mlsdk/internal/client/rest/d;

    move-result-object v1

    const-class v3, Lcom/huawei/hms/mlsdk/cloud/RemoteRequestService;

    const-string v7, "CloudTextAnalyzer"

    move-object v2, p1

    move-object v5, p2

    move-object v6, p3

    invoke-virtual/range {v1 .. v7}, Lcom/huawei/hms/mlsdk/internal/client/rest/d;->a(Ljava/util/List;Ljava/lang/Class;Ljava/lang/String;Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/network/httpclient/Response;

    move-result-object p1

    return-object p1
.end method


# virtual methods
.method public boundingPloyToPoints(Ljava/util/List;F)[Landroid/graphics/Point;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/cloud/Coord;",
            ">;F)[",
            "Landroid/graphics/Point;"
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p1, :cond_1

    .line 1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    move-result v1

    if-nez v1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    .line 3
    new-array v1, v1, [Landroid/graphics/Point;

    .line 4
    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v2

    if-ge v0, v2, :cond_0

    .line 5
    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/cloud/Coord;

    .line 6
    new-instance v3, Landroid/graphics/Point;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/cloud/Coord;->getX()I

    move-result v4

    int-to-float v4, v4

    mul-float/2addr v4, p2

    float-to-int v4, v4

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/cloud/Coord;->getY()I

    move-result v2

    int-to-float v2, v2

    mul-float/2addr v2, p2

    float-to-int v2, v2

    invoke-direct {v3, v4, v2}, Landroid/graphics/Point;-><init>(II)V

    aput-object v3, v1, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-object v1

    :cond_1
    new-array p1, v0, [Landroid/graphics/Point;

    return-object p1
.end method

.method public boundingPloyToPointsFromCoords(Ljava/util/List;F)[Landroid/graphics/Point;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/cloud/Coord;",
            ">;F)[",
            "Landroid/graphics/Point;"
        }
    .end annotation

    const/4 v0, 0x0

    if-eqz p1, :cond_1

    .line 1
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    if-lez v1, :cond_1

    .line 2
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    .line 3
    new-array v1, v1, [Landroid/graphics/Point;

    .line 4
    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v2

    if-ge v0, v2, :cond_0

    .line 5
    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/cloud/Coord;

    .line 6
    new-instance v3, Landroid/graphics/Point;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/cloud/Coord;->getX()I

    move-result v4

    int-to-float v4, v4

    mul-float/2addr v4, p2

    float-to-int v4, v4

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/cloud/Coord;->getY()I

    move-result v2

    int-to-float v2, v2

    mul-float/2addr v2, p2

    float-to-int v2, v2

    invoke-direct {v3, v4, v2}, Landroid/graphics/Point;-><init>(II)V

    aput-object v3, v1, v0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_0
    return-object v1

    :cond_1
    new-array p1, v0, [Landroid/graphics/Point;

    return-object p1
.end method

.method public close()V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    return-void
.end method

.method public processImage(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Lcom/huawei/hmf/tasks/Task<",
            "Lcom/huawei/hms/mlsdk/text/MLText;",
            ">;"
        }
    .end annotation

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;

    invoke-direct {v0, p0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer$1;-><init>(Lcom/huawei/hms/mlsdk/text/internal/client/CloudTextAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;)V

    invoke-static {v0}, Lcom/huawei/hmf/tasks/Tasks;->callInBackground(Ljava/util/concurrent/Callable;)Lcom/huawei/hmf/tasks/Task;

    move-result-object p1

    return-object p1
.end method
