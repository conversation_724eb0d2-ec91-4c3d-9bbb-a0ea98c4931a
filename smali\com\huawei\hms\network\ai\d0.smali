.class public Lcom/huawei/hms/network/ai/d0;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/huawei/hms/network/ai/i;


# static fields
.field public static final x:Ljava/lang/String; = "InitModel"


# instance fields
.field public a:Z

.field public b:J

.field public c:J

.field public d:I

.field public e:F

.field public f:I

.field public g:I

.field public h:I

.field public i:I

.field public j:F

.field public k:F

.field public l:F

.field public m:F

.field public n:F

.field public o:F

.field public p:F

.field public q:I

.field public r:I

.field public s:I

.field public t:I

.field public u:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "[J>;"
        }
    .end annotation
.end field

.field public v:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/<PERSON>an;",
            ">;"
        }
    .end annotation
.end field

.field public w:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 2

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/huawei/hms/network/ai/d0;->a:Z

    const/16 v1, 0x32

    iput v1, p0, Lcom/huawei/hms/network/ai/d0;->d:I

    const/high16 v1, 0x3f400000    # 0.75f

    iput v1, p0, Lcom/huawei/hms/network/ai/d0;->e:F

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->f:I

    const/4 v1, 0x1

    iput v1, p0, Lcom/huawei/hms/network/ai/d0;->g:I

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->h:I

    iput v1, p0, Lcom/huawei/hms/network/ai/d0;->i:I

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->s:I

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->t:I

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    new-instance v0, Ljava/util/LinkedHashMap;

    invoke-direct {v0}, Ljava/util/LinkedHashMap;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/network/ai/d0;->v:Ljava/util/Map;

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/network/ai/d0;->w:Ljava/util/Map;

    return-void
.end method

.method private a(Ljava/lang/String;)F
    .locals 3

    iget v0, p0, Lcom/huawei/hms/network/ai/d0;->o:F

    iget-object v1, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    invoke-virtual {v1, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [J

    iget v1, p0, Lcom/huawei/hms/network/ai/d0;->h:I

    aget-wide v1, p1, v1

    long-to-float p1, v1

    invoke-static {v0, p1}, Ljava/lang/Math;->min(FF)F

    move-result p1

    iget v0, p0, Lcom/huawei/hms/network/ai/d0;->o:F

    div-float/2addr p1, v0

    iget v0, p0, Lcom/huawei/hms/network/ai/d0;->n:F

    iget v1, p0, Lcom/huawei/hms/network/ai/d0;->j:F

    mul-float/2addr v0, v1

    mul-float/2addr p1, v0

    return p1
.end method

.method private a(Ljava/util/Map;Ljava/lang/String;)F
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "[I>;",
            "Ljava/lang/String;",
            ")F"
        }
    .end annotation

    invoke-interface {p1, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    iget v0, p0, Lcom/huawei/hms/network/ai/d0;->p:F

    invoke-interface {p1, p2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [I

    iget p2, p0, Lcom/huawei/hms/network/ai/d0;->g:I

    aget p1, p1, p2

    int-to-float p1, p1

    invoke-static {v0, p1}, Ljava/lang/Math;->min(FF)F

    move-result p1

    iget p2, p0, Lcom/huawei/hms/network/ai/d0;->p:F

    div-float/2addr p1, p2

    iget p2, p0, Lcom/huawei/hms/network/ai/d0;->n:F

    iget v0, p0, Lcom/huawei/hms/network/ai/d0;->l:F

    mul-float/2addr p2, v0

    mul-float/2addr p1, p2

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public static synthetic a(Lcom/huawei/hms/network/ai/d0;Lcom/huawei/hms/network/ai/l;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/d0;->c(Lcom/huawei/hms/network/ai/l;)V

    return-void
.end method

.method public static synthetic a(Lcom/huawei/hms/network/ai/d0;Lcom/huawei/hms/network/inner/api/RequestContext;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/d0;->b(Lcom/huawei/hms/network/inner/api/RequestContext;)V

    return-void
.end method

.method private b(Ljava/lang/String;)F
    .locals 4

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    invoke-virtual {v0, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [J

    iget v0, p0, Lcom/huawei/hms/network/ai/d0;->i:I

    aget-wide v0, p1, v0

    const-wide/16 v2, 0x3e8

    div-long/2addr v0, v2

    long-to-float p1, v0

    iget-wide v0, p0, Lcom/huawei/hms/network/ai/d0;->c:J

    div-long/2addr v0, v2

    long-to-float v0, v0

    div-float/2addr p1, v0

    const/high16 v0, 0x3f800000    # 1.0f

    sub-float/2addr v0, p1

    iget p1, p0, Lcom/huawei/hms/network/ai/d0;->n:F

    iget v1, p0, Lcom/huawei/hms/network/ai/d0;->k:F

    mul-float/2addr p1, v1

    mul-float/2addr v0, p1

    return v0
.end method

.method private b(Ljava/util/Map;Ljava/lang/String;)F
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "[I>;",
            "Ljava/lang/String;",
            ")F"
        }
    .end annotation

    invoke-direct {p0, p2}, Lcom/huawei/hms/network/ai/d0;->a(Ljava/lang/String;)F

    move-result v0

    invoke-direct {p0, p2}, Lcom/huawei/hms/network/ai/d0;->b(Ljava/lang/String;)F

    move-result v1

    add-float/2addr v0, v1

    invoke-direct {p0, p1, p2}, Lcom/huawei/hms/network/ai/d0;->a(Ljava/util/Map;Ljava/lang/String;)F

    move-result p1

    add-float/2addr v0, p1

    iget p1, p0, Lcom/huawei/hms/network/ai/d0;->m:F

    add-float/2addr v0, p1

    return v0
.end method

.method private b(Lcom/huawei/hms/network/ai/l;)V
    .locals 2

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->v:Ljava/util/Map;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/l;->a()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->v:Ljava/util/Map;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/l;->a()Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x1

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    invoke-interface {v0, p1, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    return-void
.end method

.method private b(Lcom/huawei/hms/network/inner/api/RequestContext;)V
    .locals 4

    invoke-virtual {p0}, Lcom/huawei/hms/network/ai/d0;->d()Z

    move-result v0

    if-eqz v0, :cond_0

    const-string p1, "InitModel"

    const-string v0, "Initmodel train finished"

    invoke-static {p1, v0}, Lcom/huawei/hms/framework/common/Logger;->d(Ljava/lang/String;Ljava/lang/Object;)V

    return-void

    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/network/inner/api/RequestContext;->requestFinishedInfo()Lcom/huawei/hms/network/httpclient/RequestFinishedInfo;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/RequestFinishedInfo;->getMetricsRealTime()Lcom/huawei/hms/network/httpclient/RequestFinishedInfo$MetricsTime;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/network/httpclient/RequestFinishedInfo$MetricsTime;->getConnectEndTime()J

    move-result-wide v0

    invoke-virtual {p1}, Lcom/huawei/hms/network/inner/api/RequestContext;->requestFinishedInfo()Lcom/huawei/hms/network/httpclient/RequestFinishedInfo;

    move-result-object v2

    invoke-virtual {v2}, Lcom/huawei/hms/network/httpclient/RequestFinishedInfo;->getMetricsRealTime()Lcom/huawei/hms/network/httpclient/RequestFinishedInfo$MetricsTime;

    move-result-object v2

    invoke-virtual {v2}, Lcom/huawei/hms/network/httpclient/RequestFinishedInfo$MetricsTime;->getConnectStartTime()J

    move-result-wide v2

    sub-long/2addr v0, v2

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    if-nez v0, :cond_1

    iget v0, p0, Lcom/huawei/hms/network/ai/d0;->t:I

    const/4 v1, 0x1

    add-int/2addr v0, v1

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->t:I

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->w:Ljava/util/Map;

    invoke-virtual {p1}, Lcom/huawei/hms/network/inner/api/RequestContext;->request()Lcom/huawei/hms/network/httpclient/Request;

    move-result-object v2

    invoke-virtual {v2}, Lcom/huawei/hms/network/httpclient/Request;->getUrl()Ljava/lang/String;

    move-result-object v2

    invoke-interface {v0, v2}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->w:Ljava/util/Map;

    invoke-virtual {p1}, Lcom/huawei/hms/network/inner/api/RequestContext;->request()Lcom/huawei/hms/network/httpclient/Request;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/network/httpclient/Request;->getUrl()Ljava/lang/String;

    move-result-object p1

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->w:Ljava/util/Map;

    invoke-virtual {p1}, Lcom/huawei/hms/network/inner/api/RequestContext;->request()Lcom/huawei/hms/network/httpclient/Request;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/network/httpclient/Request;->getUrl()Ljava/lang/String;

    move-result-object v1

    invoke-interface {v0, v1}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->w:Ljava/util/Map;

    invoke-virtual {p1}, Lcom/huawei/hms/network/inner/api/RequestContext;->request()Lcom/huawei/hms/network/httpclient/Request;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/network/httpclient/Request;->getUrl()Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x0

    :goto_0
    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    invoke-interface {v0, p1, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_2
    return-void
.end method

.method private c(Lcom/huawei/hms/network/ai/l;)V
    .locals 9

    invoke-virtual {p0}, Lcom/huawei/hms/network/ai/d0;->d()Z

    move-result v0

    const-string v1, "InitModel"

    if-eqz v0, :cond_0

    const-string p1, "Initmodel train finished"

    invoke-static {v1, p1}, Lcom/huawei/hms/framework/common/Logger;->d(Ljava/lang/String;Ljava/lang/Object;)V

    return-void

    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/l;->b()J

    move-result-wide v2

    iget-wide v4, p0, Lcom/huawei/hms/network/ai/d0;->b:J

    sub-long/2addr v2, v4

    iget-wide v4, p0, Lcom/huawei/hms/network/ai/d0;->c:J

    cmp-long v0, v2, v4

    const/4 v2, 0x1

    if-gez v0, :cond_3

    iget v0, p0, Lcom/huawei/hms/network/ai/d0;->s:I

    add-int/2addr v0, v2

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->s:I

    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/d0;->b(Lcom/huawei/hms/network/ai/l;)V

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "add a train url "

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/l;->a()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/huawei/hms/framework/common/Logger;->d(Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/l;->a()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/util/HashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    const-wide/16 v3, 0x1

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/l;->a()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [J

    iget v1, p0, Lcom/huawei/hms/network/ai/d0;->h:I

    aget-wide v5, v0, v1

    add-long/2addr v3, v5

    :cond_1
    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/l;->a()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/util/HashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v0

    const/4 v1, 0x0

    const/4 v5, 0x2

    if-nez v0, :cond_2

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/l;->a()Ljava/lang/String;

    move-result-object v6

    new-array v5, v5, [J

    aput-wide v3, v5, v1

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/l;->b()J

    move-result-wide v3

    iget-wide v7, p0, Lcom/huawei/hms/network/ai/d0;->b:J

    sub-long/2addr v3, v7

    aput-wide v3, v5, v2

    invoke-virtual {v0, v6, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_2
    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/l;->a()Ljava/lang/String;

    move-result-object v6

    new-array v5, v5, [J

    aput-wide v3, v5, v1

    iget-object v1, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/l;->a()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [J

    iget v1, p0, Lcom/huawei/hms/network/ai/d0;->i:I

    aget-wide v3, p1, v1

    aput-wide v3, v5, v2

    invoke-virtual {v0, v6, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_3
    const-string p1, "update train data"

    invoke-static {v1, p1}, Lcom/huawei/hms/framework/common/Logger;->d(Ljava/lang/String;Ljava/lang/Object;)V

    iput-boolean v2, p0, Lcom/huawei/hms/network/ai/d0;->a:Z

    invoke-direct {p0}, Lcom/huawei/hms/network/ai/d0;->i()V

    invoke-virtual {p0}, Lcom/huawei/hms/network/ai/d0;->g()V

    :goto_0
    return-void
.end method

.method private h()[F
    .locals 9

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->v:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->size()I

    move-result v0

    iget v1, p0, Lcom/huawei/hms/network/ai/d0;->r:I

    invoke-static {v0, v1}, Ljava/lang/Math;->min(II)I

    move-result v0

    iget-object v1, p0, Lcom/huawei/hms/network/ai/d0;->v:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    const/4 v2, 0x0

    const/4 v3, 0x0

    move v4, v2

    move v5, v3

    move v6, v5

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    const/high16 v8, 0x3f800000    # 1.0f

    if-eqz v7, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/util/Map$Entry;

    invoke-interface {v7}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/lang/Boolean;

    invoke-virtual {v7}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v7

    if-eqz v7, :cond_0

    add-float/2addr v5, v8

    if-ge v4, v0, :cond_0

    add-float/2addr v6, v8

    :cond_0
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    :cond_1
    iget-object v1, p0, Lcom/huawei/hms/network/ai/d0;->w:Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    move v4, v3

    :cond_2
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_3

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/util/Map$Entry;

    invoke-interface {v7}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/lang/Boolean;

    invoke-virtual {v7}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v7

    if-eqz v7, :cond_2

    add-float/2addr v4, v8

    goto :goto_1

    :cond_3
    const/4 v1, 0x3

    new-array v1, v1, [F

    const/high16 v7, 0x42c80000    # 100.0f

    if-lez v0, :cond_4

    int-to-float v0, v0

    div-float/2addr v6, v0

    mul-float/2addr v6, v7

    invoke-static {v6}, Ljava/lang/Math;->round(F)I

    move-result v0

    int-to-float v0, v0

    div-float/2addr v0, v7

    goto :goto_2

    :cond_4
    move v0, v3

    :goto_2
    aput v0, v1, v2

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->v:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->size()I

    move-result v0

    if-lez v0, :cond_5

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->v:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->size()I

    move-result v0

    int-to-float v0, v0

    div-float/2addr v5, v0

    mul-float/2addr v5, v7

    invoke-static {v5}, Ljava/lang/Math;->round(F)I

    move-result v0

    int-to-float v0, v0

    div-float/2addr v0, v7

    goto :goto_3

    :cond_5
    move v0, v3

    :goto_3
    const/4 v2, 0x1

    aput v0, v1, v2

    const/4 v0, 0x2

    iget-object v2, p0, Lcom/huawei/hms/network/ai/d0;->w:Ljava/util/Map;

    invoke-interface {v2}, Ljava/util/Map;->size()I

    move-result v2

    if-lez v2, :cond_6

    iget-object v2, p0, Lcom/huawei/hms/network/ai/d0;->w:Ljava/util/Map;

    invoke-interface {v2}, Ljava/util/Map;->size()I

    move-result v2

    int-to-float v2, v2

    div-float/2addr v4, v2

    mul-float/2addr v4, v7

    invoke-static {v4}, Ljava/lang/Math;->round(F)I

    move-result v2

    int-to-float v2, v2

    div-float v3, v2, v7

    :cond_6
    aput v3, v1, v0

    return-object v1
.end method

.method private i()V
    .locals 4

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->v:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->size()I

    move-result v0

    if-eqz v0, :cond_1

    invoke-direct {p0}, Lcom/huawei/hms/network/ai/d0;->h()[F

    move-result-object v0

    new-instance v1, Ljava/util/HashMap;

    invoke-direct {v1}, Ljava/util/HashMap;-><init>()V

    const-string v2, "model_type"

    const-string v3, "init.model"

    invoke-interface {v1, v2, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v2, 0x0

    aget v2, v0, v2

    invoke-static {v2}, Ljava/lang/String;->valueOf(F)Ljava/lang/String;

    move-result-object v2

    const-string v3, "request_accuracy"

    invoke-interface {v1, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v2, 0x1

    aget v2, v0, v2

    invoke-static {v2}, Ljava/lang/String;->valueOf(F)Ljava/lang/String;

    move-result-object v2

    const-string v3, "dns_accuracy"

    invoke-interface {v1, v3, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v2, 0x2

    aget v0, v0, v2

    invoke-static {v0}, Ljava/lang/String;->valueOf(F)Ljava/lang/String;

    move-result-object v0

    const-string v2, "domain_accuracy"

    invoke-interface {v1, v2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->v:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->size()I

    move-result v0

    iget v2, p0, Lcom/huawei/hms/network/ai/d0;->r:I

    invoke-static {v0, v2}, Ljava/lang/Math;->min(II)I

    move-result v0

    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v0

    const-string v2, "connect_count"

    invoke-interface {v1, v2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->v:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->size()I

    move-result v0

    invoke-static {v0}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    move-result-object v0

    const-string v2, "dns_count"

    invoke-interface {v1, v2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget v0, p0, Lcom/huawei/hms/network/ai/d0;->s:I

    if-nez v0, :cond_0

    const-string v0, "0"

    goto :goto_0

    :cond_0
    iget v2, p0, Lcom/huawei/hms/network/ai/d0;->t:I

    int-to-float v2, v2

    int-to-float v0, v0

    div-float/2addr v2, v0

    const/high16 v0, 0x42c80000    # 100.0f

    mul-float/2addr v2, v0

    invoke-static {v2}, Ljava/lang/Math;->round(F)I

    move-result v2

    int-to-float v2, v2

    div-float/2addr v2, v0

    invoke-static {v2}, Ljava/lang/String;->valueOf(F)Ljava/lang/String;

    move-result-object v0

    :goto_0
    const-string v2, "real_request_accuracy"

    invoke-interface {v1, v2, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-static {v1}, Lcom/huawei/hms/network/ai/j;->a(Ljava/util/Map;)V

    :cond_1
    return-void
.end method


# virtual methods
.method public a()V
    .locals 2

    invoke-virtual {p0}, Lcom/huawei/hms/network/ai/d0;->f()V

    invoke-static {}, Lcom/huawei/hms/network/ai/e;->a()Lcom/huawei/hms/network/ai/e;

    move-result-object v0

    new-instance v1, Lcom/huawei/hms/network/ai/d0$a;

    invoke-direct {v1, p0}, Lcom/huawei/hms/network/ai/d0$a;-><init>(Lcom/huawei/hms/network/ai/d0;)V

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/e;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public a(Lcom/huawei/hms/network/ai/l;)V
    .locals 2

    invoke-static {}, Lcom/huawei/hms/network/ai/e;->a()Lcom/huawei/hms/network/ai/e;

    move-result-object v0

    new-instance v1, Lcom/huawei/hms/network/ai/d0$c;

    invoke-direct {v1, p0, p1}, Lcom/huawei/hms/network/ai/d0$c;-><init>(Lcom/huawei/hms/network/ai/d0;Lcom/huawei/hms/network/ai/l;)V

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/e;->a(Ljava/lang/Runnable;)V

    return-void
.end method

.method public a(Lcom/huawei/hms/network/inner/api/RequestContext;)V
    .locals 2

    invoke-virtual {p1}, Lcom/huawei/hms/network/inner/api/RequestContext;->throwable()Ljava/lang/Throwable;

    move-result-object v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-static {}, Lcom/huawei/hms/network/ai/e;->a()Lcom/huawei/hms/network/ai/e;

    move-result-object v0

    new-instance v1, Lcom/huawei/hms/network/ai/d0$b;

    invoke-direct {v1, p0, p1}, Lcom/huawei/hms/network/ai/d0$b;-><init>(Lcom/huawei/hms/network/ai/d0;Lcom/huawei/hms/network/inner/api/RequestContext;)V

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/e;->a(Ljava/lang/Runnable;)V

    return-void
.end method

.method public b()V
    .locals 0

    return-void
.end method

.method public c()V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->clear()V

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->v:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->clear()V

    iget-object v0, p0, Lcom/huawei/hms/network/ai/d0;->w:Ljava/util/Map;

    invoke-interface {v0}, Ljava/util/Map;->clear()V

    return-void
.end method

.method public d()Z
    .locals 1

    iget-boolean v0, p0, Lcom/huawei/hms/network/ai/d0;->a:Z

    return v0
.end method

.method public e()V
    .locals 8

    const-string v0, "InitModel"

    const-string v1, "InitModule start execute"

    invoke-static {v0, v1}, Lcom/huawei/hms/framework/common/Logger;->i(Ljava/lang/String;Ljava/lang/Object;)V

    invoke-static {}, Lcom/huawei/hms/network/ai/d;->c()Lcom/huawei/hms/network/ai/d;

    move-result-object v1

    const-string v2, "init.model"

    invoke-virtual {v1, v2}, Lcom/huawei/hms/network/ai/d;->b(Ljava/lang/String;)Lcom/huawei/hms/network/ai/g;

    move-result-object v1

    invoke-interface {v1}, Lcom/huawei/hms/network/ai/g;->a()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/ArrayList;

    invoke-virtual {v1}, Ljava/util/ArrayList;->isEmpty()Z

    move-result v2

    if-eqz v2, :cond_0

    return-void

    :cond_0
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "preFetchList size: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v0, v2}, Lcom/huawei/hms/framework/common/Logger;->i(Ljava/lang/String;Ljava/lang/Object;)V

    invoke-static {}, Lcom/huawei/hms/network/inner/api/NetworkKitInnerImpl;->getInstance()Lcom/huawei/hms/network/inner/api/NetworkKitInnerImpl;

    move-result-object v2

    const-string v3, "dns"

    invoke-virtual {v2, v3}, Lcom/huawei/hms/network/inner/api/NetworkKitInnerImpl;->getService(Ljava/lang/String;)Lcom/huawei/hms/network/inner/api/NetworkService;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/network/inner/api/DnsNetworkService;

    const/4 v3, 0x0

    move v4, v3

    :goto_0
    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v5

    iget v6, p0, Lcom/huawei/hms/network/ai/d0;->q:I

    invoke-static {v5, v6}, Ljava/lang/Math;->min(II)I

    move-result v5

    if-ge v4, v5, :cond_4

    invoke-virtual {v1, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/CharSequence;

    invoke-static {v5}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v5

    if-nez v5, :cond_3

    invoke-virtual {v1}, Ljava/util/ArrayList;->size()I

    move-result v5

    iget v6, p0, Lcom/huawei/hms/network/ai/d0;->r:I

    invoke-static {v5, v6}, Ljava/lang/Math;->min(II)I

    move-result v5

    if-ge v4, v5, :cond_1

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "Pre Connect : https://"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-static {v0, v5}, Lcom/huawei/hms/framework/common/Logger;->v(Ljava/lang/String;Ljava/lang/Object;)V

    invoke-static {}, Lcom/huawei/hms/network/httpclient/util/PreConnectManager;->getInstance()Lcom/huawei/hms/network/httpclient/util/PreConnectManager;

    move-result-object v5

    invoke-virtual {v1, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    new-instance v7, Lcom/huawei/hms/network/httpclient/util/PreConnectManager$ConnectCallBack;

    invoke-direct {v7}, Lcom/huawei/hms/network/httpclient/util/PreConnectManager$ConnectCallBack;-><init>()V

    invoke-virtual {v5, v6, v7}, Lcom/huawei/hms/network/httpclient/util/PreConnectManager;->connect(Ljava/lang/String;Lcom/huawei/hms/network/httpclient/Callback;)V

    goto :goto_1

    :cond_1
    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "Pre DNS : https://"

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/String;

    invoke-virtual {v5, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-static {v0, v5}, Lcom/huawei/hms/framework/common/Logger;->v(Ljava/lang/String;Ljava/lang/Object;)V

    if-eqz v2, :cond_2

    invoke-virtual {v1, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/String;

    invoke-virtual {v2, v5}, Lcom/huawei/hms/network/inner/api/DnsNetworkService;->dnsPrefetch(Ljava/lang/String;)V

    :cond_2
    :goto_1
    iget-object v5, p0, Lcom/huawei/hms/network/ai/d0;->v:Ljava/util/Map;

    invoke-virtual {v1, v4}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v6

    invoke-static {v3}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v7

    invoke-interface {v5, v6, v7}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_3
    add-int/lit8 v4, v4, 0x1

    goto/16 :goto_0

    :cond_4
    return-void
.end method

.method public f()V
    .locals 2

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/huawei/hms/network/ai/d0;->b:J

    const-wide/32 v0, 0x493e0

    iput-wide v0, p0, Lcom/huawei/hms/network/ai/d0;->c:J

    const/16 v0, 0x32

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->d:I

    const v0, 0x3f333333    # 0.7f

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->e:F

    const v0, 0x3ecccccd    # 0.4f

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->j:F

    const v1, 0x3e4ccccd    # 0.2f

    iput v1, p0, Lcom/huawei/hms/network/ai/d0;->k:F

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->l:F

    const/high16 v0, 0x42480000    # 50.0f

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->m:F

    const/high16 v1, 0x41f00000    # 30.0f

    iput v1, p0, Lcom/huawei/hms/network/ai/d0;->o:F

    const/high16 v1, 0x41200000    # 10.0f

    iput v1, p0, Lcom/huawei/hms/network/ai/d0;->p:F

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->n:F

    const/16 v0, 0xa

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->q:I

    const/4 v0, 0x5

    iput v0, p0, Lcom/huawei/hms/network/ai/d0;->r:I

    return-void
.end method

.method public g()V
    .locals 10

    invoke-static {}, Lcom/huawei/hms/network/ai/d;->c()Lcom/huawei/hms/network/ai/d;

    move-result-object v0

    const-string v1, "init.model"

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/d;->b(Ljava/lang/String;)Lcom/huawei/hms/network/ai/g;

    move-result-object v0

    invoke-interface {v0}, Lcom/huawei/hms/network/ai/g;->b()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map;

    invoke-interface {v1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    const/high16 v4, 0x3f800000    # 1.0f

    const/4 v5, 0x0

    const/4 v6, 0x1

    if-eqz v3, :cond_2

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/util/Map$Entry;

    invoke-interface {v3}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Ljava/lang/String;

    iget-object v8, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    invoke-virtual {v8, v7}, Ljava/util/HashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v8

    invoke-interface {v3}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, [I

    if-eqz v8, :cond_0

    iget v5, p0, Lcom/huawei/hms/network/ai/d0;->g:I

    aget v8, v3, v5

    add-int/2addr v8, v6

    aput v8, v3, v5

    goto :goto_1

    :cond_0
    iget v6, p0, Lcom/huawei/hms/network/ai/d0;->g:I

    aput v5, v3, v6

    :goto_1
    const/4 v3, 0x0

    iget-object v5, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    invoke-virtual {v5, v7}, Ljava/util/HashMap;->containsKey(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_1

    invoke-direct {p0, v1, v7}, Lcom/huawei/hms/network/ai/d0;->b(Ljava/util/Map;Ljava/lang/String;)F

    move-result v3

    :cond_1
    invoke-interface {v1, v7}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, [I

    iget v6, p0, Lcom/huawei/hms/network/ai/d0;->f:I

    aget v5, v5, v6

    invoke-interface {v1, v7}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, [I

    iget v7, p0, Lcom/huawei/hms/network/ai/d0;->f:I

    iget v8, p0, Lcom/huawei/hms/network/ai/d0;->e:F

    sub-float/2addr v4, v8

    mul-float/2addr v3, v4

    int-to-float v4, v5

    mul-float/2addr v8, v4

    add-float/2addr v3, v8

    float-to-int v3, v3

    aput v3, v6, v7

    goto :goto_0

    :cond_2
    iget-object v2, p0, Lcom/huawei/hms/network/ai/d0;->u:Ljava/util/HashMap;

    invoke-virtual {v2}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :cond_3
    :goto_2
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_4

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/String;

    invoke-interface {v1, v3}, Ljava/util/Map;->containsKey(Ljava/lang/Object;)Z

    move-result v7

    if-nez v7, :cond_3

    invoke-direct {p0, v1, v3}, Lcom/huawei/hms/network/ai/d0;->b(Ljava/util/Map;Ljava/lang/String;)F

    move-result v7

    iget v8, p0, Lcom/huawei/hms/network/ai/d0;->e:F

    sub-float v9, v4, v8

    mul-float/2addr v7, v9

    iget v9, p0, Lcom/huawei/hms/network/ai/d0;->d:I

    int-to-float v9, v9

    mul-float/2addr v8, v9

    add-float/2addr v7, v8

    float-to-int v7, v7

    const/4 v8, 0x2

    new-array v8, v8, [I

    aput v7, v8, v5

    aput v6, v8, v6

    invoke-interface {v1, v3, v8}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    :cond_4
    invoke-interface {v0, v1}, Lcom/huawei/hms/network/ai/g;->a(Ljava/lang/Object;)V

    return-void
.end method
