.class public Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;
.super Ljava/lang/Object;
.source "MLRemoteLandmarkAnalyzerSetting.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Factory"
.end annotation


# instance fields
.field private enforceCertFingerprintMatch:Z

.field private maxResults:I

.field private modelType:I


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/16 v0, 0xa

    .line 2
    iput v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;->maxResults:I

    const/4 v0, 0x1

    .line 3
    iput v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;->modelType:I

    const/4 v0, 0x0

    .line 4
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;->enforceCertFingerprintMatch:Z

    return-void
.end method


# virtual methods
.method public create()Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;
    .locals 5

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;

    iget v1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;->maxResults:I

    iget v2, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;->modelType:I

    iget-boolean v3, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;->enforceCertFingerprintMatch:Z

    const/4 v4, 0x0

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting;-><init>(IIZLcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$1;)V

    return-object v0
.end method

.method public enableFingerprintVerification()Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;
    .locals 1

    const/4 v0, 0x1

    .line 1
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;->enforceCertFingerprintMatch:Z

    return-object p0
.end method

.method public setLargestNumOfReturns(I)Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;->maxResults:I

    return-object p0
.end method

.method public setPatternType(I)Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;
    .locals 0

    .line 1
    iput p1, p0, Lcom/huawei/hms/mlsdk/landmark/MLRemoteLandmarkAnalyzerSetting$Factory;->modelType:I

    return-object p0
.end method
