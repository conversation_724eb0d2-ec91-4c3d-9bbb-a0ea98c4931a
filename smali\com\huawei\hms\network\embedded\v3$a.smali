.class public Lcom/huawei/hms/network/embedded/v3$a;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/huawei/hms/network/embedded/v3;-><init>(Lcom/huawei/hms/network/embedded/h1$d;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Lcom/huawei/hms/network/embedded/v3;


# direct methods
.method public constructor <init>(Lcom/huawei/hms/network/embedded/v3;)V
    .locals 0

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/v3$a;->a:Lcom/huawei/hms/network/embedded/v3;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public run()V
    .locals 5

    const-string v0, "An IOException occurs during stream writing."

    const-string v1, "CronetUploadDataProvide"

    new-instance v2, Lcom/huawei/hms/network/embedded/u3;

    iget-object v3, p0, Lcom/huawei/hms/network/embedded/v3$a;->a:Lcom/huawei/hms/network/embedded/v3;

    invoke-static {v3}, Lcom/huawei/hms/network/embedded/v3;->a(Lcom/huawei/hms/network/embedded/v3;)Ljava/util/concurrent/LinkedBlockingQueue;

    move-result-object v3

    invoke-direct {v2, v3}, Lcom/huawei/hms/network/embedded/u3;-><init>(Ljava/util/concurrent/LinkedBlockingQueue;)V

    const/4 v3, 0x1

    :try_start_0
    iget-object v4, p0, Lcom/huawei/hms/network/embedded/v3$a;->a:Lcom/huawei/hms/network/embedded/v3;

    invoke-static {v4}, Lcom/huawei/hms/network/embedded/v3;->b(Lcom/huawei/hms/network/embedded/v3;)Lcom/huawei/hms/network/embedded/h1$e;

    move-result-object v4

    invoke-virtual {v4, v2}, Lcom/huawei/hms/network/embedded/h1$e;->writeTo(Ljava/io/OutputStream;)V
    :try_end_0
    .catch Ljava/io/FileNotFoundException; {:try_start_0 .. :try_end_0} :catch_2
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    const/4 v3, 0x0

    goto :goto_1

    :catch_0
    move-exception v2

    goto :goto_0

    :catch_1
    move-exception v2

    :goto_0
    invoke-static {v1, v0, v2}, Lcom/huawei/hms/framework/common/Logger;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    goto :goto_1

    :catch_2
    invoke-static {v1, v0}, Lcom/huawei/hms/framework/common/Logger;->w(Ljava/lang/String;Ljava/lang/Object;)V

    :goto_1
    if-eqz v3, :cond_0

    :try_start_1
    iget-object v2, p0, Lcom/huawei/hms/network/embedded/v3$a;->a:Lcom/huawei/hms/network/embedded/v3;

    invoke-static {v2}, Lcom/huawei/hms/network/embedded/v3;->a(Lcom/huawei/hms/network/embedded/v3;)Ljava/util/concurrent/LinkedBlockingQueue;

    move-result-object v2

    invoke-static {}, Lcom/huawei/hms/network/embedded/v3;->s()[B

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/util/concurrent/LinkedBlockingQueue;->put(Ljava/lang/Object;)V
    :try_end_1
    .catch Ljava/lang/InterruptedException; {:try_start_1 .. :try_end_1} :catch_3

    goto :goto_2

    :catch_3
    move-exception v2

    invoke-static {v1, v0, v2}, Lcom/huawei/hms/framework/common/Logger;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V

    :cond_0
    :goto_2
    return-void
.end method
