.class public Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;
.super Ljava/lang/Object;
.source "NativeTextAnalyzer.java"


# instance fields
.field private final detectKey:Ljava/lang/String;

.field private final hmsMLApp:Lcom/huawei/hms/mlsdk/common/MLApplication;

.field private final mContext:Landroid/content/Context;

.field private final settings:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, "isDetect"

    .line 2
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->detectKey:Ljava/lang/String;

    .line 4
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->mContext:Landroid/content/Context;

    .line 5
    invoke-static {p1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->initialize(Landroid/content/Context;)Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v0

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->hmsMLApp:Lcom/huawei/hms/mlsdk/common/MLApplication;

    if-nez p2, :cond_0

    .line 7
    new-instance p2, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;

    invoke-direct {p2}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;-><init>()V

    invoke-virtual {p2}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->create()Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    move-result-object p2

    iput-object p2, p0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->settings:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    goto :goto_0

    .line 9
    :cond_0
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->settings:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    .line 11
    :goto_0
    invoke-virtual {p0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->prepare(Landroid/content/Context;)V

    return-void
.end method

.method private analyseImage(Lcom/huawei/hms/mlsdk/common/MLFrame;Z)Landroid/util/SparseArray;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            "Z)",
            "Landroid/util/SparseArray<",
            "Lcom/huawei/hms/mlsdk/text/MLText$Block;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->isAvailable()Z

    move-result v0

    if-nez v0, :cond_0

    .line 2
    new-instance p1, Landroid/util/SparseArray;

    invoke-direct {p1}, Landroid/util/SparseArray;-><init>()V

    return-object p1

    :cond_0
    if-eqz p1, :cond_2

    .line 9
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->hmsMLApp:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplication;->toBundle()Landroid/os/Bundle;

    move-result-object v0

    const-string v1, "isDetect"

    .line 10
    invoke-virtual {v0, v1, p2}, Landroid/os/Bundle;->putInt(Ljava/lang/String;I)V

    .line 11
    new-instance p2, Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->settings:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->getLanguage()Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->settings:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->getOCRMode()I

    move-result v2

    iget-object v3, p0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->settings:Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    invoke-virtual {v3}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;->getPlateEnable()I

    move-result v3

    invoke-direct {p2, v1, v2, v3, v0}, Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;-><init>(Ljava/lang/String;IILandroid/os/Bundle;)V

    .line 14
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->getInstance()Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;

    move-result-object v1

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->mContext:Landroid/content/Context;

    invoke-static {p1}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->convert(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;

    move-result-object p1

    invoke-virtual {v1, v2, v0, p1, p2}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->recognizeWithBitmap(Landroid/content/Context;Landroid/os/Bundle;Lcom/huawei/hms/ml/common/ocr/TextDetectorFrameParcel;Lcom/huawei/hms/ml/common/ocr/TextDetectorOptionsParcel;)Lcom/huawei/hms/ml/common/ocr/TextParcel;

    move-result-object p1

    .line 15
    invoke-static {p1}, Lcom/huawei/hms/mlsdk/text/internal/client/TextUtils;->textParcelToBlockList(Lcom/huawei/hms/ml/common/ocr/TextParcel;)Ljava/util/List;

    move-result-object p1

    .line 16
    new-instance p2, Landroid/util/SparseArray;

    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v0

    invoke-direct {p2, v0}, Landroid/util/SparseArray;-><init>(I)V

    const/4 v0, 0x0

    .line 17
    :goto_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_1

    .line 18
    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;

    .line 19
    new-instance v9, Lcom/huawei/hms/mlsdk/text/MLText$Block;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getStringValue()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getBorder()Landroid/graphics/Rect;

    move-result-object v4

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getLanguageList()Ljava/util/List;

    move-result-object v5

    .line 20
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;->getContents()Ljava/util/List;

    move-result-object v6

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getVertexes()[Landroid/graphics/Point;

    move-result-object v7

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getConfidence()Ljava/lang/Float;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Float;->floatValue()F

    move-result v8

    move-object v2, v9

    invoke-direct/range {v2 .. v8}, Lcom/huawei/hms/mlsdk/text/MLText$Block;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V

    .line 21
    invoke-virtual {p2, v0, v9}, Landroid/util/SparseArray;->append(ILjava/lang/Object;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    return-object p2

    .line 26
    :catch_0
    new-instance p1, Landroid/util/SparseArray;

    invoke-direct {p1}, Landroid/util/SparseArray;-><init>()V

    return-object p1

    .line 27
    :cond_2
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string p2, "No frame supplied."

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method


# virtual methods
.method public analyse(Lcom/huawei/hms/mlsdk/common/MLFrame;)Landroid/util/SparseArray;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Landroid/util/SparseArray<",
            "Lcom/huawei/hms/mlsdk/text/MLText$Block;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x0

    .line 1
    invoke-direct {p0, p1, v0}, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->analyseImage(Lcom/huawei/hms/mlsdk/common/MLFrame;Z)Landroid/util/SparseArray;

    move-result-object p1

    return-object p1
.end method

.method public detect(Lcom/huawei/hms/mlsdk/common/MLFrame;)Landroid/util/SparseArray;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            ")",
            "Landroid/util/SparseArray<",
            "Lcom/huawei/hms/mlsdk/text/MLText$Block;",
            ">;"
        }
    .end annotation

    const/4 v0, 0x1

    .line 1
    invoke-direct {p0, p1, v0}, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->analyseImage(Lcom/huawei/hms/mlsdk/common/MLFrame;Z)Landroid/util/SparseArray;

    move-result-object p1

    return-object p1
.end method

.method public isAvailable()Z
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->getInstance()Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->hmsMLApp:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->isAvailable(Landroid/content/Context;)Z

    move-result v0

    return v0
.end method

.method public prepare(Landroid/content/Context;)V
    .locals 1

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->getInstance()Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->prepare(Landroid/content/Context;)V

    return-void
.end method

.method public release()V
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->getInstance()Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/internal/client/NativeTextAnalyzer;->hmsMLApp:Lcom/huawei/hms/mlsdk/common/MLApplication;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/mlsdk/text/internal/client/TextAnalyzer;->release(Landroid/content/Context;)V

    return-void
.end method
