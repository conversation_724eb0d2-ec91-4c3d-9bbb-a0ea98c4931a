.class public Lcom/huawei/hms/network/embedded/xa$a;
.super Ljava/io/OutputStream;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/huawei/hms/network/embedded/xa;->g()Ljava/io/OutputStream;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic a:Lcom/huawei/hms/network/embedded/xa;


# direct methods
.method public constructor <init>(Lcom/huawei/hms/network/embedded/xa;)V
    .locals 0

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/xa$a;->a:Lcom/huawei/hms/network/embedded/xa;

    invoke-direct {p0}, Ljava/io/OutputStream;-><init>()V

    return-void
.end method


# virtual methods
.method public close()V
    .locals 0

    return-void
.end method

.method public flush()V
    .locals 0

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lcom/huawei/hms/network/embedded/xa$a;->a:Lcom/huawei/hms/network/embedded/xa;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ".outputStream()"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public write(I)V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/xa$a;->a:Lcom/huawei/hms/network/embedded/xa;

    int-to-byte p1, p1

    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/embedded/xa;->writeByte(I)Lcom/huawei/hms/network/embedded/xa;

    return-void
.end method

.method public write([BII)V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/xa$a;->a:Lcom/huawei/hms/network/embedded/xa;

    invoke-virtual {v0, p1, p2, p3}, Lcom/huawei/hms/network/embedded/xa;->write([BII)Lcom/huawei/hms/network/embedded/xa;

    return-void
.end method
