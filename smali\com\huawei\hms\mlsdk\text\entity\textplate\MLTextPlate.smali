.class public Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;
.super Lcom/huawei/hms/mlsdk/text/MLPlate;
.source "MLTextPlate.java"


# instance fields
.field private blockList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/text/MLPlate;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;->blockList:Ljava/util/List;

    return-void
.end method


# virtual methods
.method public getBlockList()Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;->blockList:Ljava/util/List;

    if-nez v0, :cond_0

    new-instance v0, Ljava/util/ArrayList;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ljava/util/ArrayList;-><init>(I)V

    :cond_0
    return-object v0
.end method

.method public getBorder()Landroid/graphics/Rect;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getConfidence()Ljava/lang/Float;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public getMLPlate()Lcom/huawei/hms/mlsdk/text/MLPlate;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Lcom/huawei/hms/mlsdk/text/MLPlate;",
            ">()TT;"
        }
    .end annotation

    return-object p0
.end method

.method public getMLPlateType()Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;
    .locals 1

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;->TEXT:Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    return-object v0
.end method

.method public getStringValue()Ljava/lang/String;
    .locals 3

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 2
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/entity/textplate/MLTextPlate;->blockList:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_0

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBlock;

    .line 3
    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getStringValue()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {}, Ljava/lang/System;->lineSeparator()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    goto :goto_0

    .line 6
    :cond_0
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->length()I

    move-result v1

    if-nez v1, :cond_1

    const-string v0, ""

    return-object v0

    .line 9
    :cond_1
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->length()I

    move-result v1

    invoke-static {}, Ljava/lang/System;->lineSeparator()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/String;->length()I

    move-result v2

    sub-int/2addr v1, v2

    const/4 v2, 0x0

    invoke-virtual {v0, v2, v1}, Ljava/lang/StringBuilder;->substring(II)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getVertexes()[Landroid/graphics/Point;
    .locals 1

    const/4 v0, 0x0

    new-array v0, v0, [Landroid/graphics/Point;

    return-object v0
.end method
