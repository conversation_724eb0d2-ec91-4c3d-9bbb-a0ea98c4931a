.class public Lcom/huawei/hms/network/embedded/z8$a;
.super Lcom/huawei/hms/network/embedded/va;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/network/embedded/z8;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1
    name = null
.end annotation


# instance fields
.field public final synthetic l:Lcom/huawei/hms/network/embedded/z8;


# direct methods
.method public constructor <init>(Lcom/huawei/hms/network/embedded/z8;)V
    .locals 0

    iput-object p1, p0, Lcom/huawei/hms/network/embedded/z8$a;->l:Lcom/huawei/hms/network/embedded/z8;

    invoke-direct {p0}, Lcom/huawei/hms/network/embedded/va;-><init>()V

    return-void
.end method


# virtual methods
.method public i()V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/z8$a;->l:Lcom/huawei/hms/network/embedded/z8;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/z8;->cancel()V

    return-void
.end method
