<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:contentDescription="@string/apppageflag"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.mxz.hyx.views.TemplateTitle android:id="@id/tt_head" app:canBack="true" app:titleText="参数配置" style="@style/header_style" />
    <ScrollView android:background="@color/white" android:scrollbars="none" android:layout_width="fill_parent" android:layout_height="fill_parent" android:fillViewport="true" android:layout_weight="1.0">
        <LinearLayout android:orientation="vertical" android:id="@id/userinfo_root_layout" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginLeft="10.0dip" android:layout_marginRight="10.0dip">
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="1、设置音量键以及停止按钮的作用" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="音量键加减只对无障碍运行模式有效" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="音量键+" android:layout_weight="1.0" style="@style/addjobname" />
                    <TextView android:id="@id/addkey" android:text="暂停或继续流程" style="@style/addjobbtn" />
                </LinearLayout>
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="音量键—" android:layout_weight="1.0" style="@style/addjobname" />
                    <TextView android:id="@id/cutkey" android:text="停止流程" style="@style/addjobbtn" />
                </LinearLayout>
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="停止按钮" android:layout_weight="1.0" style="@style/addjobname" />
                    <TextView android:id="@id/tingzhikey" android:text="终止当前流程" style="@style/addjobbtn" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent">
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="2、隐藏运行时的停止和暂停中间左侧的文字" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="隐藏后，你可以把停止、暂停按钮拖到边边位置，减少对屏幕的遮挡" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/closetipText" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="3、隐藏运行时的第几步骤（停止和暂停右侧）的文字" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="勾选后，运行框将不会显示当前运行的步骤数以及流程名" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/closetipStepText" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="4、隐藏运行时的日志提示" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="流程运行时一直在刷的那些日志" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/closelog" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="5、设置查看运行日志窗口的日志折叠" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="勾选后，你查看运行日志的时候，单条日志如果超过5行，会自动折叠" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/expandlog" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="6、关闭运行时的文字提示" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="流程运行时一直显示的那个几秒后执行下一个步骤" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/closetip" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="7、按音量键不影响声音大小" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="打开后，按音量键暂停与停止流程时，将不改变声音大小" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/openCloseVoice" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="8、来电时停止流程" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="有电话进来时，流程完全停止" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/phone" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="9、加入忽略电池优化" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="能够有效提升流程在后台运行不被手机自动关闭的概率，当然了，就算你开了这个，一样有概率会被关闭，你还可以在最近任务栏中锁定APP" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/youhua" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="10、关闭运行时点击、滑动提示的指针动画" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="流程运行时一直显示的那个圆形图标指针（可以减少手机运行内存）" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/closepointtip" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="11、关闭运行时倒计时进度动画" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="流程运行时倒计时进度动画，如果你的延迟时间比较久，建议关闭这个动画（可以减少手机运行内存）" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/closeannimotip" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="13、设置子流程脚本的终止流程逻辑" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="打开后，子流程脚本的终止流程只是终止子流程脚本自己，且会回到上级流程继续执行，否则就是整个终止（此配置只对终止流程有效，对终止全部流程无效）" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/openOverChild" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="14、设置运行日志存储数量" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="打开后，有可能运行不持久，日志最大可存储两万行，超过两万行将会自动清除最前面的日志继续往后存储，请注意，如果手机性能一般，那么打开这个开关后有一定的概率会让APP运行不持久，被手机杀掉" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/openSaveLog" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="15、设置流程匹配到多个时的排序方式" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="勾选后，匹配到多个的情况下是先从上到下再从左到右方式排序，默认是先从左到右再从上到下" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/sorttype" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="16、设置图片组高性能模式" android:layout_weight="1.0" />
                        <EditText android:id="@id/imgGroupNumThred" android:hint="多线程数" style="@style/addjobedflow" />
                        <CheckBox android:textColor="@color/black" android:id="@id/imgGroupThred" android:paddingRight="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                    </LinearLayout>
                    <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="勾选后，图片组运行图片匹配的时候会按照你配置的多线程数，启动多个线程进行图片识别，可以大大提高识别速度，多线程数越大（最大50）手机越发热，APP越容易闪退，请酌情选择" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="17、设置启动流程运行后提示框收缩贴边" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="勾选后，在流程开始运行时3秒内没触碰悬浮框并且悬浮框贴近屏幕左边的时候会自动收缩贴到手机屏幕左边呈现一个半圆图标，图标显示黄色代表流程暂停了，蓝色代表运行中，用手滑动一下半圆图标会自动展开回来，如果图标太小触摸没法反应，可以调整下方贴边宽度调大点。" />
                        <LinearLayout android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x5" android:layout_weight="1.0">
                            <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="贴边宽度" />
                            <SeekBar android:id="@id/tiebiansb" android:layout_width="fill_parent" android:layout_height="wrap_content" android:max="40" android:progress="10" android:min="5" />
                        </LinearLayout>
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/tiebian" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
                <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_weight="1.0">
                        <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="18、设置壁纸白名单" />
                        <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="勾选后，运行时能尽可能不被系统清理后台（可能影响手机桌面壁纸显示）" />
                    </LinearLayout>
                    <CheckBox android:textColor="@color/black" android:id="@id/wallset" android:paddingLeft="@dimen/x10" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="24、设置流程列表框默认显示方式" android:layout_weight="1.0" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="显示方式" android:layout_weight="1.0" style="@style/addjobname" />
                    <TextView android:id="@id/defaultStartWin" android:text="展开状态" style="@style/addjobbtn" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="25、设置弹框界面步骤的框默认显示的大小" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="填写百分比,如果弹框界面步骤详情中设置了大小，则按步骤设置的大小为准，否则按此处设置的为准" />
                <LinearLayout android:visibility="visible" android:layout_marginTop="@dimen/x5" style="@style/addjobitemLL">
                    <TextView android:textColor="@color/des_text_color" android:text="宽：" style="@style/addjobname" />
                    <EditText android:id="@id/widthtkwin" android:minWidth="@dimen/x15" style="@style/addjobedflow" />
                    <TextView android:textColor="@color/des_text_color" android:text="%       高：" style="@style/addjobname" />
                    <EditText android:id="@id/heighttkwin" android:minWidth="@dimen/x15" style="@style/addjobedflow" />
                    <TextView android:textColor="@color/des_text_color" android:text="%" style="@style/addjobname" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="26、设置系统提示步骤文字显示的位置" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="系统提示步骤简单提示、全局监控系统提示功能在提示的时候的文字显示的位置" />
                <LinearLayout android:visibility="visible" android:layout_marginTop="@dimen/x5" style="@style/addjobitemLL">
                    <TextView android:textColor="@color/des_text_color" android:text="左边距：" style="@style/addjobname" />
                    <EditText android:id="@id/widthleft" android:minWidth="@dimen/x15" style="@style/addjobedflow" />
                    <TextView android:textColor="@color/des_text_color" android:text="%       上边距：" style="@style/addjobname" />
                    <EditText android:id="@id/heighttop" android:minWidth="@dimen/x15" style="@style/addjobedflow" />
                    <TextView android:textColor="@color/des_text_color" android:text="%" style="@style/addjobname" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="27、设置颜色识别匹配相关功能的像素精度" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="要识别的颜色如果很小，可以调小像素精度，最小为2，最大为20" />
                <LinearLayout android:visibility="visible" android:layout_marginTop="@dimen/x5" style="@style/addjobitemLL">
                    <TextView android:textColor="@color/des_text_color" android:text="像素精度：" style="@style/addjobname" />
                    <EditText android:id="@id/pxnum" android:minWidth="@dimen/x15" android:text="10" android:hint="必填" style="@style/addjobedflow" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="28、设置运行框的透明度" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="运行流程的时候的那个悬浮按钮与日志的透明度，10到100之间，100代表不透明" />
                <LinearLayout android:visibility="visible" android:layout_marginTop="@dimen/x5" style="@style/addjobitemLL">
                    <TextView android:textColor="@color/des_text_color" android:text="透明度：" style="@style/addjobname" />
                    <EditText android:id="@id/floatnum" android:minWidth="@dimen/x15" android:text="100" android:hint="选填" style="@style/addjobedflow" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="29、设置截图的时机" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="如果你运行、添加、测试图片颜色文本等等涉及到需要截图的功能的时候截图经常出现截到了步骤编辑框或运行框的话，可以把截图的时机调大点，默认0.1秒，最大可以填2秒，你可以填0秒代表不延迟，能提高运行流程截图时的效率，但是很可能会出现截图到你的运行框，建议你开启运行框贴边功能，如果你的手机屏幕性能差，建议设置0.2秒，如果你手机性能好，确认不会截图到运行框则可以设置成0秒，有些手机性能差，设置成0秒高频率截屏有可能会出现截屏失败的问题，所以一般情况下建议填0.1妙" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="延迟" style="@style/addjobname" />
                    <EditText android:id="@id/screenOutTime" android:hint="必填" style="@style/addjobedf" />
                    <TextView android:textColor="@color/des_text_color" android:text="秒" style="@style/addjobname" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="30、设置AI智能判断功能的授权秘钥" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:autoLink="web" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="AI智能判断条件和AI智能判断全局监控是请求服务器进行识别的，所以需要配置授权秘钥，秘钥怎么获取可以联系当前流程包的作者" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="秘钥" style="@style/addjobname" />
                    <EditText android:id="@id/aibigmodelkm" android:layout_width="fill_parent" android:layout_marginRight="@dimen/x10" android:hint="请填写秘钥" style="@style/addjobedtv" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="31、设置运行时的停止与暂停按钮在流程进行屏幕操作时是否需要屏蔽可点击" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="作用就是解决有时候流程运行的时候不小心自动点到了停止暂停按钮，也解决有时候你想要点暂停停止按钮一直点不到的问题，当你设置开启运行框自动贴边功能后，此配置将失效，停止与暂停按钮将一直可点击；                     如果设置了不自动隐藏的话，图片、文本识别等等涉及到截屏识别的步骤，会被这个运行时的悬浮框影响到，所以请慎重选择，你可以打开贴边功能，再配合这个不隐藏，把对屏幕识别的相关步骤影响降到最小。" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="屏蔽及隐藏规则" android:layout_weight="1.0" style="@style/addjobname" />
                    <TextView android:id="@id/runbtngz" android:text="自动切换屏蔽" style="@style/addjobbtn" />
                </LinearLayout>
            </LinearLayout>
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="32、设置流程截屏方式" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="一般都是用录屏权限方式，如果你的手机安卓系统版本是13及以上的话，并且使用无障碍模式运行的话，可以使用独家截屏方式；独家截屏方式的好处就是无需截屏权限，提高一些性能" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="屏幕截图方式" android:layout_weight="1.0" style="@style/addjobname" />
                    <TextView android:id="@id/screentype" android:text="录屏权限方式" style="@style/addjobbtn" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="33、设置运行模式" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="无障碍模式、ADB模式、HID模式，三种模式都有什么用可以看帮助页面运行模式的区别，请注意ADB模式你可以选择填写电脑端的IP地址，如果你手机没有ROOT那你必须填，如果你手机已经ROOT那你可以不填。 填的话你电脑要安装【极限投屏】软件，详细做法请看帮助页面运行模式的区别" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="选择模式" android:layout_weight="1.0" style="@style/addjobname" />
                    <TextView android:id="@id/runjobtype" android:text="无障碍模式" style="@style/addjobbtn" />
                </LinearLayout>
                <LinearLayout android:id="@id/runjobhidcjll" android:visibility="gone" android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="选择插件" style="@style/addjobname" />
                    <TextView android:id="@id/runjobhidcj" android:layout_marginRight="@dimen/x10" android:text="请选择" android:layout_weight="1.0" style="@style/addjobbtn" />
                    <TextView android:id="@id/runjobhidcjload" android:text="加载插件" style="@style/addjobbtn" />
                </LinearLayout>
                <LinearLayout android:id="@id/adbipll" android:visibility="gone" android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="电脑IP" style="@style/addjobname" />
                    <EditText android:id="@id/adbip" android:layout_width="fill_parent" android:layout_marginRight="@dimen/x10" android:hint="可选，填电脑局域网IPv4地址" android:digits="0123456789." style="@style/addjobedtv" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="34、设置APP系统权限" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="设置APP允许调整手机屏幕亮度、打开关闭飞行模式、允许读写剪切板等系统设置权限，请注意会打开两个权限页面！" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="有两个权限需要打开" android:layout_weight="1.0" style="@style/addjobname" />
                    <TextView android:id="@id/screenlum" android:text="前往打开权限" style="@style/addjobbtn" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="35、设置APP自带输入法" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="输入内容步骤，输入框使用自带输入法，需要启动输入法并且设置为默认，只要你启动并设置默认输入法后，输入内容步骤就会使用自带输入法来输入内容" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="开启输入法" android:layout_weight="1.0" style="@style/addjobname" />
                    <TextView android:id="@id/openinput" android:text="未开启" style="@style/addjobbtn" />
                </LinearLayout>
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="设置为默认输入法" android:layout_weight="1.0" style="@style/addjobname" />
                    <TextView android:id="@id/opendefinput" android:text="未设置" style="@style/addjobbtn" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="36、设置26个字母的五笔输入法字母位置" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="输入内容步骤，使用五笔输入法方式，很多手机因为隐私问题，不允许对输入法进行截图，造成无法识别字母按键的位置，则建议在这里配置五笔输入法按键的位置，你需要打开26个字母的五笔输入法模式，点击这里的输入框，然后拖动 Q、A、Z、M、L、P、空格键、回车键，进行录入" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <EditText android:id="@id/wubikey" android:layout_width="fill_parent" android:layout_marginRight="@dimen/x10" android:hint="先点击我，然后拖动界面上的字母" style="@style/addjobedtv" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="37、设置流程状态通知的邮箱" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:autoLink="web" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="设置发送方和接收方邮箱，可以用来在流程暂停、继续、停止的时候，发送邮件消息给你填写的接收方邮箱。发送方的邮箱必须是QQ邮箱，接收方的邮箱哪家的都行，邮箱授权码怎么获取可以问流程作者" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="发送方邮箱" style="@style/addjobname" />
                    <EditText android:id="@id/sendQQ" android:layout_width="fill_parent" android:layout_marginRight="@dimen/x10" android:hint="请填写完整的QQ邮箱号码" style="@style/addjobedtv" />
                </LinearLayout>
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="发送方邮箱的授权码" style="@style/addjobname" />
                    <EditText android:id="@id/sendToken" android:layout_width="fill_parent" android:layout_marginRight="@dimen/x10" android:hint="请填写QQ邮箱的授权码" style="@style/addjobedtv" />
                </LinearLayout>
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="接收方邮箱" style="@style/addjobname" />
                    <EditText android:id="@id/toQQ" android:layout_width="fill_parent" android:layout_marginRight="@dimen/x10" android:hint="请填写收消息的邮箱号码" style="@style/addjobedtv" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="38、设置OCR文本识别方式" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="文本识别、文本识别匹配等等涉及到文本相关的功能，如果你想要用别的识别方式，可以在这里选择，默认OCR方式就是APP内置的识别，如果你想换别的OCR识别，可以在这里选择，百度OCR识别插件你需要手机安装标配的插件，这个插件APP可以在群公告链接下载" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="文本识别方式" android:layout_weight="1.0" style="@style/addjobname" />
                    <TextView android:id="@id/ocrtype" android:text="默认OCR方式" style="@style/addjobbtn" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textStyle="bold" android:textColor="@color/main_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="39、设置APP被手机关闭后自动恢复" android:layout_weight="1.0" />
                <TextView android:textColor="@color/des_text_color" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/x10" android:text="这个配置只对无障碍运行模式有效，其他运行模式无效，并且必须要打开APP 自启动权限，自动恢复执行是重头开始执行脚本，只有APP在运行的过程中被手机关闭或者你手动关闭APP才会自动恢复，包括运行中的时候你手机关机了等开机的时候也能自动恢复,当然具体看手机系统有没有限制，APP没在运行中的时候被手机关闭或者你手动关闭APP都不会恢复，恢复后录屏权限会丢失，除非你设置用独家截屏模式" />
                <LinearLayout android:layout_marginTop="@dimen/x5" style="@style/addjobitemLLw">
                    <TextView android:textColor="@color/des_text_color" android:text="恢复方式" android:layout_weight="1.0" style="@style/addjobname" />
                    <TextView android:id="@id/resumetype" android:text="重头执行" style="@style/addjobbtn" />
                </LinearLayout>
            </LinearLayout>
            <View android:background="@color/line" android:layout_width="fill_parent" android:layout_height="@dimen/x1" android:layout_marginTop="@dimen/x8" android:layout_marginBottom="@dimen/x8" />
        </LinearLayout>
    </ScrollView>
    <RelativeLayout android:orientation="vertical" android:id="@id/bannerContainerone" android:layout_width="fill_parent" android:layout_height="wrap_content" />
</LinearLayout>