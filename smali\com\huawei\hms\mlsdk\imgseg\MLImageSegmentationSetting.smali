.class public Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;
.super Ljava/lang/Object;
.source "MLImageSegmentationSetting.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$Factory;
    }
.end annotation


# static fields
.field public static final BODY_SEG:I = 0x0

.field public static final HAIR_SEG:I = 0x2

.field public static final IMAGE_SEG:I = 0x1


# instance fields
.field private final analyzerType:I

.field private final isExact:Z

.field private final scene:I


# direct methods
.method private constructor <init>(IZI)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput p1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->analyzerType:I

    .line 4
    iput-boolean p2, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->isExact:Z

    .line 5
    iput p3, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->scene:I

    return-void
.end method

.method synthetic constructor <init>(IZILcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting$1;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p2, p3}, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;-><init>(IZI)V

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 3

    .line 1
    instance-of v0, p1, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    .line 4
    :cond_0
    check-cast p1, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;

    .line 5
    iget v0, p1, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->analyzerType:I

    iget v2, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->analyzerType:I

    if-ne v0, v2, :cond_1

    iget-boolean v0, p1, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->isExact:Z

    iget-boolean v2, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->isExact:Z

    if-ne v0, v2, :cond_1

    iget p1, p1, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->scene:I

    iget v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->scene:I

    if-ne p1, v0, :cond_1

    const/4 v1, 0x1

    :cond_1
    return v1
.end method

.method public getAnalyzerType()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->analyzerType:I

    return v0
.end method

.method public getScene()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->scene:I

    return v0
.end method

.method public hashCode()I
    .locals 3

    const/4 v0, 0x3

    new-array v0, v0, [Ljava/lang/Object;

    .line 1
    iget v1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->analyzerType:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->isExact:Z

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    iget v1, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->scene:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    invoke-static {v0}, Ljava/util/Arrays;->hashCode([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method public isExact()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationSetting;->isExact:Z

    return v0
.end method
