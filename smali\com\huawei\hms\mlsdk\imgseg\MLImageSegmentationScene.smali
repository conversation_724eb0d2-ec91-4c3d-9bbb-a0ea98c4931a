.class public Lcom/huawei/hms/mlsdk/imgseg/MLImageSegmentationScene;
.super Ljava/lang/Object;
.source "MLImageSegmentationScene.java"


# static fields
.field public static final ALL:I = 0x0

.field public static final FOREGROUND_ONLY:I = 0x2

.field public static final GRAYSCALE_ONLY:I = 0x3

.field public static final MASK_ONLY:I = 0x1


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
