.class public Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;
.super Ljava/lang/Object;
.source "MLFaceAnalyzer.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Factory"
.end annotation

.annotation runtime Ljava/lang/Deprecated;
.end annotation


# instance fields
.field private factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

.field private final mContext:Landroid/content/Context;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-direct {v0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    .line 11
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->mContext:Landroid/content/Context;

    return-void
.end method


# virtual methods
.method public create()Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->create()Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;

    move-result-object v0

    .line 2
    invoke-static {}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->getInstance()Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;

    move-result-object v1

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->mContext:Landroid/content/Context;

    invoke-virtual {v1, v2}, Lcom/huawei/hms/mlsdk/face/internal/client/RemoteFaceDetector;->prepare(Landroid/content/Context;)V

    .line 3
    new-instance v1, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->mContext:Landroid/content/Context;

    const/4 v3, 0x0

    invoke-direct {v1, v2, v0, v3}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer;-><init>(Landroid/content/Context;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting;Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$1;)V

    return-object v1
.end method

.method public setFeatureType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;
    .locals 3

    const/4 v0, 0x2

    if-eq p1, v0, :cond_1

    const/4 v0, 0x1

    if-ne p1, v0, :cond_0

    goto :goto_0

    .line 1
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Invalid feature type: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 3
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->setFeatureType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    return-object p0
.end method

.method public setKeyPointType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;
    .locals 4

    const/4 v0, 0x2

    const/4 v1, 0x1

    if-eqz p1, :cond_1

    if-eq p1, v1, :cond_1

    if-ne p1, v0, :cond_0

    goto :goto_0

    .line 1
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Invalid keyPointType type: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_1
    :goto_0
    const/4 v2, 0x3

    const/4 v3, 0x0

    if-nez p1, :cond_2

    .line 4
    iget-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-virtual {p1, v3}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->setKeyPointType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    .line 5
    iget-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-virtual {p1, v2}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->setShapeType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    goto :goto_1

    :cond_2
    if-ne p1, v1, :cond_3

    .line 7
    iget-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-virtual {p1, v1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->setKeyPointType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    .line 8
    iget-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-virtual {p1, v2}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->setShapeType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    goto :goto_1

    .line 10
    :cond_3
    iget-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-virtual {p1, v3}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->setKeyPointType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    .line 11
    iget-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-virtual {p1, v0}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->setShapeType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    :goto_1
    return-object p0
.end method

.method public setMaxSizeFaceOnly(Z)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->setMaxSizeFaceOnly(Z)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    return-object p0
.end method

.method public setMinFaceProportion(F)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;
    .locals 3

    const/4 v0, 0x0

    cmpl-float v0, p1, v0

    if-ltz v0, :cond_0

    const/high16 v0, 0x3f800000    # 1.0f

    cmpg-float v0, p1, v0

    if-gtz v0, :cond_0

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->setMinFaceProportion(F)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    return-object p0

    .line 4
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Invalid minimum proportional: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(F)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public setPerformanceType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;
    .locals 3

    const/4 v0, 0x1

    if-eq p1, v0, :cond_1

    const/4 v0, 0x2

    if-ne p1, v0, :cond_0

    goto :goto_0

    .line 1
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "Invalid performance type: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 2
    :cond_1
    :goto_0
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->setPerformanceType(I)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    return-object p0
.end method

.method public setTracingAllowed(Z)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzer$Factory;->factory:Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;->setTracingAllowed(Z)Lcom/huawei/hms/mlsdk/face/MLFaceAnalyzerSetting$Factory;

    return-object p0
.end method
