.class public Lcom/huawei/hms/mlsdk/text/entity/imageplate/MLImagePlate;
.super Lcom/huawei/hms/mlsdk/text/MLPlate;
.source "MLImagePlate.java"


# instance fields
.field private confidence:F

.field private points:[Landroid/graphics/Point;

.field private rect:Landroid/graphics/Rect;


# direct methods
.method public constructor <init>(Landroid/graphics/Rect;[Landroid/graphics/Point;F)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/text/MLPlate;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/entity/imageplate/MLImagePlate;->rect:Landroid/graphics/Rect;

    .line 3
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/text/entity/imageplate/MLImagePlate;->points:[Landroid/graphics/Point;

    .line 4
    iput p3, p0, Lcom/huawei/hms/mlsdk/text/entity/imageplate/MLImagePlate;->confidence:F

    return-void
.end method


# virtual methods
.method public getBorder()Landroid/graphics/Rect;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/entity/imageplate/MLImagePlate;->rect:Landroid/graphics/Rect;

    return-object v0
.end method

.method public getConfidence()Ljava/lang/Float;
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/text/entity/imageplate/MLImagePlate;->confidence:F

    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v0

    return-object v0
.end method

.method public getMLPlate()Lcom/huawei/hms/mlsdk/text/MLPlate;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Lcom/huawei/hms/mlsdk/text/MLPlate;",
            ">()TT;"
        }
    .end annotation

    return-object p0
.end method

.method public getMLPlateType()Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;
    .locals 1

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;->IMAGE:Lcom/huawei/hms/mlsdk/text/entity/MLPlateType;

    return-object v0
.end method

.method public getVertexes()[Landroid/graphics/Point;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/entity/imageplate/MLImagePlate;->points:[Landroid/graphics/Point;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, [Landroid/graphics/Point;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Landroid/graphics/Point;

    :goto_0
    return-object v0
.end method
