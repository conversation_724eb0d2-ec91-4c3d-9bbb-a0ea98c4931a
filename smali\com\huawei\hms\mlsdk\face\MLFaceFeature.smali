.class public Lcom/huawei/hms/mlsdk/face/MLFaceFeature;
.super Ljava/lang/Object;
.source "MLFaceFeature.java"


# static fields
.field private static final DEFAULT_MAX_PROBABILITY:F = 1.0f

.field private static final DEFAULT_PROBABILITY:F = -1.0f


# instance fields
.field private age:I

.field private hatProbability:F

.field private leftEyeOpenProbability:F

.field private moustacheProbability:F

.field private rightEyeOpenProbability:F

.field private sexProbability:F

.field private sunGlassProbability:F


# direct methods
.method public constructor <init>(FFFFFFI)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->leftEyeOpenProbability:F

    .line 3
    iput p2, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->rightEyeOpenProbability:F

    .line 4
    iput p3, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->sunGlassProbability:F

    .line 5
    iput p4, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->sexProbability:F

    .line 6
    iput p5, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->hatProbability:F

    .line 7
    iput p6, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->moustacheProbability:F

    .line 8
    iput p7, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->age:I

    return-void
.end method


# virtual methods
.method public getAge()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->age:I

    return v0
.end method

.method public getHatProbability()F
    .locals 2

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->hatProbability:F

    const/4 v1, 0x0

    cmpg-float v1, v0, v1

    if-gez v1, :cond_0

    return v0

    :cond_0
    const/high16 v1, 0x3f800000    # 1.0f

    sub-float/2addr v1, v0

    return v1
.end method

.method public getLeftEyeOpenProbability()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->leftEyeOpenProbability:F

    return v0
.end method

.method public getMoustacheProbability()F
    .locals 2

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->moustacheProbability:F

    const/4 v1, 0x0

    cmpg-float v1, v0, v1

    if-gez v1, :cond_0

    const/high16 v0, -0x40800000    # -1.0f

    return v0

    :cond_0
    const/high16 v1, 0x3f800000    # 1.0f

    sub-float/2addr v1, v0

    return v1
.end method

.method public getRightEyeOpenProbability()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->rightEyeOpenProbability:F

    return v0
.end method

.method public getSexProbability()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->sexProbability:F

    return v0
.end method

.method public getSunGlassProbability()F
    .locals 2

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->sunGlassProbability:F

    const/4 v1, 0x0

    cmpg-float v1, v0, v1

    if-gez v1, :cond_0

    const/high16 v0, -0x40800000    # -1.0f

    :cond_0
    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 1
    invoke-static {p0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->toStringHelper(Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->leftEyeOpenProbability:F

    .line 2
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "leftEyeOpenProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->rightEyeOpenProbability:F

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "rightEyeOpenProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    .line 3
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->getSunGlassProbability()F

    move-result v1

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "sunGlassProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->sexProbability:F

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "sexProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->hatProbability:F

    .line 4
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "hatProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->getMoustacheProbability()F

    move-result v1

    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "moustacheProbability"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->age:I

    .line 5
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "age"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
