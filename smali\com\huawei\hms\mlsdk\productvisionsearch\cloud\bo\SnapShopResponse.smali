.class public Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;
.super Ljava/lang/Object;
.source "SnapShopResponse.java"


# instance fields
.field private retCode:Ljava/lang/String;

.field private retMsg:Ljava/lang/String;

.field private snapShopResults:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResult;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->retCode:Ljava/lang/String;

    return-object v0
.end method

.method public b()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->retMsg:Ljava/lang/String;

    return-object v0
.end method

.method public c()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResult;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/bo/SnapShopResponse;->snapShopResults:Ljava/util/List;

    return-object v0
.end method
