.class Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer$a;
.super Ljava/lang/Object;
.source "MLRemoteProductVisionSearchAnalyzer.java"

# interfaces
.implements Ljava/util/concurrent/Callable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->asyncAnalyseFrame(Lcom/huawei/hms/mlsdk/common/MLFrame;)Lcom/huawei/hmf/tasks/Task;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/util/concurrent/Callable<",
        "Ljava/util/List<",
        "Lcom/huawei/hms/mlsdk/productvisionsearch/MLProductVisionSearch;",
        ">;>;"
    }
.end annotation


# instance fields
.field final synthetic a:Lcom/huawei/hms/mlsdk/common/MLFrame;

.field final synthetic b:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;


# direct methods
.method constructor <init>(Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer$a;->b:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;

    iput-object p2, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer$a;->a:Lcom/huawei/hms/mlsdk/common/MLFrame;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public call()Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Exception;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer$a;->b:Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer$a;->a:Lcom/huawei/hms/mlsdk/common/MLFrame;

    invoke-static {v0, v1}, Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;->a(Lcom/huawei/hms/mlsdk/productvisionsearch/cloud/MLRemoteProductVisionSearchAnalyzer;Lcom/huawei/hms/mlsdk/common/MLFrame;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
