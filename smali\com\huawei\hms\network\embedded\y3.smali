.class public Lcom/huawei/hms/network/embedded/y3;
.super Ljava/lang/Object;
.source ""


# instance fields
.field public a:Lcom/huawei/hms/network/embedded/z3;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/huawei/hms/network/embedded/z3;

    invoke-direct {v0}, Lcom/huawei/hms/network/embedded/z3;-><init>()V

    iput-object v0, p0, Lcom/huawei/hms/network/embedded/y3;->a:Lcom/huawei/hms/network/embedded/z3;

    return-void
.end method


# virtual methods
.method public a()V
    .locals 0

    return-void
.end method

.method public a(J)V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/y3;->a:Lcom/huawei/hms/network/embedded/z3;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/z3;->getMetrics()Lcom/huawei/hms/network/embedded/q2;

    move-result-object v0

    invoke-virtual {v0, p1, p2}, Lcom/huawei/hms/network/embedded/q2;->setRequestByteCount(J)V

    return-void
.end method

.method public a(Lcom/huawei/hms/network/httpclient/Response;)V
    .locals 0

    iget-object p1, p0, Lcom/huawei/hms/network/embedded/y3;->a:Lcom/huawei/hms/network/embedded/z3;

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/z3;->getMetricsTime()Lcom/huawei/hms/network/embedded/r2;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/r2;->setCallEndTime()V

    iget-object p1, p0, Lcom/huawei/hms/network/embedded/y3;->a:Lcom/huawei/hms/network/embedded/z3;

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/z3;->getMetricsRealTime()Lcom/huawei/hms/network/embedded/r2;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/r2;->setCallEndTime()V

    return-void
.end method

.method public a(Ljava/lang/Exception;)V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/y3;->a:Lcom/huawei/hms/network/embedded/z3;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/embedded/n2;->setException(Ljava/lang/Exception;)V

    iget-object p1, p0, Lcom/huawei/hms/network/embedded/y3;->a:Lcom/huawei/hms/network/embedded/z3;

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/z3;->getMetricsTime()Lcom/huawei/hms/network/embedded/r2;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/r2;->setCallEndTime()V

    iget-object p1, p0, Lcom/huawei/hms/network/embedded/y3;->a:Lcom/huawei/hms/network/embedded/z3;

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/z3;->getMetricsRealTime()Lcom/huawei/hms/network/embedded/r2;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/r2;->setCallEndTime()V

    return-void
.end method

.method public a(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/y3;->a:Lcom/huawei/hms/network/embedded/z3;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/embedded/n2;->setUrl(Ljava/lang/String;)V

    iget-object p1, p0, Lcom/huawei/hms/network/embedded/y3;->a:Lcom/huawei/hms/network/embedded/z3;

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/z3;->getMetricsTime()Lcom/huawei/hms/network/embedded/r2;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/r2;->setCallStartTime()V

    iget-object p1, p0, Lcom/huawei/hms/network/embedded/y3;->a:Lcom/huawei/hms/network/embedded/z3;

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/z3;->getMetricsRealTime()Lcom/huawei/hms/network/embedded/r2;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/network/embedded/r2;->setCallStartTime()V

    return-void
.end method

.method public a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/huawei/hms/network/embedded/d1;)V
    .locals 0

    return-void
.end method

.method public a(Ljava/util/Map;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/String;",
            ">;>;)V"
        }
    .end annotation

    return-void
.end method

.method public b()V
    .locals 0

    return-void
.end method

.method public b(J)V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/y3;->a:Lcom/huawei/hms/network/embedded/z3;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/z3;->getMetrics()Lcom/huawei/hms/network/embedded/q2;

    move-result-object v0

    invoke-virtual {v0, p1, p2}, Lcom/huawei/hms/network/embedded/q2;->setResponseByteCount(J)V

    return-void
.end method

.method public b(Ljava/lang/String;)V
    .locals 0

    return-void
.end method

.method public c()V
    .locals 0

    return-void
.end method

.method public d()V
    .locals 0

    return-void
.end method

.method public e()V
    .locals 0

    return-void
.end method

.method public getRequestFinishedInfo()Lcom/huawei/hms/network/httpclient/RequestFinishedInfo;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/y3;->a:Lcom/huawei/hms/network/embedded/z3;

    return-object v0
.end method
