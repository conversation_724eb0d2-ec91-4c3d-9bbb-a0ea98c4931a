.class public Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor;
.super Lcom/huawei/hms/mlsdk/common/MLProminentTransactor;
.source "MLMaxSizeFaceTransactor.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor$Creator;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/huawei/hms/mlsdk/common/MLProminentTransactor<",
        "Lcom/huawei/hms/mlsdk/face/MLFace;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>(Lcom/huawei/hms/mlsdk/common/MLAnalyzer;Lcom/huawei/hms/mlsdk/common/MLResultTrailer;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLAnalyzer<",
            "Lcom/huawei/hms/mlsdk/face/MLFace;",
            ">;",
            "Lcom/huawei/hms/mlsdk/common/MLResultTrailer<",
            "Lcom/huawei/hms/mlsdk/face/MLFace;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0, p1, p2}, Lcom/huawei/hms/mlsdk/common/MLProminentTransactor;-><init>(Lcom/huawei/hms/mlsdk/common/MLAnalyzer;Lcom/huawei/hms/mlsdk/common/MLResultTrailer;)V

    return-void
.end method

.method static synthetic access$000(Lcom/huawei/hms/mlsdk/face/MLMaxSizeFaceTransactor;I)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lcom/huawei/hms/mlsdk/common/MLProminentTransactor;->setMaxFrameLostCount(I)V

    return-void
.end method


# virtual methods
.method public compare(Ljava/lang/Object;Ljava/lang/Object;)Z
    .locals 0

    .line 1
    check-cast p1, Lcom/huawei/hms/mlsdk/face/MLFace;

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/face/MLFace;->getTracingIdentity()I

    move-result p1

    check-cast p2, Lcom/huawei/hms/mlsdk/face/MLFace;

    invoke-virtual {p2}, Lcom/huawei/hms/mlsdk/face/MLFace;->getTracingIdentity()I

    move-result p2

    if-ne p1, p2, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return p1
.end method

.method public getSpecificTarget(Lcom/huawei/hms/mlsdk/common/MLAnalyzer$Result;)I
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/common/MLAnalyzer$Result<",
            "Lcom/huawei/hms/mlsdk/face/MLFace;",
            ">;)I"
        }
    .end annotation

    .line 1
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLAnalyzer$Result;->getAnalyseList()Landroid/util/SparseArray;

    move-result-object p1

    .line 2
    invoke-virtual {p1}, Landroid/util/SparseArray;->size()I

    move-result v0

    if-eqz v0, :cond_2

    const/4 v0, 0x0

    .line 5
    invoke-virtual {p1, v0}, Landroid/util/SparseArray;->keyAt(I)I

    move-result v1

    .line 6
    invoke-virtual {p1, v0}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/huawei/hms/mlsdk/face/MLFace;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/face/MLFace;->getWidth()F

    move-result v0

    const/4 v2, 0x1

    .line 8
    :goto_0
    invoke-virtual {p1}, Landroid/util/SparseArray;->size()I

    move-result v3

    if-ge v2, v3, :cond_1

    .line 9
    invoke-virtual {p1, v2}, Landroid/util/SparseArray;->keyAt(I)I

    move-result v3

    .line 10
    invoke-virtual {p1, v2}, Landroid/util/SparseArray;->valueAt(I)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Lcom/huawei/hms/mlsdk/face/MLFace;

    invoke-virtual {v4}, Lcom/huawei/hms/mlsdk/face/MLFace;->getWidth()F

    move-result v4

    cmpl-float v5, v4, v0

    if-lez v5, :cond_0

    move v1, v3

    move v0, v4

    :cond_0
    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return v1

    .line 11
    :cond_2
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "No faces for selectFocus."

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
