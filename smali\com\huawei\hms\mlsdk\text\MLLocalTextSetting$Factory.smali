.class public Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;
.super Ljava/lang/Object;
.source "MLLocalTextSetting.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Factory"
.end annotation


# instance fields
.field private language:Ljava/lang/String;

.field private ocrMode:I

.field private plateEnable:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, "rm"

    .line 2
    iput-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->language:Ljava/lang/String;

    const/4 v0, 0x1

    .line 3
    iput v0, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->ocrMode:I

    const/4 v0, 0x0

    .line 6
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->plateEnable:Z

    return-void
.end method


# virtual methods
.method public create()Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;
    .locals 5

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->language:Ljava/lang/String;

    iget v2, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->ocrMode:I

    iget-boolean v3, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->plateEnable:Z

    const/4 v4, 0x0

    invoke-direct {v0, v1, v2, v3, v4}, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting;-><init>(Ljava/lang/String;IZLcom/huawei/hms/mlsdk/text/MLLocalTextSetting$1;)V

    return-object v0
.end method

.method public setLanguage(Ljava/lang/String;)Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;
    .locals 0

    if-eqz p1, :cond_0

    .line 1
    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->language:Ljava/lang/String;

    :cond_0
    return-object p0
.end method

.method public setOCRMode(I)Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;
    .locals 2

    const/4 v0, 0x1

    if-eq p1, v0, :cond_0

    const/4 v1, 0x2

    if-eq p1, v1, :cond_0

    .line 1
    iput v0, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->ocrMode:I

    const-string v0, "RecognizerOptions"

    const-string v1, "ocrMode should be either 1 for OCR_DETECT_MODE or 2 for OCR_TRACKING_MODE, set ocrMode OCR_DETECT_MODE."

    .line 2
    invoke-static {v0, v1}, Lcom/huawei/hms/ml/common/base/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 4
    :cond_0
    iput p1, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->ocrMode:I

    return-object p0
.end method

.method public setPlateEnable(Z)Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;
    .locals 0

    .line 1
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/text/MLLocalTextSetting$Factory;->plateEnable:Z

    return-object p0
.end method
