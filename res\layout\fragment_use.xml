<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:background="@color/white" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <com.mxz.hyx.views.TemplateTitle android:id="@id/tt_head" app:canBack="false" app:titleText="常见问题" style="@style/header_style" />
    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_below="@id/tt_head">
        <LinearLayout android:orientation="horizontal" android:id="@id/rl_class_serach" android:background="@drawable/bg_search_edit" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <RelativeLayout android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/x10" android:layout_marginTop="@dimen/x4" android:layout_marginBottom="@dimen/x7" android:layout_weight="1.0">
                <ImageView android:id="@id/iv_class_search_img" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/x10" android:layout_marginRight="@dimen/x8" android:src="@mipmap/ic_search" android:layout_centerVertical="true" />
                <EditText android:textSize="13.0sp" android:textColor="@color/colorTextG2" android:gravity="center_vertical" android:id="@id/search_key" android:background="@drawable/bg_transparentalert" android:layout_width="fill_parent" android:layout_height="@dimen/x33" android:hint="请输入查找的关键字" android:layout_toRightOf="@id/iv_class_search_img" android:layout_centerHorizontal="true" />
            </RelativeLayout>
        </LinearLayout>
        <LinearLayout android:gravity="center_vertical" android:orientation="vertical" android:id="@id/plan" android:layout_width="fill_parent" android:layout_height="fill_parent">
            <androidx.recyclerview.widget.RecyclerView android:id="@id/rv_list" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_weight="1.0" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>