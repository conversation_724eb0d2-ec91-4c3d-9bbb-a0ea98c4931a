.class final Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder$Holder;
.super Ljava/lang/Object;
.source "RemoteObjectDecoder.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "Holder"
.end annotation


# static fields
.field static final INSTANCE:Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;-><init>(Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder$1;)V

    sput-object v0, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder$Holder;->INSTANCE:Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
