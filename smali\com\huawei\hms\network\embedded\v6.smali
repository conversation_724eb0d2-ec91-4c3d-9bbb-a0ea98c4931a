.class public final Lcom/huawei/hms/network/embedded/v6;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/network/embedded/v6$a;
    }
.end annotation


# instance fields
.field public final delegate:Lcom/huawei/hms/network/embedded/v8;


# direct methods
.method public constructor <init>()V
    .locals 4

    sget-object v0, Ljava/util/concurrent/TimeUnit;->MINUTES:Ljava/util/concurrent/TimeUnit;

    const/4 v1, 0x5

    const-wide/16 v2, 0x5

    invoke-direct {p0, v1, v2, v3, v0}, Lcom/huawei/hms/network/embedded/v6;-><init>(IJLjava/util/concurrent/TimeUnit;)V

    return-void
.end method

.method public constructor <init>(IJLjava/util/concurrent/TimeUnit;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/huawei/hms/network/embedded/v8;

    invoke-direct {v0, p1, p2, p3, p4}, Lcom/huawei/hms/network/embedded/v8;-><init>(IJLjava/util/concurrent/TimeUnit;)V

    iput-object v0, p0, Lcom/huawei/hms/network/embedded/v6;->delegate:Lcom/huawei/hms/network/embedded/v8;

    return-void
.end method


# virtual methods
.method public a()I
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/v6;->delegate:Lcom/huawei/hms/network/embedded/v8;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/v8;->a()I

    move-result v0

    return v0
.end method

.method public a(Ljava/lang/String;ILjava/lang/String;)I
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/v6;->delegate:Lcom/huawei/hms/network/embedded/v8;

    invoke-virtual {v0, p1, p2, p3}, Lcom/huawei/hms/network/embedded/v8;->a(Ljava/lang/String;ILjava/lang/String;)I

    move-result p1

    return p1
.end method

.method public a(Lcom/huawei/hms/network/embedded/v6$a;)V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/v6;->delegate:Lcom/huawei/hms/network/embedded/v8;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/embedded/v8;->a(Lcom/huawei/hms/network/embedded/v6$a;)V

    return-void
.end method

.method public b()V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/v6;->delegate:Lcom/huawei/hms/network/embedded/v8;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/v8;->b()V

    return-void
.end method

.method public b(Lcom/huawei/hms/network/embedded/v6$a;)V
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/v6;->delegate:Lcom/huawei/hms/network/embedded/v8;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/embedded/v8;->b(Lcom/huawei/hms/network/embedded/v6$a;)V

    return-void
.end method

.method public b(Ljava/lang/String;ILjava/lang/String;)Z
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/v6;->delegate:Lcom/huawei/hms/network/embedded/v8;

    invoke-virtual {v0, p1, p2, p3}, Lcom/huawei/hms/network/embedded/v8;->b(Ljava/lang/String;ILjava/lang/String;)Z

    move-result p1

    return p1
.end method

.method public c()I
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/embedded/v6;->delegate:Lcom/huawei/hms/network/embedded/v8;

    invoke-virtual {v0}, Lcom/huawei/hms/network/embedded/v8;->c()I

    move-result v0

    return v0
.end method
