.class public Lcom/huawei/hms/mlsdk/face/MLFace;
.super Ljava/lang/Object;
.source "MLFace.java"


# static fields
.field public static final UNANALYZED_POSSIBILITY:F = -1.0f


# instance fields
.field private allPoints:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;"
        }
    .end annotation
.end field

.field private border:Landroid/graphics/Rect;

.field private coordinatePoint:Landroid/graphics/PointF;

.field private emotions:Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;

.field private faceShapes:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/face/MLFaceShape;",
            ">;"
        }
    .end annotation
.end field

.field private features:Lcom/huawei/hms/mlsdk/face/MLFaceFeature;

.field private height:F

.field private keyPoints:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;",
            ">;"
        }
    .end annotation
.end field

.field private rotationAngleX:F

.field private rotationAngleY:F

.field private rotationAngleZ:F

.field private trackingId:I

.field private width:F


# direct methods
.method public constructor <init>(ILandroid/graphics/PointF;FFFFFLjava/util/List;Ljava/util/List;Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;Lcom/huawei/hms/mlsdk/face/MLFaceFeature;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Landroid/graphics/PointF;",
            "FFFFF",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;",
            ">;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/face/MLFaceShape;",
            ">;",
            "Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;",
            "Lcom/huawei/hms/mlsdk/face/MLFaceFeature;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput p1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->trackingId:I

    .line 3
    iput-object p2, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->coordinatePoint:Landroid/graphics/PointF;

    .line 4
    iput p3, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->width:F

    .line 5
    iput p4, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->height:F

    .line 6
    iput p5, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->rotationAngleX:F

    .line 7
    iput p6, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->rotationAngleY:F

    .line 8
    iput p7, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->rotationAngleZ:F

    .line 9
    iput-object p8, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->keyPoints:Ljava/util/List;

    .line 10
    iput-object p9, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->faceShapes:Ljava/util/List;

    .line 11
    iput-object p10, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->emotions:Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;

    .line 12
    iput-object p11, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->features:Lcom/huawei/hms/mlsdk/face/MLFaceFeature;

    .line 13
    iput-object p12, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->allPoints:Ljava/util/List;

    .line 14
    new-instance p1, Landroid/graphics/Rect;

    iget-object p2, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->coordinatePoint:Landroid/graphics/PointF;

    iget p5, p2, Landroid/graphics/PointF;->x:F

    float-to-int p6, p5

    iget p2, p2, Landroid/graphics/PointF;->y:F

    float-to-int p7, p2

    add-float/2addr p5, p3

    float-to-int p3, p5

    add-float/2addr p2, p4

    float-to-int p2, p2

    invoke-direct {p1, p6, p7, p3, p2}, Landroid/graphics/Rect;-><init>(IIII)V

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->border:Landroid/graphics/Rect;

    return-void
.end method


# virtual methods
.method public getAllPoints()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/common/MLPosition;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->allPoints:Ljava/util/List;

    if-nez v0, :cond_0

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    :cond_0
    return-object v0
.end method

.method public getBorder()Landroid/graphics/Rect;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->border:Landroid/graphics/Rect;

    return-object v0
.end method

.method public getCoordinatePoint()Landroid/graphics/PointF;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->coordinatePoint:Landroid/graphics/PointF;

    return-object v0
.end method

.method public getEmotions()Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->emotions:Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;

    return-object v0
.end method

.method public getFaceKeyPoint(I)Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->keyPoints:Ljava/util/List;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-lez v0, :cond_1

    const/4 v0, 0x0

    .line 2
    :goto_0
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->keyPoints:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_1

    .line 3
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->keyPoints:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;

    if-eqz v1, :cond_0

    .line 4
    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;->getType()I

    move-result v2

    if-ne v2, p1, :cond_0

    return-object v1

    :cond_0
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    return-object p1
.end method

.method public getFaceKeyPoints()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/face/MLFaceKeyPoint;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->keyPoints:Ljava/util/List;

    if-nez v0, :cond_0

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    :cond_0
    return-object v0
.end method

.method public getFaceShape(I)Lcom/huawei/hms/mlsdk/face/MLFaceShape;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->faceShapes:Ljava/util/List;

    if-eqz v0, :cond_6

    invoke-interface {v0}, Ljava/util/List;->size()I

    move-result v0

    if-nez v0, :cond_0

    goto/16 :goto_2

    :cond_0
    const/4 v0, 0x0

    if-eqz p1, :cond_3

    .line 5
    :goto_0
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->faceShapes:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->size()I

    move-result v1

    if-ge v0, v1, :cond_2

    .line 6
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->faceShapes:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    if-eqz v1, :cond_1

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->faceShapes:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/huawei/hms/mlsdk/face/MLFaceShape;

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->getFaceShapeType()I

    move-result v1

    if-ne v1, p1, :cond_1

    .line 7
    iget-object p1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->faceShapes:Ljava/util/List;

    invoke-interface {p1, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/huawei/hms/mlsdk/face/MLFaceShape;

    return-object p1

    :cond_1
    add-int/lit8 v0, v0, 0x1

    goto :goto_0

    .line 10
    :cond_2
    new-instance v0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    invoke-direct {v0, p1, v1}, Lcom/huawei/hms/mlsdk/face/MLFaceShape;-><init>(ILjava/util/List;)V

    return-object v0

    .line 13
    :cond_3
    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    .line 14
    :goto_1
    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->faceShapes:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v0, v2, :cond_5

    .line 15
    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->faceShapes:Ljava/util/List;

    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    if-eqz v2, :cond_4

    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->faceShapes:Ljava/util/List;

    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/face/MLFaceShape;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->getPoints()Ljava/util/List;

    move-result-object v2

    if-eqz v2, :cond_4

    .line 16
    iget-object v2, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->faceShapes:Ljava/util/List;

    invoke-interface {v2, v0}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/huawei/hms/mlsdk/face/MLFaceShape;

    invoke-virtual {v2}, Lcom/huawei/hms/mlsdk/face/MLFaceShape;->getPoints()Ljava/util/List;

    move-result-object v2

    invoke-interface {v1, v2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_4
    add-int/lit8 v0, v0, 0x1

    goto :goto_1

    .line 19
    :cond_5
    new-instance v0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;

    invoke-direct {v0, p1, v1}, Lcom/huawei/hms/mlsdk/face/MLFaceShape;-><init>(ILjava/util/List;)V

    return-object v0

    .line 20
    :cond_6
    :goto_2
    new-instance v0, Lcom/huawei/hms/mlsdk/face/MLFaceShape;

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    invoke-direct {v0, p1, v1}, Lcom/huawei/hms/mlsdk/face/MLFaceShape;-><init>(ILjava/util/List;)V

    return-object v0
.end method

.method public getFaceShapeList()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/face/MLFaceShape;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->faceShapes:Ljava/util/List;

    if-nez v0, :cond_0

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    :cond_0
    return-object v0
.end method

.method public getFeatures()Lcom/huawei/hms/mlsdk/face/MLFaceFeature;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->features:Lcom/huawei/hms/mlsdk/face/MLFaceFeature;

    return-object v0
.end method

.method public getHeight()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->height:F

    return v0
.end method

.method public getRotationAngleX()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->rotationAngleX:F

    return v0
.end method

.method public getRotationAngleY()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->rotationAngleY:F

    return v0
.end method

.method public getRotationAngleZ()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->rotationAngleZ:F

    return v0
.end method

.method public getTracingIdentity()I
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->trackingId:I

    return v0
.end method

.method public getWidth()F
    .locals 1

    .line 1
    iget v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->width:F

    return v0
.end method

.method public opennessOfLeftEye()F
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->features:Lcom/huawei/hms/mlsdk/face/MLFaceFeature;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->getLeftEyeOpenProbability()F

    move-result v0

    return v0
.end method

.method public opennessOfRightEye()F
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->features:Lcom/huawei/hms/mlsdk/face/MLFaceFeature;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/face/MLFaceFeature;->getRightEyeOpenProbability()F

    move-result v0

    return v0
.end method

.method public possibilityOfSmiling()F
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->emotions:Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;->getSmilingProbability()F

    move-result v0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    .line 1
    invoke-static {p0}, Lcom/huawei/hms/mlsdk/internal/client/Objects;->toStringHelper(Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->border:Landroid/graphics/Rect;

    const-string v2, "boundingBox"

    .line 2
    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->trackingId:I

    .line 3
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "trackingId"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->emotions:Lcom/huawei/hms/mlsdk/face/MLFaceEmotion;

    const-string v2, "emotions"

    .line 4
    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->features:Lcom/huawei/hms/mlsdk/face/MLFaceFeature;

    const-string v2, "features"

    .line 5
    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->rotationAngleX:F

    .line 6
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "eulerX"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->rotationAngleY:F

    .line 7
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "eulerY"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->rotationAngleZ:F

    .line 8
    invoke-static {v1}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v1

    const-string v2, "eulerZ"

    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->keyPoints:Ljava/util/List;

    const-string v2, "keyPoints"

    .line 9
    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/face/MLFace;->faceShapes:Ljava/util/List;

    const-string v2, "faceShapes"

    .line 10
    invoke-virtual {v0, v2, v1}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->attribute(Ljava/lang/String;Ljava/lang/Object;)Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;

    move-result-object v0

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/internal/client/Objects$ToStringHelper;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
