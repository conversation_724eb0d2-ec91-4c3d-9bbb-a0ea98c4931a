.class public Lcom/huawei/hms/network/ai/j0;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/network/ai/j0$d;,
        Lcom/huawei/hms/network/ai/j0$c;,
        Lcom/huawei/hms/network/ai/j0$b;,
        Lcom/huawei/hms/network/ai/j0$a;
    }
.end annotation


# instance fields
.field public a:Ljava/lang/String;

.field public b:Lcom/huawei/hms/network/ai/j0$b;

.field public c:Lcom/huawei/hms/network/ai/j0$d;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lcom/huawei/hms/network/ai/j0$b;

    invoke-direct {v0, p0}, Lcom/huawei/hms/network/ai/j0$b;-><init>(Lcom/huawei/hms/network/ai/j0;)V

    iput-object v0, p0, Lcom/huawei/hms/network/ai/j0;->b:Lcom/huawei/hms/network/ai/j0$b;

    new-instance v0, Lcom/huawei/hms/network/ai/j0$d;

    invoke-direct {v0, p0}, Lcom/huawei/hms/network/ai/j0$d;-><init>(Lcom/huawei/hms/network/ai/j0;)V

    iput-object v0, p0, Lcom/huawei/hms/network/ai/j0;->c:Lcom/huawei/hms/network/ai/j0$d;

    return-void
.end method


# virtual methods
.method public a()Lcom/huawei/hms/network/ai/j0$b;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/ai/j0;->b:Lcom/huawei/hms/network/ai/j0$b;

    return-object v0
.end method

.method public a(Lcom/huawei/hms/network/ai/j0$b;)V
    .locals 0

    iput-object p1, p0, Lcom/huawei/hms/network/ai/j0;->b:Lcom/huawei/hms/network/ai/j0$b;

    return-void
.end method

.method public a(Lcom/huawei/hms/network/ai/j0$d;)V
    .locals 0

    iput-object p1, p0, Lcom/huawei/hms/network/ai/j0;->c:Lcom/huawei/hms/network/ai/j0$d;

    return-void
.end method

.method public a(Ljava/lang/String;)V
    .locals 0

    iput-object p1, p0, Lcom/huawei/hms/network/ai/j0;->a:Ljava/lang/String;

    return-void
.end method

.method public b()Lcom/huawei/hms/network/ai/j0$d;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/ai/j0;->c:Lcom/huawei/hms/network/ai/j0$d;

    return-object v0
.end method

.method public c()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lcom/huawei/hms/network/ai/j0;->a:Ljava/lang/String;

    return-object v0
.end method
