.class public final Lcom/huawei/hms/mlsdk/internal/client/rest/c;
.super Ljava/lang/Object;
.source "RestClientContext.java"


# instance fields
.field private final a:Landroid/content/Context;

.field private final b:Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;


# direct methods
.method public constructor <init>(Lcom/huawei/hms/mlsdk/common/MLApplication;)V
    .locals 2

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppContext()Landroid/content/Context;

    move-result-object v0

    iput-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->a:Landroid/content/Context;

    .line 3
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppSetting()Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    move-result-object v1

    if-eqz v1, :cond_0

    .line 4
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAppSetting()Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    move-result-object p1

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->b:Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    goto :goto_0

    .line 6
    :cond_0
    invoke-static {v0}, Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;->fromResource(Landroid/content/Context;)Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    move-result-object p1

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->b:Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    :goto_0
    return-void
.end method


# virtual methods
.method public a()Landroid/content/Context;
    .locals 1

    .line 16
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->a:Landroid/content/Context;

    return-object v0
.end method

.method public a(Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;)Ljava/util/Map;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation

    .line 1
    invoke-static {}, Ljava/util/UUID;->randomUUID()Ljava/util/UUID;

    move-result-object v0

    const-string v1, "Content-Type"

    const-string v2, "application/json"

    .line 2
    invoke-virtual {p1, v1, v2}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    .line 3
    invoke-static {v0}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "X-Request-ID"

    invoke-virtual {p1, v1, v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    const-string v0, "X-User-Agent"

    .line 4
    invoke-virtual {p1, v0, v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    .line 5
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->b:Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;->getAppId()Ljava/lang/String;

    move-result-object v0

    const-string v1, "appId"

    invoke-virtual {p1, v1, v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    .line 6
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->b:Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;->getAppId()Ljava/lang/String;

    move-result-object v0

    const-string v1, "HMS-APPLICATION-ID"

    invoke-virtual {p1, v1, v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    .line 7
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->b:Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;->getPackageName()Ljava/lang/String;

    move-result-object v0

    const-string v1, "X-Package-Name"

    invoke-virtual {p1, v1, v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    .line 8
    new-instance v0, Lcom/huawei/hms/ml/common/utils/CountryCodeBean;

    iget-object v1, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->a:Landroid/content/Context;

    const/4 v3, 0x0

    invoke-direct {v0, v1, v3}, Lcom/huawei/hms/ml/common/utils/CountryCodeBean;-><init>(Landroid/content/Context;Z)V

    invoke-virtual {v0}, Lcom/huawei/hms/ml/common/utils/CountryCodeBean;->getCountryCode()Ljava/lang/String;

    move-result-object v0

    const-string v1, "X-Country-Code"

    invoke-virtual {p1, v1, v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    const-string v0, "supplierId"

    .line 9
    invoke-virtual {p1, v0, v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    const-string v0, "accept"

    .line 10
    invoke-virtual {p1, v0, v2}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    .line 11
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->b:Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;->getCertFingerprint()Ljava/lang/String;

    move-result-object v0

    const-string v1, "certFingerprint"

    invoke-virtual {p1, v1, v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    .line 12
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Bearer "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getInstance()Lcom/huawei/hms/mlsdk/common/MLApplication;

    move-result-object v1

    invoke-virtual {v1}, Lcom/huawei/hms/mlsdk/common/MLApplication;->getAuthorizationToken()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "Authorization"

    invoke-virtual {p1, v1, v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    .line 13
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->b:Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;->getMLSdkVersion()Ljava/lang/String;

    move-result-object v0

    const-string v1, "X-Mlkit-Version"

    invoke-virtual {p1, v1, v0}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a(Ljava/lang/String;Ljava/lang/String;)Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;

    .line 15
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/internal/client/rest/e$b;->a()Lcom/huawei/hms/mlsdk/internal/client/rest/e;

    move-result-object p1

    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/internal/client/rest/e;->a()Ljava/util/Map;

    move-result-object p1

    return-object p1
.end method

.method public b()Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/internal/client/rest/c;->b:Lcom/huawei/hms/mlsdk/common/MLApplicationSetting;

    return-object v0
.end method
