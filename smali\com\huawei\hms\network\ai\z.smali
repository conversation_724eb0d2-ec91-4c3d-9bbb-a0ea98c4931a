.class public Lcom/huawei/hms/network/ai/z;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lcom/huawei/hms/network/ai/i;


# static fields
.field public static final A:I = 0x8

.field public static final B:I = 0x7530

.field public static final C:I = 0xea600

.field public static final D:Ljava/lang/String; = "ai_websocketpingmodel_download"

.field public static final E:Ljava/lang/String; = "lastDownloadTime"

.field public static final F:J = 0x240c8400L

.field public static final g:Ljava/lang/String; = "WebSocketPingModel"

.field public static final h:Ljava/lang/String; = "Wise_ping.model"

.field public static final i:I = -0x1

.field public static final j:I = 0x7530

.field public static final k:I = 0x1770

.field public static final l:F = 5.0f

.field public static final m:I = 0x3

.field public static final n:I = 0x5

.field public static final o:I = -0x7f

.field public static final p:I = 0x7fffffff

.field public static final q:I = -0x5f

.field public static final r:I = -0x5a

.field public static final s:I = -0x50

.field public static final t:I = 0x96

.field public static final u:I = 0x12c

.field public static final v:I = 0x190

.field public static final w:I = 0x1f4

.field public static final x:I = 0x5

.field public static final y:I = 0x6

.field public static final z:I = 0x7


# instance fields
.field public a:Lcom/huawei/hms/network/ai/a0;

.field public b:Lcom/huawei/hms/network/ai/n0;

.field public c:I

.field public d:I

.field public e:I

.field public f:Lcom/huawei/hms/framework/common/PLSharedPreferences;


# direct methods
.method public constructor <init>(Lcom/huawei/hms/network/ai/a0;)V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/huawei/hms/network/ai/z;->b:Lcom/huawei/hms/network/ai/n0;

    const/4 v0, 0x0

    iput v0, p0, Lcom/huawei/hms/network/ai/z;->c:I

    iput v0, p0, Lcom/huawei/hms/network/ai/z;->d:I

    iput v0, p0, Lcom/huawei/hms/network/ai/z;->e:I

    new-instance v0, Lcom/huawei/hms/framework/common/PLSharedPreferences;

    invoke-static {}, Lcom/huawei/hms/framework/common/ContextHolder;->getResourceContext()Landroid/content/Context;

    move-result-object v1

    const-string v2, "ai_websocketpingmodel_download"

    invoke-direct {v0, v1, v2}, Lcom/huawei/hms/framework/common/PLSharedPreferences;-><init>(Landroid/content/Context;Ljava/lang/String;)V

    iput-object v0, p0, Lcom/huawei/hms/network/ai/z;->f:Lcom/huawei/hms/framework/common/PLSharedPreferences;

    iput-object p1, p0, Lcom/huawei/hms/network/ai/z;->a:Lcom/huawei/hms/network/ai/a0;

    return-void
.end method

.method private a(ILcom/huawei/hms/network/ai/x;)I
    .locals 4

    invoke-virtual {p0, p2}, Lcom/huawei/hms/network/ai/z;->c(Lcom/huawei/hms/network/ai/x;)I

    move-result v0

    const/4 v1, 0x3

    const/4 v2, 0x2

    const/4 v3, 0x1

    if-ne p1, v3, :cond_2

    invoke-virtual {p2}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result p1

    if-ne v0, v1, :cond_0

    return p1

    :cond_0
    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->b(I)I

    move-result p1

    if-ne v0, v2, :cond_1

    invoke-virtual {p2}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result p2

    add-int/2addr p1, p2

    div-int/2addr p1, v2

    :cond_1
    return p1

    :cond_2
    invoke-virtual {p2}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result p1

    if-nez p1, :cond_5

    if-ne v0, v1, :cond_3

    invoke-direct {p0, p2}, Lcom/huawei/hms/network/ai/z;->e(Lcom/huawei/hms/network/ai/x;)I

    move-result p1

    return p1

    :cond_3
    invoke-virtual {p2}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result p1

    if-ne v0, v2, :cond_4

    invoke-direct {p0, p2}, Lcom/huawei/hms/network/ai/z;->e(Lcom/huawei/hms/network/ai/x;)I

    move-result p2

    add-int/2addr p1, p2

    div-int/2addr p1, v2

    :cond_4
    return p1

    :cond_5
    if-eq v0, v1, :cond_7

    if-ne v0, v2, :cond_6

    goto :goto_0

    :cond_6
    invoke-virtual {p2}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result p1

    return p1

    :cond_7
    :goto_0
    invoke-virtual {p2}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result p1

    invoke-virtual {p0, p1}, Lcom/huawei/hms/network/ai/z;->a(I)I

    move-result p1

    return p1
.end method

.method public static synthetic a(Lcom/huawei/hms/network/ai/z;I)I
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/z;->c:I

    return p1
.end method

.method public static synthetic a(Lcom/huawei/hms/network/ai/z;)Lcom/huawei/hms/network/ai/a0;
    .locals 0

    iget-object p0, p0, Lcom/huawei/hms/network/ai/z;->a:Lcom/huawei/hms/network/ai/a0;

    return-object p0
.end method

.method private a(Ljava/util/Map;)Lcom/huawei/hms/network/ai/x;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)",
            "Lcom/huawei/hms/network/ai/x;"
        }
    .end annotation

    new-instance v0, Lcom/huawei/hms/network/ai/x;

    invoke-direct {v0}, Lcom/huawei/hms/network/ai/x;-><init>()V

    const-string v1, "mnc"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->b(Ljava/lang/String;)V

    const-string v1, "domain"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->a(Ljava/lang/String;)V

    const-string v1, "businessPing"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->a(I)V

    const-string v1, "ping"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->j(I)V

    const-string v1, "pingStatus"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->k(I)V

    const-string v1, "wifi_signal_strength"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->l(I)V

    const-string v1, "mobile_signal_strength"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->g(I)V

    const-string v1, "networkChange"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->h(I)V

    const-string v1, "pingIntervalList"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->c(Ljava/lang/String;)V

    const-string v1, "networkType"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->i(I)V

    const-string v1, "isSuccess"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->b(Z)V

    const-string v1, "isActive"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Boolean;->parseBoolean(Ljava/lang/String;)Z

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->a(Z)V

    const-string v1, "continuePing"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    if-eqz v2, :cond_0

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->b(I)V

    const-string v1, "continueTimes"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->c(I)V

    :cond_0
    const-string v1, "delayPing"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    if-eqz v2, :cond_1

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->d(I)V

    const-string v1, "delayTimes"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/x;->e(I)V

    :cond_1
    const-string v1, "firstNetworkType"

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    if-nez v2, :cond_2

    invoke-interface {p1, v1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    invoke-static {p1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result p1

    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/ai/x;->f(I)V

    :cond_2
    iget-object p1, p0, Lcom/huawei/hms/network/ai/z;->a:Lcom/huawei/hms/network/ai/a0;

    invoke-virtual {p1, v0}, Lcom/huawei/hms/network/ai/a0;->a(Lcom/huawei/hms/network/ai/x;)V

    return-object v0
.end method

.method private a(Ljava/io/InputStream;)V
    .locals 2

    if-eqz p1, :cond_0

    :try_start_0
    invoke-virtual {p1}, Ljava/io/InputStream;->close()V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    invoke-virtual {p1}, Ljava/io/IOException;->getMessage()Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x0

    aput-object p1, v0, v1

    const-string p1, "WebSocketPingModel"

    const-string v1, "ping model inputStream close error, e:%s"

    invoke-static {p1, v1, v0}, Lcom/huawei/hms/framework/common/Logger;->e(Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;)V

    :cond_0
    :goto_0
    return-void
.end method

.method private a(Ljava/lang/String;)[Ljava/lang/String;
    .locals 2

    const-string v0, " "

    const-string v1, ""

    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "["

    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "]"

    invoke-virtual {p1, v0, v1}, Ljava/lang/String;->replace(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;

    move-result-object p1

    const-string v0, ","

    invoke-virtual {p1, v0}, Ljava/lang/String;->split(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method private b(I)I
    .locals 1

    iget v0, p0, Lcom/huawei/hms/network/ai/z;->c:I

    if-eqz v0, :cond_0

    return v0

    :cond_0
    div-int/lit8 p1, p1, 0x2

    return p1
.end method

.method public static synthetic b(Lcom/huawei/hms/network/ai/z;)I
    .locals 0

    iget p0, p0, Lcom/huawei/hms/network/ai/z;->d:I

    return p0
.end method

.method public static synthetic b(Lcom/huawei/hms/network/ai/z;I)I
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/z;->d:I

    return p1
.end method

.method private b(Ljava/lang/String;)I
    .locals 1

    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->a(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    array-length v0, p1

    add-int/lit8 v0, v0, -0x1

    aget-object p1, p1, v0

    invoke-static {p1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result p1

    return p1
.end method

.method public static synthetic c(Lcom/huawei/hms/network/ai/z;I)I
    .locals 0

    iput p1, p0, Lcom/huawei/hms/network/ai/z;->e:I

    return p1
.end method

.method private d(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/network/ai/x;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->q()Z

    move-result v0

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->e()I

    move-result v1

    const/4 v2, 0x3

    if-ge v1, v2, :cond_1

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "ping"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "pingStatus"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->d()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "delayPing"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->e()I

    move-result p1

    add-int/lit8 p1, p1, 0x1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    const-string v1, "delayTimes"

    invoke-interface {v0, v1, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object v0

    :cond_1
    iget-object v0, p0, Lcom/huawei/hms/network/ai/z;->a:Lcom/huawei/hms/network/ai/a0;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result v1

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->h()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v3

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->f()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, v1, v2, v3, p1}, Lcom/huawei/hms/network/ai/a0;->a(ILjava/lang/String;ILjava/lang/String;)Ljava/util/Map;

    move-result-object p1

    return-object p1
.end method

.method private e(Lcom/huawei/hms/network/ai/x;)I
    .locals 4

    iget-object v0, p0, Lcom/huawei/hms/network/ai/z;->a:Lcom/huawei/hms/network/ai/a0;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->h()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v2

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->f()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v1, v2, v3}, Lcom/huawei/hms/network/ai/a0;->a(Ljava/lang/String;ILjava/lang/String;)I

    move-result v0

    if-eqz v0, :cond_0

    return v0

    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result p1

    return p1
.end method

.method private e()Z
    .locals 6

    iget-object v0, p0, Lcom/huawei/hms/network/ai/z;->f:Lcom/huawei/hms/framework/common/PLSharedPreferences;

    const-string v1, "lastDownloadTime"

    const-wide/16 v2, 0x0

    invoke-virtual {v0, v1, v2, v3}, Lcom/huawei/hms/framework/common/PLSharedPreferences;->getLong(Ljava/lang/String;J)J

    move-result-wide v0

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v4

    sub-long/2addr v4, v0

    const-wide/32 v0, 0x240c8400

    cmp-long v0, v4, v0

    const-string v1, "WebSocketPingModel"

    if-gtz v0, :cond_1

    cmp-long v0, v4, v2

    if-gez v0, :cond_0

    goto :goto_0

    :cond_0
    const-string v0, "ping model initPredictor < 7 days"

    invoke-static {v1, v0}, Lcom/huawei/hms/framework/common/Logger;->v(Ljava/lang/String;Ljava/lang/Object;)V

    const/4 v0, 0x0

    return v0

    :cond_1
    :goto_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "ping model initPredictor > 7 days,try to update Model "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v4, v5}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/huawei/hms/framework/common/Logger;->v(Ljava/lang/String;Ljava/lang/Object;)V

    const/4 v0, 0x1

    return v0
.end method

.method private f(Lcom/huawei/hms/network/ai/x;)I
    .locals 4

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result v0

    invoke-direct {p0, v0}, Lcom/huawei/hms/network/ai/z;->b(I)I

    move-result v0

    iget-object v1, p0, Lcom/huawei/hms/network/ai/z;->a:Lcom/huawei/hms/network/ai/a0;

    invoke-virtual {v1, p1}, Lcom/huawei/hms/network/ai/a0;->b(Lcom/huawei/hms/network/ai/x;)F

    move-result v1

    const/high16 v2, 0x40a00000    # 5.0f

    invoke-static {v2, v1}, Ljava/lang/Float;->compare(FF)I

    move-result v1

    const/4 v2, -0x1

    if-eq v1, v2, :cond_9

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->j()I

    move-result v1

    const/4 v3, 0x1

    if-eq v1, v3, :cond_9

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v1

    if-eq v1, v2, :cond_9

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v1

    if-eqz v1, :cond_9

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v1

    const/4 v2, 0x2

    if-eq v1, v2, :cond_9

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v1

    const/4 v2, 0x3

    if-ne v1, v2, :cond_0

    goto :goto_1

    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v1

    if-ne v1, v3, :cond_6

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->o()I

    move-result v1

    const/16 v2, -0x5f

    if-lt v1, v2, :cond_5

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->o()I

    move-result v1

    const/16 v2, -0x7f

    if-ne v1, v2, :cond_1

    goto :goto_0

    :cond_1
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->o()I

    move-result v1

    const/16 v2, -0x50

    const/16 v3, 0x1770

    if-ge v1, v2, :cond_2

    return v3

    :cond_2
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->m()Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0, v1}, Lcom/huawei/hms/network/ai/z;->b(Ljava/lang/String;)I

    move-result v1

    const/16 v2, 0x1f4

    if-le v1, v2, :cond_3

    return v0

    :cond_3
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->m()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/huawei/hms/network/ai/z;->b(Ljava/lang/String;)I

    move-result v0

    const/16 v1, 0x96

    if-le v0, v1, :cond_4

    return v3

    :cond_4
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result p1

    return p1

    :cond_5
    :goto_0
    return v0

    :cond_6
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->i()I

    move-result v1

    const/16 v2, -0x5a

    if-lt v1, v2, :cond_9

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->i()I

    move-result v1

    const v2, 0x7fffffff

    if-ne v1, v2, :cond_7

    goto :goto_1

    :cond_7
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->m()Ljava/lang/String;

    move-result-object v1

    invoke-direct {p0, v1}, Lcom/huawei/hms/network/ai/z;->b(Ljava/lang/String;)I

    move-result v1

    const/16 v2, 0x12c

    if-le v1, v2, :cond_8

    return v0

    :cond_8
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result p1

    return p1

    :cond_9
    :goto_1
    return v0
.end method

.method private g(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/network/ai/x;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->q()Z

    move-result v1

    if-nez v1, :cond_2

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->b()I

    move-result v1

    const-string v2, "pingStatus"

    const-string v3, "ping"

    if-eqz v1, :cond_1

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->p()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result v1

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result v4

    invoke-virtual {p0, v1, v4}, Lcom/huawei/hms/network/ai/z;->a(II)I

    move-result v1

    goto :goto_0

    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result v1

    :goto_0
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result v1

    goto :goto_1

    :cond_1
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result v1

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result v4

    invoke-virtual {p0, v1, v4}, Lcom/huawei/hms/network/ai/z;->a(II)I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v1, 0x0

    :goto_1
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    iget-object v4, p0, Lcom/huawei/hms/network/ai/z;->a:Lcom/huawei/hms/network/ai/a0;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->h()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->g()I

    move-result v6

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->f()Ljava/lang/String;

    move-result-object v7

    invoke-interface {v0, v3}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result v8

    invoke-interface {v0, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result v9

    invoke-virtual/range {v4 .. v9}, Lcom/huawei/hms/network/ai/a0;->a(Ljava/lang/String;ILjava/lang/String;II)V

    return-object v0

    :cond_2
    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->i(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;

    move-result-object p1

    return-object p1
.end method

.method private h(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/network/ai/x;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->q()Z

    move-result v1

    if-nez v1, :cond_1

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "ping"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const/4 v1, 0x1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    const-string v4, "pingStatus"

    invoke-interface {v0, v4, v3}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->j()I

    move-result v3

    if-eq v3, v1, :cond_0

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v1

    const/4 v3, -0x1

    if-eq v1, v3, :cond_0

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v1

    if-eqz v1, :cond_0

    iget-object v5, p0, Lcom/huawei/hms/network/ai/z;->a:Lcom/huawei/hms/network/ai/a0;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->h()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->g()I

    move-result v7

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->f()Ljava/lang/String;

    move-result-object v8

    invoke-interface {v0, v2}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result v9

    invoke-interface {v0, v4}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result v10

    invoke-virtual/range {v5 .. v10}, Lcom/huawei/hms/network/ai/a0;->a(Ljava/lang/String;ILjava/lang/String;II)V

    :cond_0
    return-object v0

    :cond_1
    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->i(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;

    move-result-object p1

    return-object p1
.end method

.method private i(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;
    .locals 13
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/network/ai/x;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->b()I

    move-result v1

    const-string v2, "continueTimes"

    const-string v3, "continuePing"

    const/4 v4, 0x0

    const-string v5, "ping"

    const-string v6, "pingStatus"

    const/4 v7, 0x1

    if-eqz v1, :cond_1

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->c()I

    move-result v1

    const/4 v8, 0x5

    if-ge v1, v8, :cond_0

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v5, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v6, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->b()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->c()I

    move-result v1

    add-int/2addr v1, v7

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    goto :goto_0

    :cond_0
    const/4 v1, 0x2

    invoke-direct {p0, v1, p1}, Lcom/huawei/hms/network/ai/z;->a(ILcom/huawei/hms/network/ai/x;)I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v0, v5, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result v2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v0, v6, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result v2

    if-ne v2, v7, :cond_4

    iget v2, p0, Lcom/huawei/hms/network/ai/z;->e:I

    if-ne v1, v2, :cond_3

    goto :goto_1

    :cond_1
    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->j(Lcom/huawei/hms/network/ai/x;)I

    move-result v1

    const/16 v8, 0x1770

    if-ne v1, v8, :cond_2

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result v8

    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v8

    invoke-interface {v0, v5, v8}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result v8

    invoke-static {v8}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v8

    invoke-interface {v0, v6, v8}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-static {v7}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    :goto_0
    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    :cond_2
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v0, v5, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result v2

    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    invoke-interface {v0, v6, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result v2

    if-ne v2, v7, :cond_4

    iget v2, p0, Lcom/huawei/hms/network/ai/z;->e:I

    if-ne v1, v2, :cond_3

    :goto_1
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v6, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_3
    move v4, v7

    :cond_4
    :goto_2
    if-eqz v4, :cond_5

    iget-object v7, p0, Lcom/huawei/hms/network/ai/z;->a:Lcom/huawei/hms/network/ai/a0;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->h()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->g()I

    move-result v9

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->f()Ljava/lang/String;

    move-result-object v10

    invoke-interface {v0, v5}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result v11

    invoke-interface {v0, v6}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result v12

    invoke-virtual/range {v7 .. v12}, Lcom/huawei/hms/network/ai/a0;->a(Ljava/lang/String;ILjava/lang/String;II)V

    :cond_5
    return-object v0
.end method

.method private j(Lcom/huawei/hms/network/ai/x;)I
    .locals 1

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result v0

    if-nez v0, :cond_0

    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->l(Lcom/huawei/hms/network/ai/x;)I

    move-result p1

    return p1

    :cond_0
    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->k(Lcom/huawei/hms/network/ai/x;)I

    move-result p1

    return p1
.end method

.method private k(Lcom/huawei/hms/network/ai/x;)I
    .locals 4

    iget-object v0, p0, Lcom/huawei/hms/network/ai/z;->a:Lcom/huawei/hms/network/ai/a0;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/ai/a0;->b(Lcom/huawei/hms/network/ai/x;)F

    move-result v0

    const/high16 v1, 0x40a00000    # 5.0f

    invoke-static {v1, v0}, Ljava/lang/Float;->compare(FF)I

    move-result v0

    const/4 v1, -0x1

    const/16 v2, 0x1770

    if-eq v0, v1, :cond_8

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->j()I

    move-result v0

    const/4 v3, 0x1

    if-eq v0, v3, :cond_8

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v0

    if-eq v0, v1, :cond_8

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v0

    if-nez v0, :cond_0

    goto :goto_2

    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v0

    if-ne v0, v3, :cond_4

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->o()I

    move-result v0

    const/16 v1, -0x5f

    if-lt v0, v1, :cond_3

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->o()I

    move-result v0

    const/16 v1, -0x7f

    if-ne v0, v1, :cond_1

    goto :goto_0

    :cond_1
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->m()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/huawei/hms/network/ai/z;->b(Ljava/lang/String;)I

    move-result v0

    const/16 v1, 0x96

    if-le v0, v1, :cond_2

    return v2

    :cond_2
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result p1

    invoke-virtual {p0, p1}, Lcom/huawei/hms/network/ai/z;->a(I)I

    move-result p1

    return p1

    :cond_3
    :goto_0
    return v2

    :cond_4
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->i()I

    move-result v0

    const/16 v1, -0x5a

    if-lt v0, v1, :cond_7

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->i()I

    move-result v0

    const v1, 0x7fffffff

    if-ne v0, v1, :cond_5

    goto :goto_1

    :cond_5
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->m()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/huawei/hms/network/ai/z;->b(Ljava/lang/String;)I

    move-result v0

    const/16 v1, 0x190

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result p1

    if-le v0, v1, :cond_6

    return p1

    :cond_6
    invoke-virtual {p0, p1}, Lcom/huawei/hms/network/ai/z;->a(I)I

    move-result p1

    return p1

    :cond_7
    :goto_1
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result p1

    return p1

    :cond_8
    :goto_2
    return v2
.end method

.method private l(Lcom/huawei/hms/network/ai/x;)I
    .locals 3

    iget-object v0, p0, Lcom/huawei/hms/network/ai/z;->a:Lcom/huawei/hms/network/ai/a0;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/ai/a0;->b(Lcom/huawei/hms/network/ai/x;)F

    move-result v0

    const/high16 v1, 0x40a00000    # 5.0f

    invoke-static {v1, v0}, Ljava/lang/Float;->compare(FF)I

    move-result v0

    const/4 v1, -0x1

    if-eq v0, v1, :cond_a

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->j()I

    move-result v0

    const/4 v2, 0x1

    if-eq v0, v2, :cond_a

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v0

    if-eq v0, v1, :cond_a

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v0

    if-nez v0, :cond_0

    goto/16 :goto_2

    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->k()I

    move-result v0

    if-ne v0, v2, :cond_6

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->o()I

    move-result v0

    const/16 v1, -0x5f

    if-lt v0, v1, :cond_5

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->o()I

    move-result v0

    const/16 v1, -0x7f

    if-ne v0, v1, :cond_1

    goto :goto_0

    :cond_1
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->o()I

    move-result v0

    const/16 v1, -0x50

    const/16 v2, 0x1770

    if-ge v0, v1, :cond_2

    return v2

    :cond_2
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->m()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/huawei/hms/network/ai/z;->b(Ljava/lang/String;)I

    move-result v0

    const/16 v1, 0x1f4

    if-le v0, v1, :cond_3

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result p1

    return p1

    :cond_3
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->m()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/huawei/hms/network/ai/z;->b(Ljava/lang/String;)I

    move-result v0

    const/16 v1, 0x96

    if-le v0, v1, :cond_4

    return v2

    :cond_4
    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->e(Lcom/huawei/hms/network/ai/x;)I

    move-result p1

    return p1

    :cond_5
    :goto_0
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result p1

    return p1

    :cond_6
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->i()I

    move-result v0

    const/16 v1, -0x5a

    if-lt v0, v1, :cond_9

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->i()I

    move-result v0

    const v1, 0x7fffffff

    if-ne v0, v1, :cond_7

    goto :goto_1

    :cond_7
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->m()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/huawei/hms/network/ai/z;->b(Ljava/lang/String;)I

    move-result v0

    const/16 v1, 0x190

    if-le v0, v1, :cond_8

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result p1

    return p1

    :cond_8
    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->e(Lcom/huawei/hms/network/ai/x;)I

    move-result p1

    return p1

    :cond_9
    :goto_1
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result p1

    return p1

    :cond_a
    :goto_2
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result p1

    return p1
.end method


# virtual methods
.method public a(I)I
    .locals 1

    add-int/lit16 p1, p1, 0x7530

    iget v0, p0, Lcom/huawei/hms/network/ai/z;->e:I

    if-lt p1, v0, :cond_0

    return v0

    :cond_0
    return p1
.end method

.method public a(II)I
    .locals 0

    add-int/lit16 p1, p1, -0x7530

    if-gt p1, p2, :cond_0

    return p2

    :cond_0
    return p1
.end method

.method public a(ILjava/util/Map;)Ljava/util/Map;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Lcom/huawei/hms/network/ai/z;->b:Lcom/huawei/hms/network/ai/n0;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    const-string p1, "WebSocketPingModel"

    const-string p2, "predictor is null, check local file is exists"

    invoke-static {p1, p2}, Lcom/huawei/hms/framework/common/Logger;->e(Ljava/lang/String;Ljava/lang/Object;)V

    return-object v1

    :cond_0
    if-nez p2, :cond_1

    return-object v1

    :cond_1
    invoke-direct {p0, p2}, Lcom/huawei/hms/network/ai/z;->a(Ljava/util/Map;)Lcom/huawei/hms/network/ai/x;

    move-result-object p2

    const/4 v0, 0x1

    if-ne p1, v0, :cond_2

    invoke-virtual {p0, p2}, Lcom/huawei/hms/network/ai/z;->a(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;

    move-result-object p1

    return-object p1

    :cond_2
    const/4 v0, 0x2

    if-ne p1, v0, :cond_3

    invoke-virtual {p0, p2}, Lcom/huawei/hms/network/ai/z;->b(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;

    move-result-object p1

    return-object p1

    :cond_3
    return-object v1
.end method

.method public a(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/network/ai/x;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->q()Z

    move-result v0

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "pingStatus"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result v1

    iget v2, p0, Lcom/huawei/hms/network/ai/z;->c:I

    const-string v3, "ping"

    if-gt v1, v2, :cond_1

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v0, v3, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object v0

    :cond_1
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->b()I

    move-result v1

    const-string v2, "continueTimes"

    const-string v4, "continuePing"

    const/4 v5, 0x1

    if-eqz v1, :cond_3

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->c()I

    move-result v1

    const/4 v6, 0x5

    if-ge v1, v6, :cond_2

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v3, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->b()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    invoke-interface {v0, v4, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->c()I

    move-result p1

    add-int/2addr p1, v5

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_0

    :cond_2
    invoke-direct {p0, v5, p1}, Lcom/huawei/hms/network/ai/z;->a(ILcom/huawei/hms/network/ai/x;)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_1

    :cond_3
    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->f(Lcom/huawei/hms/network/ai/x;)I

    move-result v1

    const/16 v6, 0x1770

    if-ne v1, v6, :cond_4

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->l()I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v0, v3, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    invoke-interface {v0, v4, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    :goto_0
    invoke-interface {v0, v2, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_2

    :cond_4
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    :goto_1
    invoke-interface {v0, v3, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :goto_2
    return-object v0
.end method

.method public a()V
    .locals 2

    invoke-static {}, Lcom/huawei/hms/network/ai/e;->a()Lcom/huawei/hms/network/ai/e;

    move-result-object v0

    new-instance v1, Lcom/huawei/hms/network/ai/z$a;

    invoke-direct {v1, p0}, Lcom/huawei/hms/network/ai/z$a;-><init>(Lcom/huawei/hms/network/ai/z;)V

    invoke-virtual {v0, v1}, Lcom/huawei/hms/network/ai/e;->b(Ljava/lang/Runnable;)V

    return-void
.end method

.method public a(Lcom/huawei/hms/network/ai/l;)V
    .locals 0

    return-void
.end method

.method public a(Lcom/huawei/hms/network/inner/api/RequestContext;)V
    .locals 0

    return-void
.end method

.method public b(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/huawei/hms/network/ai/x;",
            ")",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->a()I

    move-result v0

    iget v1, p0, Lcom/huawei/hms/network/ai/z;->e:I

    if-le v0, v1, :cond_0

    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iget v1, p0, Lcom/huawei/hms/network/ai/z;->e:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "ping"

    invoke-interface {v0, v2, v1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    const-string v1, "pingStatus"

    invoke-interface {v0, v1, p1}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object v0

    :cond_0
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->d()I

    move-result v0

    if-eqz v0, :cond_1

    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->d(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;

    move-result-object p1

    return-object p1

    :cond_1
    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->n()I

    move-result v0

    if-nez v0, :cond_2

    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->h(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;

    move-result-object p1

    return-object p1

    :cond_2
    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->g(Lcom/huawei/hms/network/ai/x;)Ljava/util/Map;

    move-result-object p1

    return-object p1
.end method

.method public b()V
    .locals 0

    return-void
.end method

.method public c(Lcom/huawei/hms/network/ai/x;)I
    .locals 14

    const/16 v0, 0x9

    new-array v0, v0, [F

    invoke-virtual {p1}, Lcom/huawei/hms/network/ai/x;->m()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, p1}, Lcom/huawei/hms/network/ai/z;->a(Ljava/lang/String;)[Ljava/lang/String;

    move-result-object p1

    const-wide/16 v1, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    move-wide v6, v1

    move v5, v3

    :goto_0
    const/4 v8, 0x5

    if-ge v5, v8, :cond_1

    aget-object v8, p1, v5

    invoke-static {v8}, Ljava/lang/Float;->parseFloat(Ljava/lang/String;)F

    move-result v8

    aput v8, v0, v5

    aget-object v8, p1, v5

    invoke-static {v8}, Ljava/lang/Float;->parseFloat(Ljava/lang/String;)F

    move-result v8

    float-to-double v8, v8

    add-double/2addr v6, v8

    aget v8, v0, v5

    cmpl-float v9, v8, v4

    if-lez v9, :cond_0

    move v4, v8

    :cond_0
    add-int/lit8 v5, v5, 0x1

    goto :goto_0

    :cond_1
    array-length v5, p1

    int-to-double v9, v5

    div-double/2addr v6, v9

    double-to-float v5, v6

    aput v5, v0, v8

    move-wide v8, v1

    move v5, v3

    :goto_1
    array-length v10, p1

    if-ge v5, v10, :cond_2

    aget-object v10, p1, v5

    invoke-static {v10}, Ljava/lang/Double;->parseDouble(Ljava/lang/String;)D

    move-result-wide v10

    sub-double/2addr v10, v6

    const-wide/high16 v12, 0x4000000000000000L    # 2.0

    invoke-static {v10, v11, v12, v13}, Ljava/lang/Math;->pow(DD)D

    move-result-wide v10

    add-double/2addr v8, v10

    aget-object v10, p1, v5

    invoke-static {v10}, Ljava/lang/Double;->parseDouble(Ljava/lang/String;)D

    move-result-wide v10

    sub-double/2addr v10, v6

    invoke-static {v10, v11}, Ljava/lang/Math;->abs(D)D

    move-result-wide v10

    add-double/2addr v1, v10

    add-int/lit8 v5, v5, 0x1

    goto :goto_1

    :cond_2
    array-length v5, p1

    const/4 v6, 0x1

    sub-int/2addr v5, v6

    int-to-double v10, v5

    div-double/2addr v8, v10

    double-to-float v5, v8

    const/4 v7, 0x6

    aput v5, v0, v7

    const/4 v5, 0x7

    aput v4, v0, v5

    array-length p1, p1

    int-to-double v4, p1

    div-double/2addr v1, v4

    double-to-float p1, v1

    const/16 v1, 0x8

    aput p1, v0, v1

    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "featureArray:"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {v0}, Ljava/util/Arrays;->toString([F)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v1, "WebSocketPingModel"

    invoke-static {v1, p1}, Lcom/huawei/hms/framework/common/Logger;->d(Ljava/lang/String;Ljava/lang/Object;)V

    invoke-static {v0, v3}, Lcom/huawei/hms/network/ai/m0$a;->a([FZ)Lcom/huawei/hms/network/ai/m0;

    move-result-object p1

    iget-object v0, p0, Lcom/huawei/hms/network/ai/z;->b:Lcom/huawei/hms/network/ai/n0;

    invoke-virtual {v0, p1}, Lcom/huawei/hms/network/ai/n0;->a(Lcom/huawei/hms/network/ai/m0;)[F

    move-result-object p1

    aget v0, p1, v3

    const v1, 0x3ecccccd    # 0.4f

    invoke-static {v0, v1}, Ljava/lang/Float;->compare(FF)I

    move-result v0

    const/4 v1, -0x1

    if-ne v0, v1, :cond_3

    const/4 p1, 0x3

    return p1

    :cond_3
    aget p1, p1, v3

    const v0, 0x3f4ccccd    # 0.8f

    invoke-static {p1, v0}, Ljava/lang/Float;->compare(FF)I

    move-result p1

    if-ne p1, v1, :cond_4

    const/4 p1, 0x2

    return p1

    :cond_4
    return v6
.end method

.method public c()V
    .locals 0

    return-void
.end method

.method public d()Z
    .locals 12

    const-string v0, "modelVersion"

    const-string v1, "WebSocketPingModel"

    const-string v2, "ping model initPredictor"

    invoke-static {v1, v2}, Lcom/huawei/hms/framework/common/Logger;->i(Ljava/lang/String;Ljava/lang/Object;)V

    const/4 v2, 0x0

    :try_start_0
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {}, Lcom/huawei/hms/framework/common/ContextHolder;->getAppContext()Landroid/content/Context;

    move-result-object v4

    invoke-virtual {v4}, Landroid/content/Context;->getFilesDir()Ljava/io/File;

    move-result-object v4

    invoke-virtual {v4}, Ljava/io/File;->getCanonicalPath()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v4, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, "networkkit"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v4, Ljava/io/File;->separator:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v5, "get model initPredictor path: "

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v1, v4}, Lcom/huawei/hms/framework/common/Logger;->v(Ljava/lang/String;Ljava/lang/Object;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_5

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, "Wise_ping.model"

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/huawei/hms/framework/common/CreateFileUtil;->newSafeFile(Ljava/lang/String;)Ljava/io/File;

    move-result-object v4

    invoke-virtual {v4}, Ljava/io/File;->exists()Z

    move-result v6

    const/4 v7, 0x0

    if-nez v6, :cond_2

    invoke-virtual {v4}, Ljava/io/File;->getParentFile()Ljava/io/File;

    move-result-object v0

    invoke-virtual {v0}, Ljava/io/File;->mkdirs()Z

    const-string v0, "initPredictor fail because model file not exists"

    invoke-static {v1, v0}, Lcom/huawei/hms/framework/common/Logger;->i(Ljava/lang/String;Ljava/lang/Object;)V

    iget-object v0, p0, Lcom/huawei/hms/network/ai/z;->f:Lcom/huawei/hms/framework/common/PLSharedPreferences;

    const-string v4, "modelDisable"

    const-wide/16 v8, 0x0

    invoke-virtual {v0, v4, v8, v9}, Lcom/huawei/hms/framework/common/PLSharedPreferences;->getLong(Ljava/lang/String;J)J

    move-result-wide v10

    cmp-long v0, v10, v8

    if-nez v0, :cond_0

    new-instance v0, Lcom/huawei/hms/network/ai/k0;

    invoke-direct {v0}, Lcom/huawei/hms/network/ai/k0;-><init>()V

    :goto_0
    invoke-virtual {v0, v3, v5, v7}, Lcom/huawei/hms/network/ai/k0;->a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_1

    :cond_0
    const-string v0, "initPredictor model disable"

    invoke-static {v1, v0}, Lcom/huawei/hms/framework/common/Logger;->i(Ljava/lang/String;Ljava/lang/Object;)V

    invoke-direct {p0}, Lcom/huawei/hms/network/ai/z;->e()Z

    move-result v0

    if-eqz v0, :cond_1

    new-instance v0, Lcom/huawei/hms/network/ai/k0;

    invoke-direct {v0}, Lcom/huawei/hms/network/ai/k0;-><init>()V

    goto :goto_0

    :cond_1
    :goto_1
    return v2

    :cond_2
    const/4 v4, 0x1

    :try_start_1
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-static {v6}, Lcom/huawei/hms/framework/common/CreateFileUtil;->newSafeFileInputStream(Ljava/lang/String;)Ljava/io/FileInputStream;

    move-result-object v7

    new-instance v6, Lcom/huawei/hms/network/ai/n0;

    invoke-direct {v6, v7}, Lcom/huawei/hms/network/ai/n0;-><init>(Ljava/io/InputStream;)V

    iput-object v6, p0, Lcom/huawei/hms/network/ai/z;->b:Lcom/huawei/hms/network/ai/n0;
    :try_end_1
    .catch Ljava/io/FileNotFoundException; {:try_start_1 .. :try_end_1} :catch_4
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_3
    .catch Ljava/lang/RuntimeException; {:try_start_1 .. :try_end_1} :catch_2
    .catch Ljava/lang/NoClassDefFoundError; {:try_start_1 .. :try_end_1} :catch_1
    .catch Ljava/lang/VerifyError; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    invoke-direct {p0, v7}, Lcom/huawei/hms/network/ai/z;->a(Ljava/io/InputStream;)V

    iget-object v2, p0, Lcom/huawei/hms/network/ai/z;->f:Lcom/huawei/hms/framework/common/PLSharedPreferences;

    invoke-virtual {v2, v0}, Lcom/huawei/hms/framework/common/PLSharedPreferences;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0}, Lcom/huawei/hms/network/ai/z;->e()Z

    move-result v2

    if-eqz v2, :cond_3

    new-instance v2, Lcom/huawei/hms/network/ai/k0;

    invoke-direct {v2}, Lcom/huawei/hms/network/ai/k0;-><init>()V

    invoke-virtual {v2, v3, v5, v0}, Lcom/huawei/hms/network/ai/k0;->a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    :cond_3
    move v2, v4

    goto/16 :goto_3

    :catchall_0
    move-exception v1

    goto/16 :goto_4

    :catch_0
    move-exception v6

    const-string v8, "ping model initPredictor meet verify error, e:%s"

    :try_start_2
    new-array v4, v4, [Ljava/lang/Object;

    invoke-virtual {v6}, Ljava/lang/VerifyError;->getMessage()Ljava/lang/String;

    move-result-object v6

    aput-object v6, v4, v2

    invoke-static {v1, v8, v4}, Lcom/huawei/hms/framework/common/Logger;->w(Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    invoke-direct {p0, v7}, Lcom/huawei/hms/network/ai/z;->a(Ljava/io/InputStream;)V

    iget-object v4, p0, Lcom/huawei/hms/network/ai/z;->f:Lcom/huawei/hms/framework/common/PLSharedPreferences;

    invoke-virtual {v4, v0}, Lcom/huawei/hms/framework/common/PLSharedPreferences;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0}, Lcom/huawei/hms/network/ai/z;->e()Z

    move-result v4

    if-eqz v4, :cond_4

    new-instance v4, Lcom/huawei/hms/network/ai/k0;

    invoke-direct {v4}, Lcom/huawei/hms/network/ai/k0;-><init>()V

    goto/16 :goto_2

    :catch_1
    move-exception v6

    const-string v8, "ping model initPredictor meet no class error, e:%s"

    :try_start_3
    new-array v4, v4, [Ljava/lang/Object;

    invoke-virtual {v6}, Ljava/lang/NoClassDefFoundError;->getMessage()Ljava/lang/String;

    move-result-object v6

    aput-object v6, v4, v2

    invoke-static {v1, v8, v4}, Lcom/huawei/hms/framework/common/Logger;->w(Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    invoke-direct {p0, v7}, Lcom/huawei/hms/network/ai/z;->a(Ljava/io/InputStream;)V

    iget-object v4, p0, Lcom/huawei/hms/network/ai/z;->f:Lcom/huawei/hms/framework/common/PLSharedPreferences;

    invoke-virtual {v4, v0}, Lcom/huawei/hms/framework/common/PLSharedPreferences;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0}, Lcom/huawei/hms/network/ai/z;->e()Z

    move-result v4

    if-eqz v4, :cond_4

    new-instance v4, Lcom/huawei/hms/network/ai/k0;

    invoke-direct {v4}, Lcom/huawei/hms/network/ai/k0;-><init>()V

    goto :goto_2

    :catch_2
    move-exception v6

    const-string v8, "ping model initPredictor meet runtime exception, e:%s"

    :try_start_4
    new-array v4, v4, [Ljava/lang/Object;

    invoke-virtual {v6}, Ljava/lang/RuntimeException;->getMessage()Ljava/lang/String;

    move-result-object v6

    aput-object v6, v4, v2

    invoke-static {v1, v8, v4}, Lcom/huawei/hms/framework/common/Logger;->w(Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;)V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    invoke-direct {p0, v7}, Lcom/huawei/hms/network/ai/z;->a(Ljava/io/InputStream;)V

    iget-object v4, p0, Lcom/huawei/hms/network/ai/z;->f:Lcom/huawei/hms/framework/common/PLSharedPreferences;

    invoke-virtual {v4, v0}, Lcom/huawei/hms/framework/common/PLSharedPreferences;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0}, Lcom/huawei/hms/network/ai/z;->e()Z

    move-result v4

    if-eqz v4, :cond_4

    new-instance v4, Lcom/huawei/hms/network/ai/k0;

    invoke-direct {v4}, Lcom/huawei/hms/network/ai/k0;-><init>()V

    goto :goto_2

    :catch_3
    move-exception v6

    const-string v8, "ping model initPredictor meet io exception, e:%s"

    :try_start_5
    new-array v4, v4, [Ljava/lang/Object;

    invoke-virtual {v6}, Ljava/io/IOException;->getMessage()Ljava/lang/String;

    move-result-object v6

    aput-object v6, v4, v2

    invoke-static {v1, v8, v4}, Lcom/huawei/hms/framework/common/Logger;->w(Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;)V
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_0

    invoke-direct {p0, v7}, Lcom/huawei/hms/network/ai/z;->a(Ljava/io/InputStream;)V

    iget-object v4, p0, Lcom/huawei/hms/network/ai/z;->f:Lcom/huawei/hms/framework/common/PLSharedPreferences;

    invoke-virtual {v4, v0}, Lcom/huawei/hms/framework/common/PLSharedPreferences;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0}, Lcom/huawei/hms/network/ai/z;->e()Z

    move-result v4

    if-eqz v4, :cond_4

    new-instance v4, Lcom/huawei/hms/network/ai/k0;

    invoke-direct {v4}, Lcom/huawei/hms/network/ai/k0;-><init>()V

    goto :goto_2

    :catch_4
    move-exception v6

    const-string v8, "ping model initPredictor meet model not find, e:%s"

    :try_start_6
    new-array v4, v4, [Ljava/lang/Object;

    invoke-virtual {v6}, Ljava/io/FileNotFoundException;->getMessage()Ljava/lang/String;

    move-result-object v6

    aput-object v6, v4, v2

    invoke-static {v1, v8, v4}, Lcom/huawei/hms/framework/common/Logger;->w(Ljava/lang/String;Ljava/lang/String;[Ljava/lang/Object;)V
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_0

    invoke-direct {p0, v7}, Lcom/huawei/hms/network/ai/z;->a(Ljava/io/InputStream;)V

    iget-object v4, p0, Lcom/huawei/hms/network/ai/z;->f:Lcom/huawei/hms/framework/common/PLSharedPreferences;

    invoke-virtual {v4, v0}, Lcom/huawei/hms/framework/common/PLSharedPreferences;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0}, Lcom/huawei/hms/network/ai/z;->e()Z

    move-result v4

    if-eqz v4, :cond_4

    new-instance v4, Lcom/huawei/hms/network/ai/k0;

    invoke-direct {v4}, Lcom/huawei/hms/network/ai/k0;-><init>()V

    :goto_2
    invoke-virtual {v4, v3, v5, v0}, Lcom/huawei/hms/network/ai/k0;->a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    :cond_4
    :goto_3
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "ping model initPredictor is success: "

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/huawei/hms/framework/common/Logger;->i(Ljava/lang/String;Ljava/lang/Object;)V

    return v2

    :goto_4
    invoke-direct {p0, v7}, Lcom/huawei/hms/network/ai/z;->a(Ljava/io/InputStream;)V

    iget-object v2, p0, Lcom/huawei/hms/network/ai/z;->f:Lcom/huawei/hms/framework/common/PLSharedPreferences;

    invoke-virtual {v2, v0}, Lcom/huawei/hms/framework/common/PLSharedPreferences;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-direct {p0}, Lcom/huawei/hms/network/ai/z;->e()Z

    move-result v2

    if-eqz v2, :cond_5

    new-instance v2, Lcom/huawei/hms/network/ai/k0;

    invoke-direct {v2}, Lcom/huawei/hms/network/ai/k0;-><init>()V

    invoke-virtual {v2, v3, v5, v0}, Lcom/huawei/hms/network/ai/k0;->a(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    :cond_5
    throw v1

    :catch_5
    const-string v0, "get mobile local path meet error"

    invoke-static {v1, v0}, Lcom/huawei/hms/framework/common/Logger;->w(Ljava/lang/String;Ljava/lang/Object;)V

    return v2
.end method
