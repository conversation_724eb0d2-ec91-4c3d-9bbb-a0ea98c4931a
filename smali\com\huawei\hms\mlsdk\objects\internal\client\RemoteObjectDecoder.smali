.class public Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;
.super Ljava/lang/Object;
.source "RemoteObjectDecoder.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder$Holder;
    }
.end annotation


# static fields
.field public static final TAG:Ljava/lang/String; = "RemoteObjectDecoder"

.field private static volatile lock:Ljava/lang/Object;


# instance fields
.field private initialed:Z


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ljava/lang/Object;

    invoke-direct {v0}, Ljava/lang/Object;-><init>()V

    sput-object v0, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->lock:Ljava/lang/Object;

    return-void
.end method

.method private constructor <init>()V
    .locals 1

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 3
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->initialed:Z

    return-void
.end method

.method synthetic constructor <init>(Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder$1;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;-><init>()V

    return-void
.end method

.method private static convert(Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel;
    .locals 3

    .line 1
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->readBitmap()Landroid/graphics/Bitmap;

    move-result-object p1

    .line 2
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->getByteBuffer()Ljava/nio/ByteBuffer;

    move-result-object v0

    if-nez p1, :cond_1

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v1, 0x1

    :goto_1
    const-string v2, "bitmap and byteBuffer can\'t be empty at the same time"

    .line 3
    invoke-static {v1, v2}, Lcom/huawei/hms/common/Preconditions;->checkState(ZLjava/lang/Object;)V

    .line 5
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame;->acquireProperty()Lcom/huawei/hms/mlsdk/common/MLFrame$Property;

    move-result-object p0

    .line 7
    new-instance v1, Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;

    invoke-direct {v1}, Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;-><init>()V

    if-nez v0, :cond_2

    const/4 v0, 0x0

    goto :goto_2

    .line 8
    :cond_2
    invoke-virtual {v0}, Ljava/nio/ByteBuffer;->array()[B

    move-result-object v0

    :goto_2
    invoke-virtual {v1, v0}, Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;->setBytes([B)Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;

    move-result-object v0

    .line 9
    invoke-virtual {v0, p1}, Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;->setBitmap(Landroid/graphics/Bitmap;)Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;

    move-result-object p1

    .line 10
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getWidth()I

    move-result v0

    invoke-virtual {p1, v0}, Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;->setWidth(I)Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;

    move-result-object p1

    .line 11
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getHeight()I

    move-result v0

    invoke-virtual {p1, v0}, Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;->setHeight(I)Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;

    move-result-object p1

    .line 12
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getQuadrant()I

    move-result v0

    invoke-virtual {p1, v0}, Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;->setRotation(I)Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;

    move-result-object p1

    .line 13
    invoke-virtual {p0}, Lcom/huawei/hms/mlsdk/common/MLFrame$Property;->getFormatType()I

    move-result p0

    invoke-virtual {p1, p0}, Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;->setFormat(I)Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;

    move-result-object p0

    invoke-virtual {p0}, Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel$Builder;->build()Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel;

    move-result-object p0

    return-object p0
.end method

.method public static getInstance()Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;
    .locals 1

    .line 1
    sget-object v0, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder$Holder;->INSTANCE:Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;

    return-object v0
.end method

.method private isAvailable(Landroid/content/Context;)Z
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    .line 2
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/d;->a()Lcom/huawei/hms/mlsdk/mlvision/d;

    move-result-object v1

    .line 3
    invoke-virtual {v0, p1, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->isAvailable(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)Z

    move-result p1

    return p1
.end method

.method private notifyDownloadIfNeeded(Landroid/content/Context;)V
    .locals 2

    .line 1
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    .line 2
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/d;->a()Lcom/huawei/hms/mlsdk/mlvision/d;

    move-result-object v1

    .line 3
    invoke-virtual {v0, p1, v1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->notifyDownloadIfNeeded(Landroid/content/Context;Lcom/huawei/hms/mlsdk/dynamic/IInitializer;)V

    return-void
.end method


# virtual methods
.method public declared-synchronized destroy(Landroid/content/Context;)I
    .locals 4

    monitor-enter p0

    .line 1
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/d;->a()Lcom/huawei/hms/mlsdk/mlvision/d;

    move-result-object p1

    .line 2
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    const/4 v0, -0x1

    if-nez p1, :cond_0

    monitor-exit p0

    return v0

    .line 9
    :cond_0
    :try_start_1
    check-cast p1, Lcom/huawei/hms/ml/common/object/IRemoteObjectDecoderDelegate;

    invoke-interface {p1}, Lcom/huawei/hms/ml/common/object/IRemoteObjectDecoderDelegate;->destroy()I

    move-result p1
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return p1

    :catchall_0
    move-exception p1

    .line 14
    :try_start_2
    sget-object v1, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "destroy Throwable e: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :catch_0
    move-exception p1

    .line 15
    sget-object v1, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "destroy Exception e: "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :goto_0
    monitor-exit p0

    return v0

    :catchall_1
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized detect(Landroid/content/Context;Landroid/os/Bundle;Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)Ljava/util/List;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            "Landroid/os/Bundle;",
            "Lcom/huawei/hms/mlsdk/common/MLFrame;",
            "Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;",
            ")",
            "Ljava/util/List<",
            "Lcom/huawei/hms/ml/common/object/ObjectDetectorParcel;",
            ">;"
        }
    .end annotation

    monitor-enter p0

    .line 1
    :try_start_0
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 2
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->isAvailable(Landroid/content/Context;)Z

    move-result v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez v1, :cond_0

    monitor-exit p0

    return-object v0

    .line 6
    :cond_0
    :try_start_1
    iget-boolean v1, p0, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->initialed:Z

    if-nez v1, :cond_1

    invoke-virtual {p0, p1, p4}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->initial(Landroid/content/Context;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)I

    move-result p1

    if-ltz p1, :cond_1

    const/4 p1, 0x1

    .line 7
    iput-boolean p1, p0, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->initialed:Z

    .line 10
    :cond_1
    iget-boolean p1, p0, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->initialed:Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-nez p1, :cond_2

    monitor-exit p0

    return-object v0

    .line 11
    :cond_2
    :try_start_2
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/d;->a()Lcom/huawei/hms/mlsdk/mlvision/d;

    move-result-object p1

    .line 12
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    if-nez p1, :cond_3

    monitor-exit p0

    return-object v0

    .line 19
    :cond_3
    :try_start_3
    invoke-static {p3, p4}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->convert(Lcom/huawei/hms/mlsdk/common/MLFrame;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel;

    move-result-object p3

    .line 20
    check-cast p1, Lcom/huawei/hms/ml/common/object/IRemoteObjectDecoderDelegate;

    invoke-interface {p1, p2, p3, p4}, Lcom/huawei/hms/ml/common/object/IRemoteObjectDecoderDelegate;->detect(Landroid/os/Bundle;Lcom/huawei/hms/ml/common/object/ObjectDetectorFrameParcel;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)Ljava/util/List;

    move-result-object p1
    :try_end_3
    .catch Ljava/lang/Exception; {:try_start_3 .. :try_end_3} :catch_0
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    monitor-exit p0

    return-object p1

    :catch_0
    move-exception p1

    .line 22
    :try_start_4
    sget-object p2, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->TAG:Ljava/lang/String;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string p4, "detect Exception  e: "

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized initial(Landroid/content/Context;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)I
    .locals 3

    monitor-enter p0

    .line 1
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/d;->a()Lcom/huawei/hms/mlsdk/mlvision/d;

    move-result-object p1

    .line 2
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicDelegate()Landroid/os/IInterface;

    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    const/4 v1, -0x1

    if-nez v0, :cond_0

    monitor-exit p0

    return v1

    .line 9
    :cond_0
    :try_start_1
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->getDynamicContext()Landroid/content/Context;

    move-result-object p1

    .line 10
    check-cast v0, Lcom/huawei/hms/ml/common/object/IRemoteObjectDecoderDelegate;

    invoke-static {p1}, Lcom/huawei/hms/feature/dynamic/ObjectWrapper;->wrap(Ljava/lang/Object;)Lcom/huawei/hms/feature/dynamic/IObjectWrapper;

    move-result-object p1

    invoke-interface {v0, p1, p2}, Lcom/huawei/hms/ml/common/object/IRemoteObjectDecoderDelegate;->initial(Lcom/huawei/hms/feature/dynamic/IObjectWrapper;Lcom/huawei/hms/ml/common/object/ObjectDetectorOptionsParcel;)I

    move-result p1

    if-nez p1, :cond_1

    const/4 p2, 0x1

    .line 12
    iput-boolean p2, p0, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->initialed:Z
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :cond_1
    monitor-exit p0

    return p1

    :catchall_0
    move-exception p1

    .line 18
    :try_start_2
    sget-object p2, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->TAG:Ljava/lang/String;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "initial Throwable e: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :catch_0
    move-exception p1

    .line 19
    sget-object p2, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->TAG:Ljava/lang/String;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "initial Exception e: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p2, p1}, Lcom/huawei/hms/ml/common/utils/SmartLog;->d(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :goto_0
    monitor-exit p0

    return v1

    :catchall_1
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized prepare(Landroid/content/Context;)V
    .locals 1

    monitor-enter p0

    .line 1
    :try_start_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/d;->a()Lcom/huawei/hms/mlsdk/mlvision/d;

    move-result-object v0

    .line 2
    invoke-interface {v0, p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->initial(Landroid/content/Context;)V

    .line 3
    invoke-direct {p0, p1}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->notifyDownloadIfNeeded(Landroid/content/Context;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method

.method public declared-synchronized release(Landroid/content/Context;)V
    .locals 1

    monitor-enter p0

    .line 1
    :try_start_0
    iget-boolean v0, p0, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->initialed:Z

    if-eqz v0, :cond_0

    .line 2
    invoke-virtual {p0, p1}, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->destroy(Landroid/content/Context;)I

    const/4 v0, 0x0

    .line 3
    iput-boolean v0, p0, Lcom/huawei/hms/mlsdk/objects/internal/client/RemoteObjectDecoder;->initialed:Z

    .line 6
    :cond_0
    invoke-static {}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->getInstance()Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/huawei/hms/mlsdk/internal/client/adapter/AvailableAdapterManager;->release(Landroid/content/Context;)V

    .line 7
    invoke-static {}, Lcom/huawei/hms/mlsdk/mlvision/d;->a()Lcom/huawei/hms/mlsdk/mlvision/d;

    move-result-object p1

    .line 8
    invoke-interface {p1}, Lcom/huawei/hms/mlsdk/dynamic/IInitializer;->release()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method
