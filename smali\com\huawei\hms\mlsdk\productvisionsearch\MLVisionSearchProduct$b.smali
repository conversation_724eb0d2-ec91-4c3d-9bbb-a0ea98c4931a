.class final Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct$b;
.super Ljava/lang/Object;
.source "MLVisionSearchProduct.java"

# interfaces
.implements Ljava/io/Serializable;
.implements Ljava/util/Comparator;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/io/Serializable;",
        "Ljava/util/Comparator<",
        "Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;",
        ">;"
    }
.end annotation


# direct methods
.method synthetic constructor <init>(Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProduct$a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public compare(Ljava/lang/Object;Ljava/lang/Object;)I
    .locals 0

    .line 1
    check-cast p1, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;

    check-cast p2, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;

    .line 2
    invoke-virtual {p1}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->getPossibility()F

    move-result p1

    invoke-virtual {p2}, Lcom/huawei/hms/mlsdk/productvisionsearch/MLVisionSearchProductImage;->getPossibility()F

    move-result p2

    invoke-static {p1, p2}, Ljava/lang/Float;->compare(FF)I

    move-result p1

    return p1
.end method
