.class public Lcom/huawei/hms/mlsdk/text/MLText$Block;
.super Lcom/huawei/hms/mlsdk/text/MLText$Base;
.source "MLText.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/huawei/hms/mlsdk/text/MLText;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "Block"
.end annotation


# instance fields
.field private textLineList:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/MLText$TextLine;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Landroid/graphics/Rect;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/TextLanguage;",
            ">;",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;",
            ">;[",
            "Landroid/graphics/Point;",
            "F)V"
        }
    .end annotation

    move-object v0, p0

    move-object v1, p1

    move-object v2, p2

    move-object v3, p3

    move-object v4, p5

    move v5, p6

    .line 1
    invoke-direct/range {v0 .. v5}, Lcom/huawei/hms/mlsdk/text/MLText$Base;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;[Landroid/graphics/Point;F)V

    .line 2
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/huawei/hms/mlsdk/text/MLText$Block;->textLineList:Ljava/util/List;

    .line 6
    invoke-direct {p0, p4}, Lcom/huawei/hms/mlsdk/text/MLText$Block;->initMLTextLine(Ljava/util/List;)V

    return-void
.end method

.method private initMLTextLine(Ljava/util/List;)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;

    .line 2
    iget-object v1, p0, Lcom/huawei/hms/mlsdk/text/MLText$Block;->textLineList:Ljava/util/List;

    new-instance v9, Lcom/huawei/hms/mlsdk/text/MLText$TextLine;

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getStringValue()Ljava/lang/String;

    move-result-object v3

    .line 3
    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getBorder()Landroid/graphics/Rect;

    move-result-object v4

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getLanguageList()Ljava/util/List;

    move-result-object v5

    .line 4
    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextLine;->getContents()Ljava/util/List;

    move-result-object v6

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getVertexes()[Landroid/graphics/Point;

    move-result-object v7

    invoke-virtual {v0}, Lcom/huawei/hms/mlsdk/text/entity/textplate/impl/MLTextBase;->getConfidence()Ljava/lang/Float;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Float;->floatValue()F

    move-result v8

    move-object v2, v9

    invoke-direct/range {v2 .. v8}, Lcom/huawei/hms/mlsdk/text/MLText$TextLine;-><init>(Ljava/lang/String;Landroid/graphics/Rect;Ljava/util/List;Ljava/util/List;[Landroid/graphics/Point;F)V

    .line 5
    invoke-interface {v1, v9}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    goto :goto_0

    :cond_0
    return-void
.end method


# virtual methods
.method public getContents()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lcom/huawei/hms/mlsdk/text/MLText$TextLine;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/huawei/hms/mlsdk/text/MLText$Block;->textLineList:Ljava/util/List;

    return-object v0
.end method
